using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using IdentityManager.Data;
using IdentityManager.Models;
using System.Security.Claims;

namespace IdentityManager.Middleware;

/// <summary>
/// Middleware to track user activity and update session information
/// </summary>
public class ActivityTrackingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ActivityTrackingMiddleware> _logger;

    public ActivityTrackingMiddleware(RequestDelegate next, ILogger<ActivityTrackingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, IServiceProvider serviceProvider)
    {
        // Only track activity for authenticated users
        if (context.User.Identity?.IsAuthenticated == true)
        {
            // Run activity tracking in background to avoid slowing down requests
            _ = Task.Run(async () => await TrackUserActivityAsync(context, serviceProvider));
        }

        // Continue with the request pipeline
        await _next(context);
    }

    private async Task TrackUserActivityAsync(HttpContext context, IServiceProvider serviceProvider)
    {
        try
        {
            using var scope = serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            var userId = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var sessionId = context.User.FindFirst("session_id")?.Value;

            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(sessionId))
                return;

            var now = DateTime.UtcNow;

            // Update session last activity
            var session = await dbContext.UserSessions
                .FirstOrDefaultAsync(s => s.SessionId == sessionId && s.UserId == userId);

            if (session != null && session.IsActive)
            {
                // Only update if it's been more than 1 minute since last update (to reduce DB writes)
                if (now - session.LastActivityAt > TimeSpan.FromMinutes(1))
                {
                    session.LastActivityAt = now;

                    // Update user's last activity
                    var user = await userManager.FindByIdAsync(userId);
                    if (user != null)
                    {
                        user.LastActivityAt = now;
                        await userManager.UpdateAsync(user);
                    }

                    await dbContext.SaveChangesAsync();

                    _logger.LogDebug("Updated activity for user {UserId}, session {SessionId}", userId, sessionId);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error tracking user activity");
            // Don't throw - activity tracking failure shouldn't break the request
        }
    }
}

/// <summary>
/// Extension methods for adding the activity tracking middleware
/// </summary>
public static class ActivityTrackingMiddlewareExtensions
{
    /// <summary>
    /// Adds the activity tracking middleware to the application pipeline
    /// </summary>
    /// <param name="builder">The application builder</param>
    /// <returns>The application builder for chaining</returns>
    public static IApplicationBuilder UseActivityTracking(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<ActivityTrackingMiddleware>();
    }
}
