using Microsoft.AspNetCore.Identity;
using System.Security.Claims;
using IdentityManager.Models;

namespace IdentityManager.Services;

/// <summary>
/// Service responsible for ensuring all required role claims exist in the system
/// Specifically handles claims for Operations, Supervisor, and RTM Critical roles required for Módulo Asistencia DT
/// </summary>
public class RoleClaimsSeederService
{
    private readonly RoleManager<IdentityRole> _roleManager;
    private readonly ILogger<RoleClaimsSeederService> _logger;

    /// <summary>
    /// Claims definitions for Operations, Supervisor, and RTM Critical roles (Módulo Asistencia DT)
    /// </summary>
    private static readonly Dictionary<string, List<(string Type, string Value)>> RoleClaims = new()
    {
        {
            RoleDefinitions.Roles.Operations,
            new List<(string Type, string Value)>
            {
                (RoleDefinitions.Claims.Scope, RoleDefinitions.Scopes.OperationsManage),
                (RoleDefinitions.Claims.Scope, RoleDefinitions.Scopes.ShiftsManage),
                (RoleDefinitions.Claims.Department, "operations"),
                (RoleDefinitions.Claims.Tenant, "required")
            }
        },
        {
            RoleDefinitions.Roles.Supervisor,
            new List<(string Type, string Value)>
            {
                (RoleDefinitions.Claims.Scope, RoleDefinitions.Scopes.TeamManage),
                (RoleDefinitions.Claims.Scope, RoleDefinitions.Scopes.EmployeeSupervise),
                (RoleDefinitions.Claims.Department, "supervision"),
                (RoleDefinitions.Claims.Tenant, "required")
            }
        },
        {
            RoleDefinitions.Roles.ComplianceOfficer,
            new List<(string Type, string Value)>
            {
                (RoleDefinitions.Claims.Scope, RoleDefinitions.Scopes.ComplianceManage),
                (RoleDefinitions.Claims.Scope, RoleDefinitions.Scopes.AuditLogsRead),
                (RoleDefinitions.Claims.Scope, RoleDefinitions.Scopes.ComplianceReportsGenerate),
                (RoleDefinitions.Claims.Department, "compliance"),
                (RoleDefinitions.Claims.Tenant, "required"),
                (RoleDefinitions.Claims.ComplianceLevel, "critical")
            }
        },
        {
            RoleDefinitions.Roles.LegalCounsel,
            new List<(string Type, string Value)>
            {
                (RoleDefinitions.Claims.Scope, RoleDefinitions.Scopes.LegalInterpret),
                (RoleDefinitions.Claims.Scope, RoleDefinitions.Scopes.ComplianceReportsRead),
                (RoleDefinitions.Claims.Department, "legal"),
                (RoleDefinitions.Claims.Tenant, "required"),
                (RoleDefinitions.Claims.LegalPrivilege, "attorney_client")
            }
        },
        {
            RoleDefinitions.Roles.DTRelationsSpecialist,
            new List<(string Type, string Value)>
            {
                (RoleDefinitions.Claims.Scope, RoleDefinitions.Scopes.DTRelationsManage),
                (RoleDefinitions.Claims.Scope, RoleDefinitions.Scopes.CertificationMaintain),
                (RoleDefinitions.Claims.Scope, RoleDefinitions.Scopes.RegulatoryReporting),
                (RoleDefinitions.Claims.Authority, "government_liaison"),
                (RoleDefinitions.Claims.CrossTenant, "dt_authorized"),
                (RoleDefinitions.Claims.DTAuthorized, "true")
            }
        },
        {
            RoleDefinitions.Roles.TechnicalLead,
            new List<(string Type, string Value)>
            {
                (RoleDefinitions.Claims.Scope, RoleDefinitions.Scopes.TechnicalArchitecture),
                (RoleDefinitions.Claims.Scope, RoleDefinitions.Scopes.SupportAccess),
                (RoleDefinitions.Claims.Department, "technical"),
                (RoleDefinitions.Claims.Tenant, "optional")
            }
        },
        {
            RoleDefinitions.Roles.ExternalAuditor,
            new List<(string Type, string Value)>
            {
                (RoleDefinitions.Claims.AuditAccess, "readonly"),
                (RoleDefinitions.Claims.Scope, RoleDefinitions.Scopes.AuditLogsRead),
                (RoleDefinitions.Claims.Scope, RoleDefinitions.Scopes.ComplianceReportsRead),
                (RoleDefinitions.Claims.Tenant, "required")
            }
        }
    };

    public RoleClaimsSeederService(
        RoleManager<IdentityRole> roleManager,
        ILogger<RoleClaimsSeederService> logger)
    {
        _roleManager = roleManager;
        _logger = logger;
    }

    /// <summary>
    /// Ensures all required role claims exist in the system for Operations, Supervisor, and RTM Critical roles
    /// </summary>
    /// <returns>Task representing the asynchronous operation</returns>
    public async Task EnsureRoleClaimsExistAsync()
    {
        _logger.LogInformation("Starting role claims seeding process for Módulo Asistencia DT and RTM Critical roles.");

        int createdClaims = 0;
        int existingClaimsCount = 0;

        foreach (var (roleName, claims) in RoleClaims)
        {
            try
            {
                var role = await _roleManager.FindByNameAsync(roleName);
                if (role == null)
                {
                    _logger.LogWarning("Role {RoleName} not found. Skipping claims seeding for this role.", roleName);
                    continue;
                }

                _logger.LogDebug("Processing claims for role: {RoleName}", roleName);

                foreach (var (claimType, claimValue) in claims)
                {
                    try
                    {
                        // Check if claim already exists
                        var roleClaims = await _roleManager.GetClaimsAsync(role);
                        var claimExists = roleClaims.Any(c => c.Type == claimType && c.Value == claimValue);

                        if (!claimExists)
                        {
                            var claim = new Claim(claimType, claimValue);
                            var result = await _roleManager.AddClaimAsync(role, claim);

                            if (result.Succeeded)
                            {
                                _logger.LogInformation("Successfully added claim {ClaimType}:{ClaimValue} to role {RoleName}", 
                                    claimType, claimValue, roleName);
                                createdClaims++;
                            }
                            else
                            {
                                _logger.LogError("Failed to add claim {ClaimType}:{ClaimValue} to role {RoleName}: {Errors}", 
                                    claimType, claimValue, roleName, string.Join(", ", result.Errors.Select(e => e.Description)));
                            }
                        }
                        else
                        {
                            _logger.LogDebug("Claim {ClaimType}:{ClaimValue} already exists for role {RoleName}", 
                                claimType, claimValue, roleName);
                            existingClaimsCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Unexpected error while adding claim {ClaimType}:{ClaimValue} to role {RoleName}", 
                            claimType, claimValue, roleName);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error while processing claims for role {RoleName}", roleName);
            }
        }

        _logger.LogInformation("Role claims seeding completed. Created: {CreatedCount}, Existing: {ExistingCount}", 
            createdClaims, existingClaimsCount);
    }

    /// <summary>
    /// Gets all the claims that should exist for the specified role
    /// </summary>
    /// <param name="roleName">The role name to get claims for</param>
    /// <returns>List of claims for the role, or empty list if role not found</returns>
    public static List<(string Type, string Value)> GetRequiredClaimsForRole(string roleName)
    {
        return RoleClaims.TryGetValue(roleName, out var claims) ? claims : new List<(string Type, string Value)>();
    }

    /// <summary>
    /// Validates if a claim is one of the required claims for Operations, Supervisor, or RTM Critical roles
    /// </summary>
    /// <param name="roleName">The role name</param>
    /// <param name="claimType">The claim type</param>
    /// <param name="claimValue">The claim value</param>
    /// <returns>True if the claim is a required claim for the role, false otherwise</returns>
    public static bool IsRequiredClaim(string roleName, string claimType, string claimValue)
    {
        if (!RoleClaims.TryGetValue(roleName, out var claims))
            return false;

        return claims.Any(c => c.Type == claimType && c.Value == claimValue);
    }
}
