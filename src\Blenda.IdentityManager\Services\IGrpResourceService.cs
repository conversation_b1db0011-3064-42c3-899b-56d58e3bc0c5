using IdentityManager.Models;

namespace IdentityManager.Services;

/// <summary>
/// Service interface for GRP resource synchronization operations
/// </summary>
public interface IGrpResourceService
{
    /// <summary>
    /// Synchronize user registration with GRP resource system
    /// </summary>
    /// <param name="user">Registered user instance</param>
    /// <returns>Synchronization result with tracking information</returns>
    Task<GrpSyncResult> SynchronizeUserResourceAsync(ApplicationUser user);

    /// <summary>
    /// Submit anonymous request for resource replication
    /// </summary>
    /// <param name="resource">Resource to replicate</param>
    /// <returns>Submission response with tracking ID</returns>
    Task<AnonymousRequestResponse> SubmitResourceReplicationAsync(ResourceDto resource);

    /// <summary>
    /// Generate TODO activities based on GRP resource allocation
    /// </summary>
    /// <param name="user">User for activities</param>
    /// <param name="requestId">GRP request tracking identifier</param>
    /// <returns>Generated activity list for user workflow</returns>
    Task<List<TodoActivity>> GenerateTodoActivitiesAsync(
        ApplicationUser user, 
        string requestId);
}