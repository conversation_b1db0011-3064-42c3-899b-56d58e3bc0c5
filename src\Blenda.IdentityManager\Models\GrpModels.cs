namespace IdentityManager.Models;

/// <summary>
/// Resource data transfer object for GRP API integration
/// </summary>
public class ResourceDto
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public Guid TenantId { get; set; }
    public Guid ResourceTypeId { get; set; }
    public string DisplayName { get; set; } = string.Empty;
    public string CodeName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid LifeCycleState { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public Guid? UpdatedBy { get; set; }
    public string? Definition { get; set; }
    
    // Additional properties required by GrpResourceService
    public string Type { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public Dictionary<string, object>? Properties { get; set; }
    public string Version { get; set; } = "1.0.0";
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Anonymous request submission DTO for GRP API
/// </summary>
public class SubmitAnonymousRequestDto
{
    public string SourceType { get; set; } = string.Empty;
    public string? SourceId { get; set; }
    public string OperationType { get; set; } = string.Empty;
    public object Payload { get; set; } = new();
    public int Priority { get; set; } = 3; // Default to medium priority
    
    // Additional properties required by GrpResourceService
    public Guid TenantId { get; set; }
    public ResourceDto? Resource { get; set; }
    public DateTime RequestedAt { get; set; } = DateTime.UtcNow;
    public string Source { get; set; } = "IdentityManager";
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Anonymous request submission response from GRP API
/// </summary>
public class AnonymousRequestSubmissionResponseDto
{
    public string TrackingId { get; set; } = string.Empty;
    public int EstimatedProcessingTimeSeconds { get; set; }
    public string StatusCheckUrl { get; set; } = string.Empty;
    public DateTime ReceivedAt { get; set; }
    
    // Additional properties required by GrpResourceService
    public string RequestId { get; set; } = string.Empty;
    public string Status { get; set; } = "Submitted";
    public string? Message { get; set; }
}

/// <summary>
/// GRP synchronization result model
/// </summary>
public class GrpSyncResult
{
    public string UserId { get; set; } = string.Empty;
    public Guid TenantId { get; set; }
    public string Role { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? TrackingId { get; set; }
    public string? StatusCheckUrl { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public List<TodoActivity>? TodoActivities { get; set; }
    
    // Additional properties required by GrpResourceService
    public string RequestId { get; set; } = string.Empty;
    public string OperationId { get; set; } = string.Empty;
    public DateTime SyncTimestamp { get; set; } = DateTime.UtcNow;
    public long ProcessingTimeMs { get; set; }
    public string ResourceId { get; set; } = string.Empty;
    public string Details { get; set; } = string.Empty;
}

/// <summary>
/// Anonymous request response wrapper
/// </summary>
public class AnonymousRequestResponse
{
    public bool Success { get; set; }
    public string? TrackingId { get; set; }
    public string? StatusCheckUrl { get; set; }
    public int EstimatedProcessingTime { get; set; }
    public string? ErrorMessage { get; set; }
    
    // Additional properties required by GrpResourceService
    public string RequestId { get; set; } = string.Empty;
    public string Status { get; set; } = "Processing";
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// Todo activity model for resource provisioning workflows
/// </summary>
public class TodoActivity
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid ResourceId { get; set; }
    public string UserId { get; set; } = string.Empty;
    public ActivityPriority Priority { get; set; }
    public DateTime DueDate { get; set; }
    public ActivityStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    
    // Additional properties required by GrpResourceService
    public string AssignedUserId { get; set; } = string.Empty;
    public string Category { get; set; } = "Resource";
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Activity priority enumeration
/// </summary>
public enum ActivityPriority
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

/// <summary>
/// Activity status enumeration
/// </summary>
public enum ActivityStatus
{
    Pending = 0,
    InProgress = 1,
    Completed = 2,
    Cancelled = 3
}