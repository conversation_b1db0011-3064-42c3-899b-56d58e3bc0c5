using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Azure.Identity;
using System.Text;
using System.Text.RegularExpressions;
using IdentityManager.Models;
using System.Security.Cryptography;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Graph.Models;

namespace IdentityManager.Services;

public class EmailSender : IEmailSender<ApplicationUser>
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<EmailSender> _logger;
    private readonly IMemoryCache _cache;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private GraphServiceClient? _graphClient;

    // Rate limiting
    private readonly Dictionary<string, List<DateTime>> _emailHistory = new();
    private readonly object _lockObject = new();
    private const int MaxEmailsPerHour = 10;
    private const int MaxEmailsPerDay = 50;

    public EmailSender(
        IConfiguration configuration,
        ILogger<EmailSender> logger,
        IMemoryCache cache,
        IHttpContextAccessor httpContextAccessor)
    {
        _configuration = configuration;
        _logger = logger;
        _cache = cache;
        _httpContextAccessor = httpContextAccessor;

        InitializeGraphClient();
    }

    private void InitializeGraphClient()
    {
        try
        {
            var tenantId = _configuration["AzureAd:TenantId"];
            var clientId = _configuration["AzureAd:ClientId"];
            var clientSecret = _configuration["AzureAd:ClientSecret"];

            // Validación de configuración
            if (string.IsNullOrWhiteSpace(tenantId) ||
                string.IsNullOrWhiteSpace(clientId) ||
                string.IsNullOrWhiteSpace(clientSecret))
            {
                throw new InvalidOperationException("Azure AD configuration is incomplete");
            }

            var options = new ClientSecretCredentialOptions
            {
                AuthorityHost = AzureAuthorityHosts.AzurePublicCloud,
            };

            var clientSecretCredential = new ClientSecretCredential(
                tenantId, clientId, clientSecret, options);

            _graphClient = new GraphServiceClient(clientSecretCredential);

            _logger.LogInformation("Microsoft Graph client initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize Microsoft Graph client");
            throw;
        }
    }

    public async Task SendEmailAsync(ApplicationUser user, string subject, string message)
    {
        ArgumentNullException.ThrowIfNull(user);

        if (string.IsNullOrWhiteSpace(user.Email))
        {
            throw new ArgumentException("User email cannot be empty", nameof(user));
        }

        var htmlMessage = GetGenericEmailTemplate(subject, message);
        await SendEmailAsync(user.Email, subject, htmlMessage);
    }

    public async Task SendPasswordResetCodeAsync(ApplicationUser user, string email, string resetCode)
    {
        // Validación adicional de seguridad
        if (!IsValidEmail(email))
        {
            _logger.LogWarning("Invalid email format attempted: {Email}", SanitizeForLogging(email));
            throw new ArgumentException("Invalid email format", nameof(email));
        }

        var subject = "Código de restablecimiento de contraseña";
        var message = GetPasswordResetEmailTemplate(resetCode);

        await SendEmailAsync(email, subject, message);
    }

    public async Task SendConfirmationLinkAsync(ApplicationUser user, string email, string confirmationLink)
    {
        // Comentar temporalmente para debugging
        /*
        if (!_configuration.GetValue<bool>("Development:AllowInsecureLinks", false))
        {
            if (!confirmationLink.StartsWith("https://", StringComparison.OrdinalIgnoreCase))
            {
                throw new InvalidOperationException("Confirmation links must use HTTPS in production");
            }
        }
        */

        // Validar dominio del link
        // Comentado para DESARROLLO
        /*
        if (!IsValidDomain(confirmationLink))
        {
            throw new InvalidOperationException("Invalid confirmation link domain");
        }*/


        var subject = "Confirma tu dirección de correo electrónico";
        var message = GetConfirmationEmailTemplate(confirmationLink);

        await SendEmailAsync(email, subject, message);
    }

    public async Task SendPasswordResetLinkAsync(ApplicationUser user, string email, string resetLink)
    {
        // Mismas validaciones que SendConfirmationLinkAsync
        //Comentato para DESARROLLO
        /*
        if (!_configuration.GetValue<bool>("Development:AllowInsecureLinks", false))
        {
            if (!resetLink.StartsWith("https://", StringComparison.OrdinalIgnoreCase))
            {
                throw new InvalidOperationException("Reset links must use HTTPS in production");
            }
        }

        if (!IsValidDomain(resetLink))
        {
            throw new InvalidOperationException("Invalid reset link domain");
        }*/

        var subject = "Restablecer tu contraseña";
        var message = GetPasswordResetLinkTemplate(resetLink);

        await SendEmailAsync(email, subject, message);
    }

    private async Task SendEmailAsync(string email, string subject, string htmlMessage)
    {
        // Rate limiting
        if (!CheckRateLimit(email))
        {
            _logger.LogWarning("Rate limit exceeded for email: {Email}", SanitizeForLogging(email));
            throw new InvalidOperationException("Email rate limit exceeded. Please try again later.");
        }

        // Validación de entrada
        if (!IsValidEmail(email))
        {
            throw new ArgumentException("Invalid email format", nameof(email));
        }

        // Sanitización del subject
        subject = SanitizeSubject(subject);

        // Log sin información sensible
        _logger.LogInformation("Attempting to send email to {EmailDomain}", GetEmailDomain(email));

        try
        {
            await SendEmailWithGraphAsync(email, subject, htmlMessage);

            // Registrar envío exitoso para rate limiting
            RecordEmailSent(email);
        }
        catch (Exception ex)
        {
            // Log completo para debugging
            _logger.LogError(ex, "Failed to send email to domain {EmailDomain}. Error: {ErrorMessage}", GetEmailDomain(email), ex.Message);
            throw; // Re-lanza la excepción original con todos los detalles
        }
    }

    private bool CheckRateLimit(string email)
    {
        var cacheKey = $"email_rate_{GetEmailHash(email)}";

        if (_cache.TryGetValue<int>(cacheKey, out var count))
        {
            if (count >= MaxEmailsPerHour)
            {
                return false;
            }
        }

        return true;
    }

    private void RecordEmailSent(string email)
    {
        var cacheKey = $"email_rate_{GetEmailHash(email)}";

        _cache.TryGetValue<int>(cacheKey, out var count);
        count++;

        _cache.Set(cacheKey, count, TimeSpan.FromHours(1));
    }

    private string GetEmailHash(string email)
    {
        using var sha256 = SHA256.Create();
        var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(email.ToLowerInvariant()));
        return Convert.ToBase64String(hash);
    }

    private bool IsValidEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        // Regex más estricto para emails
        var emailRegex = @"^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$";

        return Regex.IsMatch(email, emailRegex) && email.Length <= 254;
    }

    private bool IsValidDomain(string url)
    {
        try
        {
            var uri = new Uri(url);
            var allowedDomains = _configuration.GetSection("Email:AllowedDomains").Get<string[]>()
                ?? new[] { "localhost", "yourdomain.com" };

            return allowedDomains.Any(domain =>
                uri.Host.Equals(domain, StringComparison.OrdinalIgnoreCase) ||
                uri.Host.EndsWith($".{domain}", StringComparison.OrdinalIgnoreCase));
        }
        catch
        {
            return false;
        }
    }

    private string SanitizeSubject(string subject)
    {
        if (string.IsNullOrWhiteSpace(subject))
            return "Sin asunto";

        // Eliminar caracteres de control y saltos de línea
        subject = Regex.Replace(subject, @"[\r\n\x00-\x1F\x7F]", "");

        // Limitar longitud
        if (subject.Length > 200)
            subject = subject.Substring(0, 197) + "...";

        return subject;
    }

    private string SanitizeForLogging(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            return "[empty]";

        // Solo mostrar dominio para emails
        if (input.Contains('@'))
        {
            var parts = input.Split('@');
            if (parts.Length == 2)
                return $"***@{parts[1]}";
        }

        // Para otros inputs, mostrar solo primeros caracteres
        return input.Length > 4 ? $"{input.Substring(0, 3)}..." : "***";
    }

    private string GetEmailDomain(string email)
    {
        try
        {
            return email.Split('@')[1];
        }
        catch
        {
            return "unknown";
        }
    }

    private async Task SendEmailWithGraphAsync(string email, string subject, string htmlMessage)
    {
        if (_graphClient == null)
        {
            throw new InvalidOperationException("Graph client is not initialized");
        }

        try
        {
            var fromEmail = _configuration["Email:From"] ?? "<EMAIL>";
            var fromName = _configuration["Email:FromName"] ?? "Identity Manager";

            // Validar configuración
            if (!IsValidEmail(fromEmail))
            {
                throw new InvalidOperationException("Invalid sender email configuration");
            }

            var message = new Message
            {
                Subject = subject,
                Body = new ItemBody
                {
                    ContentType = BodyType.Html,
                    Content = htmlMessage
                },
                ToRecipients = new List<Recipient>
                {
                    new Recipient
                    {
                        EmailAddress = new EmailAddress
                        {
                            Address = email
                        }
                    }
                },
                From = new Recipient
                {
                    EmailAddress = new EmailAddress
                    {
                        Address = fromEmail,
                        Name = fromName
                    }
                },
                // Headers de seguridad
                InternetMessageHeaders = new List<InternetMessageHeader>
                {
                    new InternetMessageHeader
                    {
                        Name = "X-Mailer",
                        Value = "Identity Manager"
                    },
                    new InternetMessageHeader
                    {
                        Name = "X-Priority",
                        Value = "3"
                    }
                }
            };

            // Crear el cuerpo de la solicitud
            var sendMailBody = new Microsoft.Graph.Users.Item.SendMail.SendMailPostRequestBody
            {
                Message = message,
                SaveToSentItems = false
            };

            // Enviar el mensaje
            await _graphClient.Users[fromEmail]
                .SendMail
                .PostAsync(sendMailBody);

            _logger.LogInformation("Email sent successfully via Microsoft Graph to domain {Domain}",
                GetEmailDomain(email));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email via Microsoft Graph");
            throw;
        }
    }

    // Plantillas usando archivos HTML externos
    private string GetConfirmationEmailTemplate(string confirmationLink)
    {
        var template = System.IO.File.ReadAllText("confirmationtemplate.html");

        return template
            .Replace("{{HEADER_LINK}}", "https://blenda.lat")
            .Replace("{{HEADER_LINK_TEXT}}", "Hola, Somos del equipo de Blenda.")
            .Replace("{{HEADLINE}}", "¡Bienvenido a Blenda!")
            .Replace("{{BODY}}", "Para completar tu registro y activar tu cuenta, por favor confirma tu dirección de correo electrónico haciendo clic en el botón de abajo.")
            .Replace("{{CONFIRMATION_LINK}}", confirmationLink);
    }

    private string GetPasswordResetEmailTemplate(string resetCode)
    {
        var template = System.IO.File.ReadAllText("resetcodetemplate.html");

        return template
            .Replace("{{HEADER_LINK}}", "https://blenda.lat")
            .Replace("{{HEADER_LINK_TEXT}}", "Equipo de Seguridad de Blenda")
            .Replace("{{HEADLINE}}", "Restablecimiento de contraseña")
            .Replace("{{BODY}}", "Has solicitado restablecer tu contraseña. Usa el siguiente código de seguridad para completar el proceso.")
            .Replace("{{RESET_CODE}}", resetCode);
    }

    private string GetPasswordResetLinkTemplate(string resetLink)
    {
        var template = System.IO.File.ReadAllText("resetlinktemplate.html");

        return template
            .Replace("{{HEADER_LINK}}", "https://blenda.lat")
            .Replace("{{HEADER_LINK_TEXT}}", "Equipo de Seguridad de Blenda")
            .Replace("{{HEADLINE}}", "Restablecer tu contraseña")
            .Replace("{{BODY}}", "Has solicitado restablecer la contraseña de tu cuenta. Haz clic en el botón de abajo para crear una nueva contraseña.")
            .Replace("{{RESET_LINK}}", resetLink);
    }
    private string GetGenericEmailTemplate(string headline, string content)
    {
        var template = System.IO.File.ReadAllText("emailtemplate.html");

        return template
            .Replace("{{HEADER_LINK}}", "https://blenda.lat")
            .Replace("{{HEADER_LINK_TEXT}}", "Equipo de Blenda")
            .Replace("{{HEADLINE}}", headline)
            .Replace("{{CONTENT}}", content);
    }

    #region IEmailService<ApplicationUser> Implementation

    /// <inheritdoc />
    public async Task SendConfirmationEmailAsync(ApplicationUser user, string confirmationUrl)
    {
        try
        {
            var template = GetConfirmationEmailTemplate(confirmationUrl);
            await SendEmailAsync(user, "Confirma tu dirección de correo electrónico", template);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send confirmation email to {Email}", user.Email);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task SendPasswordResetEmailAsync(ApplicationUser user, string resetUrl)
    {
        try
        {
            var template = GetPasswordResetLinkTemplate(resetUrl);
            await SendEmailAsync(user, "Restablece tu contraseña", template);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send password reset email to {Email}", user.Email);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task SendPasswordResetCodeAsync(ApplicationUser user, string resetCode)
    {
        try
        {
            var template = GetPasswordResetEmailTemplate(resetCode);
            await SendEmailAsync(user, "Código de restablecimiento de contraseña", template);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send password reset code to {Email}", user.Email);
            throw;
        }
    }

    #endregion
}