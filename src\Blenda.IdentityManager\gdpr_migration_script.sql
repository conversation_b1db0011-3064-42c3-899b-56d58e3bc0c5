﻿DECLARE
V_COUNT INTEGER;
BEGIN
SELECT COUNT(TABLE_NAME) INTO V_COUNT from USER_TABLES where TABLE_NAME = '__EFMigrationsHistory';
IF V_COUNT = 0 THEN
Begin
BEGIN 
EXECUTE IMMEDIATE 'CREATE TABLE 
"__EFMigrationsHistory" (
    "MigrationId" NVARCHAR2(150) NOT NULL,
    "ProductVersion" NVARCHAR2(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
)';
END;

End;

END IF;
EXCEPTION
WHEN OTHERS THEN
    IF(SQLCODE != -942)THEN
        RAISE;
    END IF;
END;
/

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES (N'20250628054430_InitialCreate', N'9.0.8')
/

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES (N'20250629215720_InitialIdentity', N'9.0.8')
/

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES (N'20250630023356_SetIdentity', N'9.0.8')
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'AspNetUsers' 
  and column_name = 'UserName' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "AspNetUsers" MODIFY "UserName" NVARCHAR2(128) NULL';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "AspNetUsers" MODIFY "UserName" NVARCHAR2(128)';
 end if;
end;
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'AspNetRoles' 
  and column_name = 'Name' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "AspNetRoles" MODIFY "Name" NVARCHAR2(128) NULL';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "AspNetRoles" MODIFY "Name" NVARCHAR2(128)';
 end if;
end;
/

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES (N'20250807025241_Deploy20250806', N'9.0.8')
/

BEGIN 
EXECUTE IMMEDIATE 'CREATE TABLE 
"DataProtectionKeys" (
    "Id" NUMBER(10) GENERATED BY DEFAULT ON NULL AS IDENTITY NOT NULL,
    "FriendlyName" NVARCHAR2(255),
    "Xml" CLOB,
    CONSTRAINT "PK_DataProtectionKeys" PRIMARY KEY ("Id")
)';
END;
/

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES (N'20250822174022_AddDataProtectionKeys', N'9.0.8')
/

ALTER TABLE "AspNetUsers" ADD "AccountStatus" NUMBER(10) DEFAULT 1 NOT NULL
/

ALTER TABLE "AspNetUsers" ADD "Country" NVARCHAR2(100)
/

ALTER TABLE "AspNetUsers" ADD "CreatedAt" TIMESTAMP(6) DEFAULT (SYS_EXTRACT_UTC(SYSTIMESTAMP)) NOT NULL
/

ALTER TABLE "AspNetUsers" ADD "DataRetentionDays" NUMBER(10) DEFAULT 0 NOT NULL
/

ALTER TABLE "AspNetUsers" ADD "DeletionRequestedAt" TIMESTAMP(6)
/

ALTER TABLE "AspNetUsers" ADD "FailedLoginAttempts" NUMBER(10) DEFAULT 0 NOT NULL
/

ALTER TABLE "AspNetUsers" ADD "HasAcceptedPrivacyPolicy" NUMBER(1) DEFAULT 0 NOT NULL
/

ALTER TABLE "AspNetUsers" ADD "HasAcceptedTermsOfService" NUMBER(1) DEFAULT 0 NOT NULL
/

ALTER TABLE "AspNetUsers" ADD "IsEUCitizen" NUMBER(1) DEFAULT 0 NOT NULL
/

ALTER TABLE "AspNetUsers" ADD "IsMarkedForDeletion" NUMBER(1) DEFAULT 0 NOT NULL
/

ALTER TABLE "AspNetUsers" ADD "LastDataExportRequestAt" TIMESTAMP(6)
/

ALTER TABLE "AspNetUsers" ADD "LastLoginAt" TIMESTAMP(6)
/

ALTER TABLE "AspNetUsers" ADD "LastLoginIPHash" NVARCHAR2(128)
/

ALTER TABLE "AspNetUsers" ADD "LastLoginUserAgent" NVARCHAR2(1000)
/

ALTER TABLE "AspNetUsers" ADD "LawfulBasisForProcessing" NUMBER(10) DEFAULT 1 NOT NULL
/

ALTER TABLE "AspNetUsers" ADD "PrivacyPolicyAcceptedAt" TIMESTAMP(6)
/

ALTER TABLE "AspNetUsers" ADD "PrivacyPolicyVersion" NVARCHAR2(50)
/

ALTER TABLE "AspNetUsers" ADD "ScheduledDeletionDate" TIMESTAMP(6)
/

ALTER TABLE "AspNetUsers" ADD "SuspendedAt" TIMESTAMP(6)
/

ALTER TABLE "AspNetUsers" ADD "SuspensionReason" NVARCHAR2(500)
/

ALTER TABLE "AspNetUsers" ADD "TermsOfServiceAcceptedAt" TIMESTAMP(6)
/

ALTER TABLE "AspNetUsers" ADD "TermsOfServiceVersion" NVARCHAR2(50)
/

ALTER TABLE "AspNetUsers" ADD "UpdatedAt" TIMESTAMP(6)
/

BEGIN 
EXECUTE IMMEDIATE 'CREATE TABLE 
"UserConsents" (
    "Id" RAW(16) DEFAULT (SYS_GUID()) NOT NULL,
    "UserId" NVARCHAR2(450) NOT NULL,
    "ConsentType" NUMBER(10) NOT NULL,
    "Status" NUMBER(10) NOT NULL,
    "ConsentDate" TIMESTAMP(6) DEFAULT (SYS_EXTRACT_UTC(SYSTIMESTAMP)) NOT NULL,
    "ExpirationDate" TIMESTAMP(6),
    "IpAddressHash" NVARCHAR2(128),
    "UserAgent" NVARCHAR2(500),
    "PolicyVersion" NVARCHAR2(50),
    "Metadata" NCLOB,
    "ConsentSource" NVARCHAR2(100),
    "WithdrawalReason" NVARCHAR2(500),
    CONSTRAINT "PK_UserConsents" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_UserConsents_AspNetUsers_UserId" FOREIGN KEY ("UserId") REFERENCES "AspNetUsers" ("Id") ON DELETE CASCADE
)';
END;
/

BEGIN 
EXECUTE IMMEDIATE 'CREATE TABLE 
"UserPrivacySettings" (
    "Id" RAW(16) DEFAULT (SYS_GUID()) NOT NULL,
    "UserId" NVARCHAR2(450) NOT NULL,
    "AllowAnalytics" NUMBER(1) DEFAULT 0 NOT NULL,
    "AllowMarketing" NUMBER(1) DEFAULT 0 NOT NULL,
    "AllowThirdPartySharing" NUMBER(1) DEFAULT 0 NOT NULL,
    "AllowCrossBorderTransfer" NUMBER(1) DEFAULT 0 NOT NULL,
    "AllowAIProcessing" NUMBER(1) DEFAULT 0 NOT NULL,
    "PreferredDataRetentionDays" NUMBER(10) DEFAULT 2555 NOT NULL,
    "PreferredLanguage" NVARCHAR2(10) DEFAULT N''en-US'' NOT NULL,
    "NotificationFrequency" NVARCHAR2(50) DEFAULT N''Immediate'' NOT NULL,
    "LastUpdated" TIMESTAMP(6) DEFAULT (SYS_EXTRACT_UTC(SYSTIMESTAMP)) NOT NULL,
    "SettingsVersion" NVARCHAR2(50) DEFAULT N''1.0'' NOT NULL,
    CONSTRAINT "PK_UserPrivacySettings" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_UserPrivacySettings_AspNetUsers_UserId" FOREIGN KEY ("UserId") REFERENCES "AspNetUsers" ("Id") ON DELETE CASCADE
)';
END;
/

BEGIN 
EXECUTE IMMEDIATE 'CREATE TABLE 
"DataProcessingRecords" (
    "Id" RAW(16) DEFAULT (SYS_GUID()) NOT NULL,
    "UserId" NVARCHAR2(450) NOT NULL,
    "Activity" NUMBER(10) NOT NULL,
    "LawfulBasis" NUMBER(10) NOT NULL,
    "ProcessingDate" TIMESTAMP(6) DEFAULT (SYS_EXTRACT_UTC(SYSTIMESTAMP)) NOT NULL,
    "DataDescription" NCLOB NOT NULL,
    "ProcessingPurpose" NVARCHAR2(500) NOT NULL,
    "DataCategories" NVARCHAR2(500),
    "ThirdPartiesInvolved" NVARCHAR2(500),
    "RetentionDays" NUMBER(10) NOT NULL,
    "ProcessingSystem" NVARCHAR2(200) NOT NULL,
    "RelatedConsentId" RAW(16),
    "ProcessingMetadata" NCLOB,
    "DataHash" NVARCHAR2(128),
    CONSTRAINT "PK_DataProcessingRecords" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_DataProcessingRecords_AspNetUsers_UserId" FOREIGN KEY ("UserId") REFERENCES "AspNetUsers" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_DataProcessingRecords_UserConsents_RelatedConsentId" FOREIGN KEY ("RelatedConsentId") REFERENCES "UserConsents" ("Id") ON DELETE SET NULL
)';
END;
/

CREATE INDEX "IX_ApplicationUser_AccountStatus" ON "AspNetUsers" ("AccountStatus")
/

CREATE INDEX "IX_ApplicationUser_Country_IsEUCitizen" ON "AspNetUsers" ("Country", "IsEUCitizen")
/

CREATE INDEX "IX_ApplicationUser_IsEUCitizen" ON "AspNetUsers" ("IsEUCitizen")
/

CREATE INDEX "IX_ApplicationUser_IsMarkedForDeletion" ON "AspNetUsers" ("IsMarkedForDeletion")
/

CREATE INDEX "IX_ApplicationUser_ScheduledDeletionDate" ON "AspNetUsers" ("ScheduledDeletionDate")
/

CREATE INDEX "IX_DataProcessingRecord_Activity" ON "DataProcessingRecords" ("Activity")
/

CREATE INDEX "IX_DataProcessingRecord_LawfulBasis" ON "DataProcessingRecords" ("LawfulBasis")
/

CREATE INDEX "IX_DataProcessingRecord_ProcessingDate" ON "DataProcessingRecords" ("ProcessingDate")
/

CREATE INDEX "IX_DataProcessingRecord_UserId" ON "DataProcessingRecords" ("UserId")
/

CREATE INDEX "IX_DataProcessingRecord_UserId_Activity_Date" ON "DataProcessingRecords" ("UserId", "Activity", "ProcessingDate")
/

CREATE INDEX "IX_DataProcessingRecords_RelatedConsentId" ON "DataProcessingRecords" ("RelatedConsentId")
/

CREATE INDEX "IX_UserConsent_ConsentDate" ON "UserConsents" ("ConsentDate")
/

CREATE INDEX "IX_UserConsent_ExpirationDate" ON "UserConsents" ("ExpirationDate")
/

CREATE INDEX "IX_UserConsent_Status" ON "UserConsents" ("Status")
/

CREATE UNIQUE INDEX "IX_UserConsent_UserId_ConsentType" ON "UserConsents" ("UserId", "ConsentType")
/

CREATE UNIQUE INDEX "IX_UserPrivacySettings_UserId" ON "UserPrivacySettings" ("UserId")
/

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES (N'20250829085204_GDPRComplianceFeatures', N'9.0.8')
/

