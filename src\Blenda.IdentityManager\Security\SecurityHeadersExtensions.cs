using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;

namespace IdentityManager.Security
{
    /// <summary>
    /// Extensions for configuring comprehensive security headers middleware
    /// Implements OWASP security best practices and enterprise-grade header policies
    /// </summary>
    public static class SecurityHeadersExtensions
    {
        /// <summary>
        /// Adds advanced security headers middleware with environment-aware configuration
        /// </summary>
        public static IApplicationBuilder UseAdvancedSecurityHeaders(this IApplicationBuilder app, IWebHostEnvironment environment)
        {
            return app.UseMiddleware<SecurityHeadersMiddleware>(environment);
        }
    }

    /// <summary>
    /// Middleware for applying comprehensive security headers
    /// Implements security controls based on OWASP recommendations
    /// </summary>
    public class SecurityHeadersMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IWebHostEnvironment _environment;

        public SecurityHeadersMiddleware(RequestDelegate next, IWebHostEnvironment environment)
        {
            _next = next;
            _environment = environment;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // SECURITY: Apply comprehensive security headers before processing request
            ApplySecurityHeaders(context);

            await _next(context);
        }

        private void ApplySecurityHeaders(HttpContext context)
        {
            var headers = context.Response.Headers;

            // SECURITY: Fundamental security headers (always applied)
            headers["X-Content-Type-Options"] = "nosniff";
            headers["X-Frame-Options"] = "DENY";
            headers["X-XSS-Protection"] = "1; mode=block";
            headers["Referrer-Policy"] = "strict-origin-when-cross-origin";

            // SECURITY: Cross-Origin policies for enhanced isolation
            headers["Cross-Origin-Opener-Policy"] = "same-origin";
            headers["Cross-Origin-Embedder-Policy"] = "require-corp";
            headers["Cross-Origin-Resource-Policy"] = "same-site";
            headers["X-Permitted-Cross-Domain-Policies"] = "none";

            // SECURITY: Content Security Policy - comprehensive XSS protection
            var cspPolicy = BuildContentSecurityPolicy(_environment);
            headers["Content-Security-Policy"] = cspPolicy;

            // SECURITY: Permissions Policy - restrict browser feature access
            var permissionsPolicy = BuildPermissionsPolicy();
            headers["Permissions-Policy"] = permissionsPolicy;

            // SECURITY: Environment-specific headers
            if (_environment.IsProduction())
            {
                ApplyProductionSecurityHeaders(headers);
            }
            else if (_environment.IsDevelopment())
            {
                ApplyDevelopmentSecurityHeaders(headers);
            }
        }

        private static string BuildContentSecurityPolicy(IWebHostEnvironment environment)
        {
            var csp = new List<string>
            {
                "default-src 'self'",
                "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net",
                "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
                "font-src 'self' https://fonts.gstatic.com",
                "img-src 'self' data: https:",
                "connect-src 'self' https://api.blenda.lat https://identity.blenda.lat",
                "frame-ancestors 'none'",
                "base-uri 'self'",
                "form-action 'self'",
                "object-src 'none'",
                "media-src 'self'"
            };

            if (environment.IsDevelopment())
            {
                // Allow localhost connections in development
                csp[5] = "connect-src 'self' https://api.blenda.lat https://identity.blenda.lat http://localhost:* https://localhost:*";
            }

            return string.Join("; ", csp);
        }

        private static string BuildPermissionsPolicy()
        {
            var permissions = new List<string>
            {
                "geolocation=()",
                "microphone=()",
                "camera=()",
                "fullscreen=(self)",
                "payment=()",
                "usb=()",
                "accelerometer=()",
                "gyroscope=()",
                "magnetometer=()",
                "ambient-light-sensor=()",
                "autoplay=()",
                "encrypted-media=()",
                "picture-in-picture=()",
                "screen-wake-lock=()",
                "web-share=()"
            };

            return string.Join(", ", permissions);
        }

        private static void ApplyProductionSecurityHeaders(IHeaderDictionary headers)
        {
            // SECURITY: Production-only security headers
            headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains; preload";
            headers["X-Robots-Tag"] = "noindex, nofollow";
            
            // SECURITY: Additional production headers for enhanced security
            headers["Cache-Control"] = "no-store, no-cache, must-revalidate, private";
            headers["Pragma"] = "no-cache";
            headers["Expires"] = "0";
        }

        private static void ApplyDevelopmentSecurityHeaders(IHeaderDictionary headers)
        {
            // SECURITY: Development-specific headers (less restrictive for debugging)
            headers["X-Development-Mode"] = "true";
            
            // Allow more permissive caching in development
            headers["Cache-Control"] = "no-cache";
        }
    }

    /// <summary>
    /// Security header configuration options
    /// </summary>
    public class SecurityHeadersOptions
    {
        /// <summary>
        /// Enable or disable Content Security Policy
        /// </summary>
        public bool EnableCSP { get; set; } = true;

        /// <summary>
        /// Enable or disable Permissions Policy
        /// </summary>
        public bool EnablePermissionsPolicy { get; set; } = true;

        /// <summary>
        /// Enable or disable Cross-Origin policies
        /// </summary>
        public bool EnableCrossOriginPolicies { get; set; } = true;

        /// <summary>
        /// Custom CSP directives to append
        /// </summary>
        public List<string> CustomCSPDirectives { get; set; } = new();

        /// <summary>
        /// Additional allowed domains for connect-src
        /// </summary>
        public List<string> AllowedConnectDomains { get; set; } = new();
    }
}
