# Oracle Autonomous Database 23ai Audit Debug - Enhanced SecurityAuditLogs Analysis

## Overview
Comprehensive debugging and analysis of the audit logging system using SecurityAuditLogs table with Oracle Autonomous Database 23ai advanced features.

## Scope
- SecurityAuditLogs data integrity verification with Oracle 23ai JSON validation
- API audit logging flow analysis using Oracle 23ai performance insights
- Risk scoring validation with Oracle 23ai JSON functions
- Performance optimization leveraging Oracle 23ai automatic tuning
- Event correlation analysis using Oracle 23ai advanced analytics

## Instructions
When analyzing audit logs with Oracle Autonomous Database 23ai, follow this systematic approach:

### 1. Oracle 23ai Data Integrity Check with JSON Validation
```sql
-- Verify recent audit entries with Oracle 23ai JSON features
SELECT * FROM (
    SELECT 
        Id,
        EventType,
        UserId,
        Success,
        FailureReason,
        RiskScore,
        Timestamp,
        SecurityMetadata,
        -- Oracle 23ai JSON validation
        JSON_VALUE(SecurityMetadata, '$.RiskScore' RETURNING NUMBER) as ExtractedRiskScore,
        JSON_VALUE(SecurityMetadata, '$.CorrelationId' RETURNING VARCHAR2(50)) as CorrelationId,
        CASE WHEN JSON_EXISTS(SecurityMetadata, '$') THEN 'Valid' ELSE 'Invalid' END as JSONStatus
    FROM USER_IDENTITY.SecurityAuditLogs 
    ORDER BY Timestamp DESC
)
WHERE ROWNUM <= 20;

-- Oracle 23ai automatic data quality check
SELECT 
    COUNT(*) as TotalRecords,
    COUNT(CASE WHEN JSON_EXISTS(SecurityMetadata, '$') THEN 1 END) as ValidJSON,
    COUNT(CASE WHEN JSON_EXISTS(SecurityMetadata, '$.RiskScore') THEN 1 END) as HasRiskScore,
    MIN(Timestamp) as EarliestRecord,
    MAX(Timestamp) as LatestRecord
FROM USER_IDENTITY.SecurityAuditLogs;
```

### 2. Oracle 23ai Event Type Analysis with Advanced Analytics
- `API_REQUEST`: All API endpoint calls with Oracle 23ai automatic performance tracking
- `LoginEnhanced`: User authentication events with Oracle 23ai risk analysis
- `PasswordReset`: Password change operations with Oracle 23ai security validation
- `EmailConfirmation`: Email verification events with Oracle 23ai audit trail
- `GDPR_Request`: GDPR compliance operations with Oracle 23ai data governance

### 3. Oracle 23ai Risk Score Validation with JSON Functions
```sql
-- Risk score validation using Oracle 23ai JSON capabilities
SELECT 
    EventType,
    COUNT(*) as EventCount,
    MIN(JSON_VALUE(SecurityMetadata, '$.RiskScore' RETURNING NUMBER)) as MinRisk,
    MAX(JSON_VALUE(SecurityMetadata, '$.RiskScore' RETURNING NUMBER)) as MaxRisk,
    AVG(JSON_VALUE(SecurityMetadata, '$.RiskScore' RETURNING NUMBER)) as AvgRisk,
    COUNT(CASE WHEN JSON_VALUE(SecurityMetadata, '$.RiskScore' RETURNING NUMBER) < 0 
                OR JSON_VALUE(SecurityMetadata, '$.RiskScore' RETURNING NUMBER) > 10 
          THEN 1 END) as InvalidScores
FROM USER_IDENTITY.SecurityAuditLogs 
WHERE JSON_EXISTS(SecurityMetadata, '$.RiskScore')
GROUP BY EventType
ORDER BY AvgRisk DESC;
```

**Oracle 23ai Risk Score Ranges:**
- Score Range: 0-10 (0=low risk, 10=critical risk)
- Failed login attempts: 6-8 risk score
- Successful operations: 1-3 risk score
- Suspicious patterns: 8-10 risk score

### 4. Oracle 23ai Performance Metrics with Automatic Insights
```sql
-- Oracle Autonomous Database 23ai audit volume analysis with automatic optimization
SELECT 
    EventType,
    COUNT(*) as EventCount,
    AVG(JSON_VALUE(SecurityMetadata, '$.RiskScore' RETURNING NUMBER)) as AvgRiskScore,
    MIN(Timestamp) as FirstEvent,
    MAX(Timestamp) as LastEvent,
    -- Oracle 23ai automatic performance metrics
    COUNT(DISTINCT JSON_VALUE(SecurityMetadata, '$.CorrelationId' RETURNING VARCHAR2(50))) as UniqueCorrelations,
    COUNT(CASE WHEN Success = 1 THEN 1 END) as SuccessfulEvents,
### 6. Oracle 23ai Correlation Analysis with Machine Learning
```sql
-- Oracle 23ai advanced correlation analysis for security patterns
SELECT 
    JSON_VALUE(SecurityMetadata, '$.CorrelationId' RETURNING VARCHAR2(50)) as CorrelationId,
    COUNT(*) as RelatedEvents,
    COUNT(DISTINCT EventType) as EventTypes,
    COUNT(DISTINCT UserId) as UniqueUsers,
    MIN(Timestamp) as StartTime,
    MAX(Timestamp) as EndTime,
    AVG(JSON_VALUE(SecurityMetadata, '$.RiskScore' RETURNING NUMBER)) as AvgRiskScore,
    MAX(JSON_VALUE(SecurityMetadata, '$.RiskScore' RETURNING NUMBER)) as MaxRiskScore
FROM USER_IDENTITY.SecurityAuditLogs 
WHERE JSON_EXISTS(SecurityMetadata, '$.CorrelationId')
    AND Timestamp >= SYSTIMESTAMP - INTERVAL '7' DAY
GROUP BY JSON_VALUE(SecurityMetadata, '$.CorrelationId' RETURNING VARCHAR2(50))
HAVING COUNT(*) > 5  -- Multi-event sequences
ORDER BY MaxRiskScore DESC, RelatedEvents DESC;
```

### 7. Oracle 23ai Anomaly Detection
```sql
-- Oracle Autonomous Database 23ai automatic anomaly detection
WITH HourlyStats AS (
    SELECT 
        TRUNC(Timestamp, 'HH') as EventHour,
        EventType,
        COUNT(*) as EventCount,
        AVG(JSON_VALUE(SecurityMetadata, '$.RiskScore' RETURNING NUMBER)) as AvgRisk
    FROM USER_IDENTITY.SecurityAuditLogs 
    WHERE Timestamp >= SYSTIMESTAMP - INTERVAL '7' DAY
    GROUP BY TRUNC(Timestamp, 'HH'), EventType
),
EventBaseline AS (
    SELECT 
        EventType,
        AVG(EventCount) as BaselineCount,
        STDDEV(EventCount) as StdDevCount,
        AVG(AvgRisk) as BaselineRisk
    FROM HourlyStats
    GROUP BY EventType
)
SELECT 
    h.EventHour,
    h.EventType,
    h.EventCount,
    h.AvgRisk,
    b.BaselineCount,
    b.BaselineRisk,
    CASE 
        WHEN h.EventCount > (b.BaselineCount + 2 * b.StdDevCount) THEN 'HIGH_VOLUME_ANOMALY'
        WHEN h.AvgRisk > (b.BaselineRisk + 2) THEN 'HIGH_RISK_ANOMALY'
        ELSE 'NORMAL'
    END as AnomalyStatus
FROM HourlyStats h
JOIN EventBaseline b ON h.EventType = b.EventType
WHERE h.EventHour >= SYSTIMESTAMP - INTERVAL '24' HOUR
ORDER BY h.EventHour DESC, h.AvgRisk DESC;
```

## Oracle 23ai Troubleshooting

### Common Issues
1. **JSON Validation Errors**: Use Oracle 23ai JSON_EXISTS function
2. **Performance Issues**: Leverage Oracle 23ai automatic indexing
3. **Missing Correlation IDs**: Check SecurityMetadata JSON structure
4. **Risk Score Anomalies**: Validate against Oracle 23ai constraints

### Oracle 23ai Debugging Commands
```sql
-- Check Oracle 23ai JSON constraints
SELECT 
    CONSTRAINT_NAME,
    CONSTRAINT_TYPE,
    SEARCH_CONDITION
FROM USER_CONSTRAINTS
WHERE TABLE_NAME = 'SECURITYAUDITLOGS'
    AND CONSTRAINT_TYPE = 'C'; -- Check constraints

-- Verify Oracle 23ai automatic indexes for JSON paths
SELECT 
    INDEX_NAME,
    INDEX_TYPE,
    FUNCIDX_STATUS,
    STATUS
FROM USER_INDEXES
WHERE TABLE_NAME = 'SECURITYAUDITLOGS'
    AND INDEX_TYPE = 'FUNCTION-BASED NORMAL';
```

## Key Validation Points
1. **AuditService Integration**: Verify LogRequestAsync properly converts ApiAuditEntry to SecurityAuditLog
2. **IP Address Hashing**: Confirm IP addresses are hashed for privacy (starts with /jN or similar)
3. **SecurityMetadata**: Validate JSON format contains Method, Path, StatusCode, ResponseTime
4. **Risk Scoring**: Ensure proper calculation based on event type and success status
5. **User Agent Truncation**: Verify User-Agent strings are limited to 500 characters

## Common Issues
- **Missing Events**: Check if AuditService is properly registered in DI container
- **Null SecurityMetadata**: Verify JSON serialization in LogRequestAsync method
- **Invalid Risk Scores**: Check CalculateRiskScore method implementation
- **Performance Issues**: Analyze query patterns and consider indexing on Timestamp, EventType, UserId

## Resolution Steps
1. Verify database connection and table schema
2. Check AuditService configuration in Program.cs
3. Review middleware registration order
4. Test API endpoints with audit logging enabled
5. Monitor SecurityAuditLogs table for real-time entries
