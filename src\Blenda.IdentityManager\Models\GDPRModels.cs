using IdentityManager.Models;

namespace IdentityManager.Models;

/// <summary>
/// Request model for granting consent
/// </summary>
public class GrantConsentRequest
{
    public ConsentType ConsentType { get; set; }
    public string? PolicyVersion { get; set; }
    public int? ExpirationDays { get; set; }
}

/// <summary>
/// Request model for withdrawing consent
/// </summary>
public class WithdrawConsentRequest
{
    public ConsentType ConsentType { get; set; }
    public string? WithdrawalReason { get; set; }
}

/// <summary>
/// Response model for consent operations
/// </summary>
public class ConsentResponse
{
    public Guid? ConsentId { get; set; }
    public ConsentType ConsentType { get; set; }
    public ConsentStatus Status { get; set; }
    public DateTime? GrantedAt { get; set; }
    public DateTime? WithdrawnAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string? WithdrawalReason { get; set; }
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// Response model for consent status check
/// </summary>
public class ConsentStatusResponse
{
    public ConsentType ConsentType { get; set; }
    public bool HasValidConsent { get; set; }
    public DateTime CheckedAt { get; set; }
}

/// <summary>
/// Response model for all consent types status
/// </summary>
public class AllConsentStatusResponse
{
    public List<ConsentTypeStatus> ConsentStatuses { get; set; } = new();
    public DateTime CheckedAt { get; set; }
}

/// <summary>
/// Individual consent type status
/// </summary>
public class ConsentTypeStatus
{
    public ConsentType ConsentType { get; set; }
    public bool HasValidConsent { get; set; }
}

/// <summary>
/// Request model for updating privacy settings
/// </summary>
public class UpdatePrivacySettingsRequest
{
    public bool AllowAnalytics { get; set; }
    public bool AllowMarketing { get; set; }
    public bool AllowThirdPartySharing { get; set; }
    public bool AllowCrossBorderTransfer { get; set; }
    public bool AllowAIProcessing { get; set; }
    public int PreferredDataRetentionDays { get; set; } = 2555;
    public string? PreferredLanguage { get; set; }
    public string? NotificationFrequency { get; set; }
}

/// <summary>
/// Request model for data deletion
/// </summary>
public class DataDeletionRequest
{
    public int? LegalRetentionDays { get; set; }
}

/// <summary>
/// Response model for data deletion request
/// </summary>
public class DataDeletionResponse
{
    public DateTime RequestedAt { get; set; }
    public DateTime ScheduledDeletionDate { get; set; }
    public int LegalRetentionDays { get; set; }
    public string Message { get; set; } = string.Empty;
}
