using System.Text;
using Microsoft.AspNetCore.Identity;
using QRCoder;
using System.Text.Encodings.Web;
using IdentityManager.Models;
using IdentityManager.Services.Interfaces;

namespace IdentityManager.Services;

/// <summary>
/// Service for handling Two-Factor Authentication operations
/// Enterprise-grade implementation with comprehensive security controls
/// </summary>
public class TwoFactorService<TUser> : ITwoFactorService<TUser> where TUser : class
{
    private readonly UserManager<TUser> _userManager;
    private readonly ILogger<TwoFactorService<TUser>> _logger;
    private readonly IConfiguration _configuration;

    public TwoFactorService(
        UserManager<TUser> userManager,
        ILogger<TwoFactorService<TUser>> logger,
        IConfiguration configuration)
    {
        _userManager = userManager;
        _logger = logger;
        _configuration = configuration;
    }

    /// <summary>
    /// Sets up 2FA for a user and returns QR code and backup codes
    /// </summary>
    public async Task<TwoFactorSetupResponse> SetupTwoFactorAsync(TUser user)
    {
        try
        {
            // Reset the authenticator key
            await _userManager.ResetAuthenticatorKeyAsync(user);
            var authenticatorKey = await _userManager.GetAuthenticatorKeyAsync(user);

            if (string.IsNullOrEmpty(authenticatorKey))
            {
                throw new InvalidOperationException("Failed to generate authenticator key");
            }

            // Get application details from configuration
            var appName = _configuration["TwoFactorSettings:ApplicationName"] ?? "Blenda Identity";
            var issuer = _configuration["TwoFactorSettings:Issuer"] ?? "Blenda IAM";

            // Get user email
            var email = await _userManager.GetEmailAsync(user);
            if (string.IsNullOrEmpty(email))
            {
                throw new InvalidOperationException("User email is required for 2FA setup");
            }

            // Generate QR code URI
            var qrCodeUri = GenerateQrCodeUri(authenticatorKey, email, appName, issuer);

            // Generate QR code as Base64 image
            var qrCodeBase64 = GenerateQrCodeBase64(qrCodeUri);

            var userId = await _userManager.GetUserIdAsync(user);
            _logger.LogInformation("Two-factor authentication setup initiated for user {UserId}", userId);

            return new TwoFactorSetupResponse
            {
                AuthenticatorKey = FormatAuthenticatorKey(authenticatorKey),
                QrCodeBase64 = qrCodeBase64,
                QrCodeUri = qrCodeUri
            };
        }
        catch (Exception ex)
        {
            var userId = await _userManager.GetUserIdAsync(user);
            _logger.LogError(ex, "Error setting up two-factor authentication for user {UserId}", userId);
            throw new InvalidOperationException("Failed to setup two-factor authentication", ex);
        }
    }

    /// <summary>
    /// Verifies the 2FA setup and enables 2FA for the user
    /// </summary>
    public async Task<TwoFactorVerifyResponse> VerifyTwoFactorSetupAsync(TUser user, string verificationCode)
    {
        try
        {
            // Verify the code
            var isTokenValid = await _userManager.VerifyTwoFactorTokenAsync(
                user, _userManager.Options.Tokens.AuthenticatorTokenProvider, verificationCode);

            var userId = await _userManager.GetUserIdAsync(user);

            if (!isTokenValid)
            {
                _logger.LogWarning("Invalid two-factor verification code provided for user {UserId}", userId);
                return new TwoFactorVerifyResponse
                {
                    Success = false,
                    RecoveryCodes = Array.Empty<string>(),
                    Message = "Invalid verification code. Please try again."
                };
            }

            // Enable 2FA
            var result = await _userManager.SetTwoFactorEnabledAsync(user, true);
            if (!result.Succeeded)
            {
                _logger.LogError("Failed to enable two-factor authentication for user {UserId}: {Errors}",
                    userId, string.Join(", ", result.Errors.Select(e => e.Description)));
                
                return new TwoFactorVerifyResponse
                {
                    Success = false,
                    RecoveryCodes = Array.Empty<string>(),
                    Message = "Failed to enable two-factor authentication."
                };
            }

            // Generate recovery codes
            var recoveryCodes = await _userManager.GenerateNewTwoFactorRecoveryCodesAsync(user, 10);

            _logger.LogInformation("Two-factor authentication successfully enabled for user {UserId}", userId);

            return new TwoFactorVerifyResponse
            {
                Success = true,
                RecoveryCodes = recoveryCodes?.ToArray() ?? Array.Empty<string>(),
                Message = "Two-factor authentication has been enabled successfully."
            };
        }
        catch (Exception ex)
        {
            var userId = await _userManager.GetUserIdAsync(user);
            _logger.LogError(ex, "Error verifying two-factor setup for user {UserId}", userId);
            throw new InvalidOperationException("Failed to verify two-factor authentication", ex);
        }
    }

    /// <summary>
    /// Disables 2FA for a user after password verification
    /// </summary>
    public async Task<bool> DisableTwoFactorAsync(TUser user, string password)
    {
        try
        {
            // Verify password
            var isPasswordValid = await _userManager.CheckPasswordAsync(user, password);
            var userId = await _userManager.GetUserIdAsync(user);

            if (!isPasswordValid)
            {
                _logger.LogWarning("Invalid password provided when attempting to disable 2FA for user {UserId}", userId);
                return false;
            }

            // Disable 2FA
            var result = await _userManager.SetTwoFactorEnabledAsync(user, false);
            if (!result.Succeeded)
            {
                _logger.LogError("Failed to disable two-factor authentication for user {UserId}: {Errors}",
                    userId, string.Join(", ", result.Errors.Select(e => e.Description)));
                return false;
            }

            // Reset authenticator key for security
            await _userManager.ResetAuthenticatorKeyAsync(user);

            _logger.LogInformation("Two-factor authentication disabled for user {UserId}", userId);
            return true;
        }
        catch (Exception ex)
        {
            var userId = await _userManager.GetUserIdAsync(user);
            _logger.LogError(ex, "Error disabling two-factor authentication for user {UserId}", userId);
            throw new InvalidOperationException("Failed to disable two-factor authentication", ex);
        }
    }

    /// <summary>
    /// Gets the current 2FA status for a user
    /// </summary>
    public async Task<TwoFactorStatusResponse> GetTwoFactorStatusAsync(TUser user)
    {
        try
        {
            var isEnabled = await _userManager.GetTwoFactorEnabledAsync(user);
            var recoveryCodesCount = await _userManager.CountRecoveryCodesAsync(user);
            var hasAuthenticatorKey = !string.IsNullOrEmpty(await _userManager.GetAuthenticatorKeyAsync(user));

            return new TwoFactorStatusResponse
            {
                IsEnabled = isEnabled,
                RecoveryCodesCount = recoveryCodesCount,
                HasAuthenticatorKey = hasAuthenticatorKey,
                LastEnabledDate = null // Could be stored in user claims if needed
            };
        }
        catch (Exception ex)
        {
            var userId = await _userManager.GetUserIdAsync(user);
            _logger.LogError(ex, "Error getting two-factor status for user {UserId}", userId);
            throw new InvalidOperationException("Failed to get two-factor authentication status", ex);
        }
    }

    /// <summary>
    /// Regenerates recovery codes for a user
    /// </summary>
    public async Task<string[]> RegenerateRecoveryCodesAsync(TUser user, string password)
    {
        try
        {
            // Verify password
            var isPasswordValid = await _userManager.CheckPasswordAsync(user, password);
            var userId = await _userManager.GetUserIdAsync(user);

            if (!isPasswordValid)
            {
                _logger.LogWarning("Invalid password provided when attempting to regenerate recovery codes for user {UserId}", userId);
                throw new UnauthorizedAccessException("Invalid password");
            }

            // Verify 2FA is enabled
            var isTwoFactorEnabled = await _userManager.GetTwoFactorEnabledAsync(user);
            if (!isTwoFactorEnabled)
            {
                throw new InvalidOperationException("Two-factor authentication is not enabled");
            }

            // Generate new recovery codes
            var recoveryCodes = await _userManager.GenerateNewTwoFactorRecoveryCodesAsync(user, 10);

            _logger.LogInformation("Recovery codes regenerated for user {UserId}", userId);

            return recoveryCodes?.ToArray() ?? Array.Empty<string>();
        }
        catch (Exception ex) when (!(ex is UnauthorizedAccessException || ex is InvalidOperationException))
        {
            var userId = await _userManager.GetUserIdAsync(user);
            _logger.LogError(ex, "Error regenerating recovery codes for user {UserId}", userId);
            throw new InvalidOperationException("Failed to regenerate recovery codes", ex);
        }
    }

    /// <summary>
    /// Verifies a 2FA token for login
    /// </summary>
    public async Task<bool> VerifyTwoFactorTokenAsync(TUser user, string token)
    {
        try
        {
            var isValid = await _userManager.VerifyTwoFactorTokenAsync(
                user, _userManager.Options.Tokens.AuthenticatorTokenProvider, token);

            var userId = await _userManager.GetUserIdAsync(user);

            if (isValid)
            {
                _logger.LogInformation("Valid two-factor token provided for user {UserId}", userId);
            }
            else
            {
                _logger.LogWarning("Invalid two-factor token provided for user {UserId}", userId);
            }

            return isValid;
        }
        catch (Exception ex)
        {
            var userId = await _userManager.GetUserIdAsync(user);
            _logger.LogError(ex, "Error verifying two-factor token for user {UserId}", userId);
            return false;
        }
    }

    /// <summary>
    /// Verifies a recovery code for login
    /// </summary>
    public async Task<bool> VerifyRecoveryCodeAsync(TUser user, string recoveryCode)
    {
        try
        {
            var result = await _userManager.RedeemTwoFactorRecoveryCodeAsync(user, recoveryCode);
            var userId = await _userManager.GetUserIdAsync(user);

            if (result.Succeeded)
            {
                _logger.LogInformation("Valid recovery code used for user {UserId}", userId);
            }
            else
            {
                _logger.LogWarning("Invalid recovery code provided for user {UserId}", userId);
            }

            return result.Succeeded;
        }
        catch (Exception ex)
        {
            var userId = await _userManager.GetUserIdAsync(user);
            _logger.LogError(ex, "Error verifying recovery code for user {UserId}", userId);
            return false;
        }
    }

    #region Private Helper Methods

    private static string GenerateQrCodeUri(string authenticatorKey, string email, string appName, string issuer)
    {
        const string authenticatorUriFormat = "otpauth://totp/{0}:{1}?secret={2}&issuer={0}&digits=6";

        return string.Format(
            authenticatorUriFormat,
            UrlEncoder.Default.Encode(issuer),
            UrlEncoder.Default.Encode(email),
            authenticatorKey);
    }

    private static string GenerateQrCodeBase64(string qrCodeUri)
    {
        using var qrGenerator = new QRCodeGenerator();
        using var qrCodeData = qrGenerator.CreateQrCode(qrCodeUri, QRCodeGenerator.ECCLevel.Q);
        using var qrCode = new PngByteQRCode(qrCodeData);
        
        var qrCodeBytes = qrCode.GetGraphic(4);
        return Convert.ToBase64String(qrCodeBytes);
    }

    private static string FormatAuthenticatorKey(string authenticatorKey)
    {
        // Format the key in groups of 4 characters for better readability
        var formattedKey = new StringBuilder();
        var currentPosition = 0;
        
        while (currentPosition + 4 < authenticatorKey.Length)
        {
            formattedKey.Append(authenticatorKey.AsSpan(currentPosition, 4)).Append(' ');
            currentPosition += 4;
        }
        
        if (currentPosition < authenticatorKey.Length)
        {
            formattedKey.Append(authenticatorKey.AsSpan(currentPosition));
        }

        return formattedKey.ToString().Trim();
    }

    /// <inheritdoc />
    public async Task<bool> ValidateTwoFactorCodeAsync(TUser user, string code)
    {
        try
        {
            _logger.LogInformation("Validating 2FA code for user {UserId}", await _userManager.GetUserIdAsync(user));
            
            // Validate the authenticator code
            var isValid = await _userManager.VerifyTwoFactorTokenAsync(user, _userManager.Options.Tokens.AuthenticatorTokenProvider, code);
            
            if (isValid)
            {
                _logger.LogInformation("2FA code validation successful for user {UserId}", await _userManager.GetUserIdAsync(user));
            }
            else
            {
                _logger.LogWarning("2FA code validation failed for user {UserId}", await _userManager.GetUserIdAsync(user));
            }
            
            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating 2FA code for user {UserId}", await _userManager.GetUserIdAsync(user));
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> ValidateRecoveryCodeAsync(TUser user, string recoveryCode)
    {
        try
        {
            _logger.LogInformation("Validating recovery code for user {UserId}", await _userManager.GetUserIdAsync(user));
            
            // Validate the recovery code
            var result = await _userManager.RedeemTwoFactorRecoveryCodeAsync(user, recoveryCode);
            
            if (result.Succeeded)
            {
                _logger.LogInformation("Recovery code validation successful for user {UserId}", await _userManager.GetUserIdAsync(user));
                return true;
            }
            else
            {
                _logger.LogWarning("Recovery code validation failed for user {UserId}: {Errors}", 
                    await _userManager.GetUserIdAsync(user), 
                    string.Join(", ", result.Errors.Select(e => e.Description)));
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating recovery code for user {UserId}", await _userManager.GetUserIdAsync(user));
            return false;
        }
    }

    #endregion
}
