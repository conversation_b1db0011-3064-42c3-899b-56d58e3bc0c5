using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Oracle.ManagedDataAccess.Client;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace IdentityManager.Data
{
    /// <summary>
    /// Custom execution strategy for Oracle that handles transient failures
    /// including ORA-12537 (TNS connection closed) and other network-related errors
    /// </summary>
    public class OracleRetryingExecutionStrategy : ExecutionStrategy
    {
        private readonly List<int> _oracleErrorNumbers;

        public OracleRetryingExecutionStrategy(
            DbContext context, 
            int maxRetryCount = 3, 
            TimeSpan? maxRetryDelay = null) 
            : base(context, maxRetryCount, maxRetryDelay ?? TimeSpan.FromSeconds(30))
        {
            // Oracle error numbers that should trigger a retry
            _oracleErrorNumbers = new List<int>
            {
                12537, // TNS:connection closed
                12541, // TNS:no listener
                12545, // Connect failed because target host or object does not exist
                12170, // TNS:Connect timeout occurred
                12535, // TNS:operation timed out
                3114,  // Not connected to Oracle
                1033,  // Oracle initialization or shutdown in progress
                1034,  // Oracle not available
                1089,  // Immediate shutdown in progress
                17002  // IO Exception: Connection reset
            };
        }

        public OracleRetryingExecutionStrategy(
            ExecutionStrategyDependencies dependencies, 
            int maxRetryCount = 3, 
            TimeSpan? maxRetryDelay = null) 
            : base(dependencies, maxRetryCount, maxRetryDelay ?? TimeSpan.FromSeconds(30))
        {
            // Oracle error numbers that should trigger a retry
            _oracleErrorNumbers = new List<int>
            {
                12537, // TNS:connection closed
                12541, // TNS:no listener
                12545, // Connect failed because target host or object does not exist
                12170, // TNS:Connect timeout occurred
                12535, // TNS:operation timed out
                3114,  // Not connected to Oracle
                1033,  // Oracle initialization or shutdown in progress
                1034,  // Oracle not available
                1089,  // Immediate shutdown in progress
                17002  // IO Exception: Connection reset
            };
        }

        protected override bool ShouldRetryOn(Exception exception)
        {
            if (exception is OracleException oracleException)
            {
                return _oracleErrorNumbers.Contains(oracleException.Number);
            }

            // Also retry on general network exceptions
            if (exception is System.Net.Sockets.SocketException ||
                exception is System.TimeoutException ||
                exception.Message.Contains("TNS", StringComparison.OrdinalIgnoreCase) ||
                exception.Message.Contains("connection closed", StringComparison.OrdinalIgnoreCase) ||
                exception.Message.Contains("network", StringComparison.OrdinalIgnoreCase))
            {
                return true;
            }

            return false;
        }

        protected override TimeSpan? GetNextDelay(Exception lastException)
        {
            var baseDelay = base.GetNextDelay(lastException);
            if (baseDelay == null)
                return null;

            // Add jitter to prevent thundering herd
            var jitter = TimeSpan.FromMilliseconds(Random.Shared.Next(0, 1000));
            return baseDelay.Value.Add(jitter);
        }
    }
}
