using IdentityManager.Data;
using IdentityManager.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text;

namespace IdentityManager.Services
{
    /// <summary>
    /// GDPR Compliance Service for comprehensive data protection and user consent management
    /// Implements GDPR Articles 6, 7, 12-22 requirements for identity systems
    /// </summary>
    public class GDPRComplianceService : IGDPRComplianceService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<GDPRComplianceService> _logger;

        public GDPRComplianceService(ApplicationDbContext context, ILogger<GDPRComplianceService> logger)
        {
            _context = context;
            _logger = logger;
        }

        // =============================================================================
        // CONSENT MANAGEMENT (GDPR Articles 6-7)
        // =============================================================================

        /// <summary>
        /// GDPR Article 7: Record user consent with comprehensive audit trail
        /// </summary>
        /// <param name="userId">User identifier</param>
        /// <param name="consentType">Type of consent being granted</param>
        /// <param name="ipAddress">User's IP address for audit purposes</param>
        /// <param name="userAgent">User's browser for audit purposes</param>
        /// <param name="policyVersion">Version of policy consented to</param>
        /// <param name="consentSource">Source of consent (web, mobile, etc.)</param>
        /// <param name="expirationDays">Days until consent expires (optional)</param>
        /// <returns>Consent record ID</returns>
        public async Task<Guid> RecordConsentAsync(
            string userId, 
            ConsentType consentType, 
            string? ipAddress = null,
            string? userAgent = null,
            string? policyVersion = null,
            string? consentSource = "web",
            int? expirationDays = null)
        {
            try
            {
                // Hash IP address for privacy protection
                var ipHash = !string.IsNullOrEmpty(ipAddress) ? HashIPAddress(ipAddress) : null;

                // Check for existing consent
                var existingConsent = await _context.UserConsents
                    .FirstOrDefaultAsync(c => c.UserId == userId && c.ConsentType == consentType);

                if (existingConsent != null)
                {
                    // Update existing consent
                    existingConsent.Status = ConsentStatus.Granted;
                    existingConsent.ConsentDate = DateTime.UtcNow;
                    existingConsent.ExpirationDate = expirationDays.HasValue 
                        ? DateTime.UtcNow.AddDays(expirationDays.Value) : null;
                    existingConsent.IpAddressHash = ipHash;
                    existingConsent.UserAgent = userAgent?.Substring(0, Math.Min(userAgent.Length, 500));
                    existingConsent.PolicyVersion = policyVersion;
                    existingConsent.ConsentSource = consentSource;

                    await LogDataProcessingActivity(userId, DataProcessingActivity.ProfileUpdate, 
                        ProcessingLawfulBasis.Consent, "Updated user consent record",
                        "User consent preferences", existingConsent.Id);

                    await _context.SaveChangesAsync();
                    
                    _logger.LogInformation("Updated consent for user {UserId}, type {ConsentType}", 
                        userId, consentType);
                    
                    return existingConsent.Id;
                }

                // Create new consent record
                var consent = new UserConsent
                {
                    UserId = userId,
                    ConsentType = consentType,
                    Status = ConsentStatus.Granted,
                    ConsentDate = DateTime.UtcNow,
                    ExpirationDate = expirationDays.HasValue 
                        ? DateTime.UtcNow.AddDays(expirationDays.Value) : null,
                    IpAddressHash = ipHash,
                    UserAgent = userAgent?.Substring(0, Math.Min(userAgent?.Length ?? 0, 500)),
                    PolicyVersion = policyVersion,
                    ConsentSource = consentSource,
                    Metadata = $"{{\"timestamp\":\"{DateTime.UtcNow:O}\",\"version\":\"1.0\"}}"
                };

                _context.UserConsents.Add(consent);
                await _context.SaveChangesAsync();

                // Log data processing activity
                await LogDataProcessingActivity(userId, DataProcessingActivity.ProfileUpdate, 
                    ProcessingLawfulBasis.Consent, "Recorded new user consent",
                    "User consent preferences", consent.Id);

                _logger.LogInformation("Recorded new consent for user {UserId}, type {ConsentType}, ID {ConsentId}", 
                    userId, consentType, consent.Id);

                return consent.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to record consent for user {UserId}, type {ConsentType}", 
                    userId, consentType);
                throw;
            }
        }

        /// <summary>
        /// GDPR Article 7: Withdraw user consent with audit trail
        /// </summary>
        /// <param name="userId">User identifier</param>
        /// <param name="consentType">Type of consent being withdrawn</param>
        /// <param name="withdrawalReason">Reason for withdrawal (optional)</param>
        /// <returns>True if consent was successfully withdrawn</returns>
        public async Task<bool> WithdrawConsentAsync(string userId, ConsentType consentType, string? withdrawalReason = null)
        {
            try
            {
                var consent = await _context.UserConsents
                    .FirstOrDefaultAsync(c => c.UserId == userId && c.ConsentType == consentType);

                if (consent == null || consent.Status == ConsentStatus.Withdrawn)
                {
                    _logger.LogWarning("No active consent found to withdraw for user {UserId}, type {ConsentType}", 
                        userId, consentType);
                    return false;
                }

                consent.Status = ConsentStatus.Withdrawn;
                consent.ConsentDate = DateTime.UtcNow; // Update to withdrawal date
                consent.WithdrawalReason = withdrawalReason?.Substring(0, Math.Min(withdrawalReason.Length, 500));

                await LogDataProcessingActivity(userId, DataProcessingActivity.ProfileUpdate, 
                    ProcessingLawfulBasis.Consent, "User withdrew consent",
                    "User consent preferences", consent.Id);

                await _context.SaveChangesAsync();

                _logger.LogInformation("Withdrew consent for user {UserId}, type {ConsentType}, reason: {Reason}", 
                    userId, consentType, withdrawalReason ?? "Not specified");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to withdraw consent for user {UserId}, type {ConsentType}", 
                    userId, consentType);
                throw;
            }
        }

        /// <summary>
        /// Check if user has valid consent for specific type
        /// </summary>
        /// <param name="userId">User identifier</param>
        /// <param name="consentType">Type of consent to check</param>
        /// <returns>True if user has valid, non-expired consent</returns>
        public async Task<bool> HasValidConsentAsync(string userId, ConsentType consentType)
        {
            try
            {
                var consent = await _context.UserConsents
                    .FirstOrDefaultAsync(c => c.UserId == userId && c.ConsentType == consentType);

                if (consent == null || consent.Status != ConsentStatus.Granted)
                    return false;

                // Check expiration
                if (consent.ExpirationDate.HasValue && consent.ExpirationDate.Value < DateTime.UtcNow)
                {
                    // Mark as expired
                    consent.Status = ConsentStatus.Expired;
                    await _context.SaveChangesAsync();
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to check consent for user {UserId}, type {ConsentType}", 
                    userId, consentType);
                return false; // Default to no consent on error for security
            }
        }

        // =============================================================================
        // PRIVACY SETTINGS MANAGEMENT
        // =============================================================================

        /// <summary>
        /// Initialize default privacy settings for new user
        /// </summary>
        /// <param name="userId">User identifier</param>
        /// <param name="isEUCitizen">Whether user is EU citizen</param>
        /// <returns>Created privacy settings</returns>
        public async Task<UserPrivacySettings> InitializePrivacySettingsAsync(string userId, bool isEUCitizen = false)
        {
            try
            {
                var existingSettings = await _context.UserPrivacySettings
                    .FirstOrDefaultAsync(p => p.UserId == userId);

                if (existingSettings != null)
                {
                    _logger.LogWarning("Privacy settings already exist for user {UserId}", userId);
                    return existingSettings;
                }

                var settings = new UserPrivacySettings
                {
                    UserId = userId,
                    AllowAnalytics = false, // Default to most restrictive for GDPR compliance
                    AllowMarketing = false,
                    AllowThirdPartySharing = false,
                    AllowCrossBorderTransfer = !isEUCitizen, // Restrict for EU citizens by default
                    AllowAIProcessing = false,
                    PreferredDataRetentionDays = 2555, // 7 years default
                    PreferredLanguage = "es-CL",
                    NotificationFrequency = "Immediate",
                    SettingsVersion = "1.0"
                };

                _context.UserPrivacySettings.Add(settings);
                await _context.SaveChangesAsync();

                await LogDataProcessingActivity(userId, DataProcessingActivity.AccountCreation, 
                    ProcessingLawfulBasis.Consent, "Initialized privacy settings",
                    "Privacy preferences, consent settings");

                _logger.LogInformation("Initialized privacy settings for user {UserId}", userId);
                return settings;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize privacy settings for user {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// Update user privacy settings
        /// </summary>
        /// <param name="userId">User identifier</param>
        /// <param name="settings">Updated privacy settings</param>
        /// <returns>True if successfully updated</returns>
        public async Task<bool> UpdatePrivacySettingsAsync(string userId, UserPrivacySettings settings)
        {
            try
            {
                var existingSettings = await _context.UserPrivacySettings
                    .FirstOrDefaultAsync(p => p.UserId == userId);

                if (existingSettings == null)
                {
                    _logger.LogWarning("No privacy settings found for user {UserId}", userId);
                    return false;
                }

                // Update settings
                existingSettings.AllowAnalytics = settings.AllowAnalytics;
                existingSettings.AllowMarketing = settings.AllowMarketing;
                existingSettings.AllowThirdPartySharing = settings.AllowThirdPartySharing;
                existingSettings.AllowCrossBorderTransfer = settings.AllowCrossBorderTransfer;
                existingSettings.AllowAIProcessing = settings.AllowAIProcessing;
                existingSettings.PreferredDataRetentionDays = settings.PreferredDataRetentionDays;
                existingSettings.PreferredLanguage = settings.PreferredLanguage;
                existingSettings.NotificationFrequency = settings.NotificationFrequency;
                existingSettings.LastUpdated = DateTime.UtcNow;

                await LogDataProcessingActivity(userId, DataProcessingActivity.ProfileUpdate, 
                    ProcessingLawfulBasis.Consent, "Updated privacy settings",
                    "Privacy preferences");

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated privacy settings for user {UserId}", userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update privacy settings for user {UserId}", userId);
                throw;
            }
        }

        // =============================================================================
        // DATA PROCESSING AUDIT TRAIL (GDPR Article 30)
        // =============================================================================

        /// <summary>
        /// Log data processing activity for GDPR compliance audit trail
        /// </summary>
        /// <param name="userId">User whose data was processed</param>
        /// <param name="activity">Type of processing activity</param>
        /// <param name="lawfulBasis">Legal basis for processing</param>
        /// <param name="purpose">Purpose of processing</param>
        /// <param name="dataCategories">Categories of data processed</param>
        /// <param name="relatedConsentId">Related consent record (if applicable)</param>
        /// <param name="thirdParties">Third parties involved (if any)</param>
        /// <param name="retentionDays">Data retention period</param>
        /// <returns>Processing record ID</returns>
        public async Task<Guid> LogDataProcessingActivity(
            string userId, 
            DataProcessingActivity activity, 
            ProcessingLawfulBasis lawfulBasis,
            string purpose,
            string? dataCategories = null,
            Guid? relatedConsentId = null,
            string? thirdParties = null,
            int retentionDays = 2555)
        {
            try
            {
                var record = new DataProcessingRecord
                {
                    UserId = userId,
                    Activity = activity,
                    LawfulBasis = lawfulBasis,
                    ProcessingPurpose = purpose.Substring(0, Math.Min(purpose.Length, 500)),
                    DataCategories = dataCategories?.Substring(0, Math.Min(dataCategories.Length, 500)),
                    ThirdPartiesInvolved = thirdParties?.Substring(0, Math.Min(thirdParties?.Length ?? 0, 500)),
                    RetentionDays = retentionDays,
                    ProcessingSystem = "BlendaIdentityManager",
                    RelatedConsentId = relatedConsentId,
                    ProcessingMetadata = $"{{\"timestamp\":\"{DateTime.UtcNow:O}\",\"version\":\"1.0\"}}"
                };

                _context.DataProcessingRecords.Add(record);
                await _context.SaveChangesAsync();

                return record.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log data processing activity for user {UserId}, activity {Activity}", 
                    userId, activity);
                throw;
            }
        }

        // =============================================================================
        // RIGHT TO BE FORGOTTEN (GDPR Article 17)
        // =============================================================================

        /// <summary>
        /// GDPR Article 17: Process user's right to be forgotten request
        /// </summary>
        /// <param name="userId">User requesting deletion</param>
        /// <param name="legalRetentionDays">Legal retention period (default 7 years)</param>
        /// <returns>Scheduled deletion date</returns>
        public async Task<DateTime> RequestDataDeletionAsync(string userId, int legalRetentionDays = 2555)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    throw new ArgumentException($"User {userId} not found");
                }

                var scheduledDeletion = DateTime.UtcNow.AddDays(legalRetentionDays);

                user.IsMarkedForDeletion = true;
                user.DeletionRequestedAt = DateTime.UtcNow;
                user.ScheduledDeletionDate = scheduledDeletion;
                user.AccountStatus = UserAccountStatus.PendingDeletion;

                await LogDataProcessingActivity(userId, DataProcessingActivity.DataDeletion, 
                    ProcessingLawfulBasis.LegalObligation, "User requested data deletion (Right to be Forgotten)",
                    "All personal data", null, null, legalRetentionDays);

                await _context.SaveChangesAsync();

                _logger.LogInformation("Scheduled data deletion for user {UserId} on {DeletionDate}", 
                    userId, scheduledDeletion);

                return scheduledDeletion;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process deletion request for user {UserId}", userId);
                throw;
            }
        }

        // =============================================================================
        // DATA EXPORT (GDPR Article 20)
        // =============================================================================

        /// <summary>
        /// GDPR Article 20: Generate user data export for portability
        /// </summary>
        /// <param name="userId">User requesting data export</param>
        /// <returns>Comprehensive user data export</returns>
        public async Task<UserDataExport> ExportUserDataAsync(string userId)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.UserConsents)
                    .Include(u => u.PrivacySettings)
                    .Include(u => u.DataProcessingRecords)
                    .FirstOrDefaultAsync(u => u.Id == userId);

                if (user == null)
                {
                    throw new ArgumentException($"User {userId} not found");
                }

                var export = new UserDataExport
                {
                    ExportDate = DateTime.UtcNow,
                    UserId = userId,
                    UserProfile = new UserProfileData
                    {
                        Email = user.Email,
                        UserName = user.UserName,
                        PhoneNumber = user.PhoneNumber,
                        EmailConfirmed = user.EmailConfirmed,
                        PhoneNumberConfirmed = user.PhoneNumberConfirmed,
                        TwoFactorEnabled = user.TwoFactorEnabled,
                        CreatedAt = user.CreatedAt,
                        LastLoginAt = user.LastLoginAt,
                        Country = user.Country,
                        IsEUCitizen = user.IsEUCitizen,
                        AccountStatus = user.AccountStatus.ToString(),
                        DataRetentionDays = user.DataRetentionDays
                    },
                    ConsentRecords = user.UserConsents.Select(c => new ConsentExportData
                    {
                        ConsentType = c.ConsentType.ToString(),
                        Status = c.Status.ToString(),
                        ConsentDate = c.ConsentDate,
                        ExpirationDate = c.ExpirationDate,
                        PolicyVersion = c.PolicyVersion,
                        ConsentSource = c.ConsentSource,
                        WithdrawalReason = c.WithdrawalReason
                    }).ToList(),
                    PrivacySettings = user.PrivacySettings != null ? new PrivacySettingsExportData
                    {
                        AllowAnalytics = user.PrivacySettings.AllowAnalytics,
                        AllowMarketing = user.PrivacySettings.AllowMarketing,
                        AllowThirdPartySharing = user.PrivacySettings.AllowThirdPartySharing,
                        AllowCrossBorderTransfer = user.PrivacySettings.AllowCrossBorderTransfer,
                        AllowAIProcessing = user.PrivacySettings.AllowAIProcessing,
                        PreferredDataRetentionDays = user.PrivacySettings.PreferredDataRetentionDays,
                        PreferredLanguage = user.PrivacySettings.PreferredLanguage,
                        LastUpdated = user.PrivacySettings.LastUpdated
                    } : null,
                    ProcessingHistory = user.DataProcessingRecords.Select(p => new ProcessingHistoryData
                    {
                        Activity = p.Activity.ToString(),
                        LawfulBasis = p.LawfulBasis.ToString(),
                        ProcessingDate = p.ProcessingDate,
                        Purpose = p.ProcessingPurpose,
                        DataCategories = p.DataCategories,
                        RetentionDays = p.RetentionDays,
                        ProcessingSystem = p.ProcessingSystem
                    }).ToList()
                };

                // Update last export request timestamp
                user.LastDataExportRequestAt = DateTime.UtcNow;

                await LogDataProcessingActivity(userId, DataProcessingActivity.DataExport, 
                    ProcessingLawfulBasis.Consent, "User requested data export (Data Portability)",
                    "All personal data and processing history");

                await _context.SaveChangesAsync();

                _logger.LogInformation("Generated data export for user {UserId}", userId);
                return export;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to export data for user {UserId}", userId);
                throw;
            }
        }

        // =============================================================================
        // UTILITY METHODS
        // =============================================================================

        /// <summary>
        /// Hash IP address for privacy protection while maintaining audit capability
        /// </summary>
        /// <param name="ipAddress">IP address to hash</param>
        /// <returns>SHA256 hash of IP address</returns>
        private static string HashIPAddress(string ipAddress)
        {
            using var sha256 = SHA256.Create();
            var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(ipAddress + "GDPR_SALT"));
            return Convert.ToHexString(hashBytes);
        }

        /// <summary>
        /// Get users with expired consents for cleanup
        /// </summary>
        /// <returns>List of users with expired consents</returns>
        public async Task<List<string>> GetUsersWithExpiredConsentsAsync()
        {
            try
            {
                return await _context.UserConsents
                    .Where(c => c.Status == ConsentStatus.Granted && 
                               c.ExpirationDate.HasValue && 
                               c.ExpirationDate.Value < DateTime.UtcNow)
                    .Select(c => c.UserId)
                    .Distinct()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get users with expired consents");
                throw;
            }
        }

        /// <summary>
        /// Mark expired consents as expired
        /// </summary>
        /// <returns>Number of consents marked as expired</returns>
        public async Task<int> MarkExpiredConsentsAsync()
        {
            try
            {
                var expiredConsents = await _context.UserConsents
                    .Where(c => c.Status == ConsentStatus.Granted && 
                               c.ExpirationDate.HasValue && 
                               c.ExpirationDate.Value < DateTime.UtcNow)
                    .ToListAsync();

                foreach (var consent in expiredConsents)
                {
                    consent.Status = ConsentStatus.Expired;
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("Marked {Count} consents as expired", expiredConsents.Count);
                return expiredConsents.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to mark expired consents");
                throw;
            }
        }
    }

    // =============================================================================
    // DATA EXPORT MODELS
    // =============================================================================

    /// <summary>
    /// Comprehensive user data export for GDPR Article 20 compliance
    /// </summary>
    public class UserDataExport
    {
        public DateTime ExportDate { get; set; }
        public string UserId { get; set; } = string.Empty;
        public UserProfileData UserProfile { get; set; } = new();
        public List<ConsentExportData> ConsentRecords { get; set; } = new();
        public PrivacySettingsExportData? PrivacySettings { get; set; }
        public List<ProcessingHistoryData> ProcessingHistory { get; set; } = new();
    }

    public class UserProfileData
    {
        public string? Email { get; set; }
        public string? UserName { get; set; }
        public string? PhoneNumber { get; set; }
        public bool EmailConfirmed { get; set; }
        public bool PhoneNumberConfirmed { get; set; }
        public bool TwoFactorEnabled { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLoginAt { get; set; }
        public string? Country { get; set; }
        public bool IsEUCitizen { get; set; }
        public string AccountStatus { get; set; } = string.Empty;
        public int DataRetentionDays { get; set; }
    }

    public class ConsentExportData
    {
        public string ConsentType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime ConsentDate { get; set; }
        public DateTime? ExpirationDate { get; set; }
        public string? PolicyVersion { get; set; }
        public string? ConsentSource { get; set; }
        public string? WithdrawalReason { get; set; }
    }

    public class PrivacySettingsExportData
    {
        public bool AllowAnalytics { get; set; }
        public bool AllowMarketing { get; set; }
        public bool AllowThirdPartySharing { get; set; }
        public bool AllowCrossBorderTransfer { get; set; }
        public bool AllowAIProcessing { get; set; }
        public int PreferredDataRetentionDays { get; set; }
        public string PreferredLanguage { get; set; } = string.Empty;
        public DateTime LastUpdated { get; set; }
    }

    public class ProcessingHistoryData
    {
        public string Activity { get; set; } = string.Empty;
        public string LawfulBasis { get; set; } = string.Empty;
        public DateTime ProcessingDate { get; set; }
        public string Purpose { get; set; } = string.Empty;
        public string? DataCategories { get; set; }
        public int RetentionDays { get; set; }
        public string ProcessingSystem { get; set; } = string.Empty;
    }

    // =============================================================================
    // INTERFACE DEFINITION
    // =============================================================================

    /// <summary>
    /// Interface for GDPR compliance service operations
    /// </summary>
    public interface IGDPRComplianceService
    {
        Task<Guid> RecordConsentAsync(string userId, ConsentType consentType, string? ipAddress = null,
            string? userAgent = null, string? policyVersion = null, string? consentSource = "web", int? expirationDays = null);
        Task<bool> WithdrawConsentAsync(string userId, ConsentType consentType, string? withdrawalReason = null);
        Task<bool> HasValidConsentAsync(string userId, ConsentType consentType);
        Task<UserPrivacySettings> InitializePrivacySettingsAsync(string userId, bool isEUCitizen = false);
        Task<bool> UpdatePrivacySettingsAsync(string userId, UserPrivacySettings settings);
        Task<Guid> LogDataProcessingActivity(string userId, DataProcessingActivity activity, ProcessingLawfulBasis lawfulBasis,
            string purpose, string? dataCategories = null, Guid? relatedConsentId = null, string? thirdParties = null, int retentionDays = 2555);
        Task<DateTime> RequestDataDeletionAsync(string userId, int legalRetentionDays = 2555);
        Task<UserDataExport> ExportUserDataAsync(string userId);
        Task<List<string>> GetUsersWithExpiredConsentsAsync();
        Task<int> MarkExpiredConsentsAsync();
    }
}
