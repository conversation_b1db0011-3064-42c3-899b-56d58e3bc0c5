using IdentityManager.Models;
using IdentityManager.Services;
using IdentityManager.Security;
using IdentityManager.Middleware;
using Blenda.IdentityManager.Services;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using System.Security.Claims;
using System.Text.Json;

namespace IdentityManager.Services.Configuration;

/// <summary>
/// Extension methods for WebApplication to organize middleware and endpoint configuration
/// </summary>
public static class WebApplicationExtensions
{
    /// <summary>
    /// Configure comprehensive security headers middleware
    /// </summary>
    public static WebApplication ConfigureSecurityHeaders(this WebApplication app)
    {
        // SECURITY: Configure the HTTP request pipeline with comprehensive security headers
        app.Use(async (context, next) =>
        {
            // SECURITY: Basic security headers
            context.Response.Headers["X-Content-Type-Options"] = "nosniff";
            context.Response.Headers["X-Frame-Options"] = "DENY";
            context.Response.Headers["X-XSS-Protection"] = "1; mode=block";
            context.Response.Headers["Referrer-Policy"] = "strict-origin-when-cross-origin";
            
            // SECURITY: Enhanced security headers for comprehensive protection
            context.Response.Headers["X-Permitted-Cross-Domain-Policies"] = "none";
            context.Response.Headers["Cross-Origin-Opener-Policy"] = "same-origin";
            context.Response.Headers["Cross-Origin-Embedder-Policy"] = "require-corp";
            context.Response.Headers["Cross-Origin-Resource-Policy"] = "same-site";
            
            // SECURITY: Content Security Policy (CSP) for XSS protection
            var cspPolicy = "default-src 'self'; " +
                           "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; " +
                           "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
                           "font-src 'self' https://fonts.gstatic.com; " +
                           "img-src 'self' data: https:; " +
                           "connect-src 'self' https://api.blenda.lat https://identity.blenda.lat; " +
                           "frame-ancestors 'none'; " +
                           "base-uri 'self'; " +
                           "form-action 'self'";
            
            context.Response.Headers["Content-Security-Policy"] = cspPolicy;
            
            // SECURITY: Permissions Policy to disable unnecessary browser features
            var permissionsPolicy = "geolocation=(), microphone=(), camera=(), " +
                                   "fullscreen=(self), payment=(), usb=(), " +
                                   "accelerometer=(), gyroscope=(), magnetometer=()";
            
            context.Response.Headers["Permissions-Policy"] = permissionsPolicy;
            
            if (!app.Environment.IsDevelopment())
            {
                // SECURITY: HSTS for production (HTTPS enforcement)
                context.Response.Headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains; preload";
                
                // SECURITY: Additional production-only security headers
                context.Response.Headers["X-Robots-Tag"] = "noindex, nofollow";
            }
            
            await next();
        });

        return app;
    }

    /// <summary>
    /// Configure advanced security headers using dedicated middleware
    /// Provides comprehensive OWASP-compliant security headers with environment-aware policies
    /// </summary>
    public static WebApplication ConfigureAdvancedSecurityHeaders(this WebApplication app)
    {
        // ENHANCEMENT: Use advanced security headers middleware for comprehensive protection
        app.UseAdvancedSecurityHeaders(app.Environment);
        return app;
    }

    /// <summary>
    /// Configure enhanced CORS middleware with debugging
    /// </summary>
    public static WebApplication ConfigureEnhancedCors(this WebApplication app)
    {
        // SECURITY: Enhanced CORS middleware for debugging and explicit header handling
        app.Use(async (context, next) =>
        {
            var origin = context.Request.Headers["Origin"].ToString();
            var logger = context.RequestServices.GetRequiredService<ILogger<Program>>();
            
            // Log CORS requests for debugging
            if (!string.IsNullOrEmpty(origin))
            {
                logger.LogInformation("CORS request from origin: {Origin} to path: {Path}", origin, context.Request.Path);
            }
            
            // Handle preflight requests explicitly
            if (context.Request.Method == "OPTIONS")
            {
                logger.LogInformation("Handling preflight OPTIONS request from origin: {Origin}", origin);
                
                var allowedOrigins = new[]
                {
                    "http://localhost:5000",                    
                    "http://localhost:5045",
                    "http://localhost:5160",
                    "http://localhost:5176",
                    "https://localhost:5001",
                    "https://localhost:7171",                    
                    "https://localhost:7045",                    
                    "https://localhost:7249",
                    "https://blenda.lat",
                    "https://api.blenda.lat",
                    "https://admin.blenda.lat",
                    "https://blog.blenda.lat",
                    "https://docs.blenda.lat",
                    "https://grp.blenda.lat",
                    "https://identity.blenda.lat",
                    "https://my.blenda.lat",
                    "https://portal.blenda.lat",
                    "https://www.blenda.lat"
                };
                
                if (allowedOrigins.Contains(origin))
                {
                    context.Response.Headers["Access-Control-Allow-Origin"] = origin;
                    context.Response.Headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS, PATCH";
                    context.Response.Headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With, Accept, Origin, Access-Control-Request-Method, Access-Control-Request-Headers";
                    context.Response.Headers["Access-Control-Allow-Credentials"] = "true";
                    context.Response.Headers["Access-Control-Max-Age"] = "3600";
                    
                    context.Response.StatusCode = 200;
                    return;
                }
            }
            
            await next();
        });

        return app;
    }

    /// <summary>
    /// Configure development and staging specific middleware
    /// </summary>
    public static WebApplication ConfigureDevelopmentMiddleware(this WebApplication app)
    {
        // SECURITY: Conditional Swagger based on environment
        if (app.Environment.IsDevelopment() || app.Environment.IsStaging())
        {
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "Blenda Identity API v1");
                c.RoutePrefix = "swagger";
                c.DocumentTitle = "Blenda Identity API Documentation";
            });
        }

        return app;
    }

    /// <summary>
    /// Configure production security middleware
    /// </summary>
    public static WebApplication ConfigureProductionSecurity(this WebApplication app)
    {
        // SECURITY: Force HTTPS in production
        if (app.Environment.IsProduction())
        {
            app.UseHttpsRedirection();
            app.UseHsts();
        }

        return app;
    }

    /// <summary>
    /// Configure the standard ASP.NET Core middleware pipeline
    /// </summary>
    public static WebApplication ConfigureMiddlewarePipeline(this WebApplication app)
    {
        // SECURITY: CORS must be configured before authentication and authorization
        app.UseCors();

        // SECURITY: Apply rate limiting before authentication
        app.UseRateLimiter();

        // SECURITY: Use ASP.NET Core standard authentication middleware
        app.UseAuthentication();
        app.UseAuthorization();
        
        // SECURITY: Add comprehensive audit logging middleware for compliance and security monitoring
        app.UseMiddleware<Blenda.IdentityManager.Middleware.AuditingMiddleware>();
        
        // ACTIVITY TRACKING: Add activity tracking middleware after authentication and auditing
        app.UseActivityTracking();

        return app;
    }

    /// <summary>
    /// Initialize database and seed required roles
    /// </summary>
    public static WebApplication InitializeDatabase(this WebApplication app)
    {
        using (var scope = app.Services.CreateScope())
        {
            var services = scope.ServiceProvider;
            var logger = services.GetRequiredService<ILogger<Program>>();

            try
            {
                // Initialize role seeding
                var roleSeeder = services.GetRequiredService<RoleSeederService>();
                var roleClaimsSeeder = services.GetRequiredService<RoleClaimsSeederService>();
                Task.Run(async () =>
                {
                    try
                    {
                        await roleSeeder.EnsureRolesExistAsync();
                        await roleClaimsSeeder.EnsureRoleClaimsExistAsync();
                        logger.LogInformation("Database initialization completed successfully");
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Error during database role seeding");
                    }
                }).Wait(TimeSpan.FromSeconds(30)); // Wait max 30 seconds for role seeding
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error during database initialization");
                // Don't throw - let the app start even if role seeding fails
            }
        }

        return app;
    }

    /// <summary>
    /// Configure all API endpoints
    /// </summary>
    public static WebApplication ConfigureEndpoints(this WebApplication app)
    {
        // Root endpoint with enhanced security logging and rate limiting
        app.MapGet("/", (ClaimsPrincipal user, ILogger<Program> logger, HttpContext httpContext) =>
        {
            var userEmail = user.FindFirst(ClaimTypes.Email)?.Value ?? "Unknown";
            var userRoles = string.Join(", ", user.FindAll(ClaimTypes.Role).Select(c => c.Value));
            var clientIp = httpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            
            logger.LogInformation("Root endpoint accessed - User: {UserEmail}, Roles: {Roles}, IP: {ClientIP}, Authenticated: {IsAuthenticated}",
                userEmail, userRoles, clientIp, user.Identity?.IsAuthenticated);
            
            return Results.Ok(new 
            { 
                message = "Blenda Core Identity & Access Management API", 
                version = "v1.0.0",
                status = "Active",
                timestamp = DateTime.UtcNow,
                environment = app.Environment.EnvironmentName
            });
        }).RequireAuthorization("AdminPolicy")
          .RequireRateLimiting("AdminOperationsPolicy")
          .WithTags("System");

        // SECURITY: Enhanced identity API endpoints with rate limiting
        app.MapGroup("/api/auth")
           .MapCustomIdentityApi<ApplicationUser>()
           .RequireRateLimiting("AuthenticationPolicy")
           .WithTags("Authentication");

        // SECURITY: GDPR compliance endpoints with enhanced security
        app.MapGroup("/api/gdpr")
           .MapGDPREndpoints()
           .RequireRateLimiting("GDPRPolicy")
           .WithTags("GDPR Compliance");

        // SECURITY: Health check endpoint with detailed information
        app.MapHealthChecks("/health", new HealthCheckOptions
        {
            ResponseWriter = async (context, report) =>
            {
                context.Response.ContentType = "application/json";
                var response = new
                {
                    status = report.Status.ToString(),
                    checks = report.Entries.Select(x => new
                    {
                        name = x.Key,
                        status = x.Value.Status.ToString(),
                        duration = x.Value.Duration.TotalMilliseconds,
                        description = x.Value.Description,
                        data = x.Value.Data?.Count > 0 ? x.Value.Data : null
                    }),
                    timestamp = DateTime.UtcNow
                };
                await context.Response.WriteAsync(JsonSerializer.Serialize(response, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = true
                }));
            }
        }).AllowAnonymous()
          .RequireRateLimiting("PublicPolicy")
          .WithTags("Health");


        return app;
    }
}
