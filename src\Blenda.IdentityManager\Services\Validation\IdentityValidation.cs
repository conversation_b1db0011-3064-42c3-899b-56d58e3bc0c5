using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http.HttpResults;

namespace IdentityManager.Services.Validation;

/// <summary>
/// Common validation utilities for identity operations
/// </summary>
public static class IdentityValidation
{
    private static readonly EmailAddressAttribute _emailAddressAttribute = new();

    /// <summary>
    /// Validates an email address format
    /// </summary>
    public static bool IsValidEmail(string? email)
    {
        return !string.IsNullOrEmpty(email) && _emailAddressAttribute.IsValid(email);
    }

    /// <summary>
    /// Validates a government email domain
    /// </summary>
    public static bool IsGovernmentEmail(string email)
    {
        return email.EndsWith("@dt.gob.cl", StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Validates a corporate email domain
    /// </summary>
    public static bool IsCorporateEmail(string email)
    {
        var corporateDomains = new[] { ".com", ".org", ".net" };
        return corporateDomains.Any(domain => email.Contains(domain, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Creates a validation problem from IdentityResult errors
    /// </summary>
    public static ValidationProblem CreateValidationProblem(IdentityResult result)
    {
        var errors = new Dictionary<string, string[]>();
        foreach (var error in result.Errors)
        {
            errors.TryAdd(error.Code, new[] { error.Description });
        }
        return TypedResults.ValidationProblem(errors);
    }

    /// <summary>
    /// Creates a validation problem for a specific field and error
    /// </summary>
    public static ValidationProblem CreateValidationProblem(string field, string error)
    {
        var errors = new Dictionary<string, string[]> { { field, new[] { error } } };
        return TypedResults.ValidationProblem(errors);
    }
}

/// <summary>
/// Email validation strategies for different user types
/// </summary>
public static class EmailValidationStrategies
{
    /// <summary>
    /// Standard email validation for regular users
    /// </summary>
    public static Func<string, bool> Standard => email => IdentityValidation.IsValidEmail(email);

    /// <summary>
    /// Government email validation for labor inspectors
    /// </summary>
    public static Func<string, bool> Government => email => 
        IdentityValidation.IsValidEmail(email) && IdentityValidation.IsGovernmentEmail(email);

    /// <summary>
    /// Corporate email validation for business users
    /// </summary>
    public static Func<string, bool> Corporate => email => 
        IdentityValidation.IsValidEmail(email) && IdentityValidation.IsCorporateEmail(email);

    /// <summary>
    /// No additional validation beyond basic email format
    /// </summary>
    public static Func<string, bool> None => email => IdentityValidation.IsValidEmail(email);
}
