using Microsoft.AspNetCore.Identity;
using IdentityManager.Models;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.WebUtilities;
using System.Text;
using System.Security.Claims;
using Microsoft.AspNetCore.Identity.Data;
using IdentityManager.Services.Validation;
using Blenda.Shared.Messaging.Services;
using Blenda.Shared.Messaging.Constants;
using Blenda.Shared.Messaging.Models;
using Blenda.Shared.Messaging.Extensions;

using ForgotPasswordRequest = Microsoft.AspNetCore.Identity.Data.ForgotPasswordRequest;
using ResetPasswordRequest = Microsoft.AspNetCore.Identity.Data.ResetPasswordRequest;
using InfoRequest = Microsoft.AspNetCore.Identity.Data.InfoRequest;
using InfoResponse = Microsoft.AspNetCore.Identity.Data.InfoResponse;
using ValidationProblem = Microsoft.AspNetCore.Http.HttpResults.ValidationProblem;
using ResendConfirmationEmailRequest = IdentityManager.Models.ResendConfirmationEmailRequest;
using Microsoft.Extensions.Options;

namespace IdentityManager.Services.Handlers;

/// <summary>
/// Interface for user account management operations
/// </summary>
public interface IUserAccountHandler<TUser> where TUser : class
{
    /// <summary>
    /// Confirms user email address
    /// </summary>
    Task<Results<Ok, ValidationProblem, NotFound>> ConfirmEmailAsync(string userId, string code, string? changedEmail = null);

    /// <summary>
    /// Sends password reset email
    /// </summary>
    Task<Ok> ForgotPasswordAsync(ExtendedForgotPasswordRequest request);

    /// <summary>
    /// Resets user password
    /// </summary>
    Task<Results<Ok, ValidationProblem>> ResetPasswordAsync(ExtendedResetPasswordRequest request);

    /// <summary>
    /// Custom reset password with optional reset code
    /// </summary>
    Task<Results<Ok, ValidationProblem>> CustomResetPasswordAsync(CustomResetPasswordRequest request);

    /// <summary>
    /// Gets user information
    /// </summary>
    Task<Results<Ok<InfoResponse>, ValidationProblem, NotFound>> GetUserInfoAsync(ClaimsPrincipal user);

    /// <summary>
    /// Gets extended user information including Two-Factor Authentication status
    /// </summary>
    Task<Results<Ok<ExtendedInfoResponse>, ValidationProblem, NotFound>> GetExtendedUserInfoAsync(ClaimsPrincipal user);

    /// <summary>
    /// Updates user information
    /// </summary>
    Task<Results<Ok<InfoResponse>, ValidationProblem, NotFound>> PostUserInfoAsync(ClaimsPrincipal user, InfoRequest infoRequest);

    /// <summary>
    /// Updates extended user information
    /// </summary>
    Task<Results<Ok<ExtendedInfoResponse>, ValidationProblem, NotFound>> PostExtendedUserInfoAsync(ClaimsPrincipal user, ExtendedInfoRequest infoRequest);

    /// <summary>
    /// Resends email confirmation link
    /// </summary>
    Task<Ok> ResendConfirmationEmailAsync(ExtendedResendConfirmationEmailRequest request);
}

/// <summary>
/// Handles user account management operations with human-friendly messaging and standardized responses
/// Provides comprehensive account management including:
/// - Email confirmation with localized messages
/// - Password reset with security-conscious error handling
/// - User profile management with GDPR compliance
/// - Extended user information with privacy controls
/// - Multi-language support for all user-facing messages
/// </summary>
public class UserAccountHandler<TUser> : IUserAccountHandler<TUser> where TUser : class
{
    private readonly UserManager<TUser> _userManager;
    private readonly IEmailSender<TUser> _emailSender;
    private readonly IConfiguration _configuration;
    private readonly ILogger<UserAccountHandler<TUser>> _logger;
    private readonly IClientCommunicationService _communicationService;
    private readonly IMessageService _messageService;
    private readonly GrpApiOptions _options;

    public UserAccountHandler(
        UserManager<TUser> userManager,
        IEmailSender<TUser> emailSender,
        IConfiguration configuration,
        ILogger<UserAccountHandler<TUser>> logger,
        IClientCommunicationService communicationService,
        IMessageService messageService,
        IOptions<GrpApiOptions> options)
    {
        _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
        _emailSender = emailSender ?? throw new ArgumentNullException(nameof(emailSender));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
        _messageService = messageService ?? throw new ArgumentNullException(nameof(messageService));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
    }

    /// <inheritdoc />
    public async Task<Results<Ok, ValidationProblem, NotFound>> ConfirmEmailAsync(
        string userId, 
        string code, 
        string? changedEmail = null)
    {
        try
        {
            _logger.LogInformation("Email confirmation attempt for user {UserId}", userId);

            if (string.IsNullOrWhiteSpace(userId))
            {
                _logger.LogWarning("Email confirmation failed: Empty userId provided");
                return CreateValidationProblem("UserId", _messageService.GetMessage(MessageKeys.Validation.Required));
            }

            if (string.IsNullOrWhiteSpace(code))
            {
                _logger.LogWarning("Email confirmation failed: Empty code provided for user {UserId}", userId);
                return CreateValidationProblem("Code", _messageService.GetMessage(MessageKeys.Validation.Required));
            }

            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                _logger.LogWarning("Email confirmation failed: User {UserId} not found", userId);
                return TypedResults.NotFound();
            }

            string decodedCode;
            try
            {
                decodedCode = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(code));
            }
            catch (FormatException)
            {
                _logger.LogWarning("Email confirmation failed: Invalid code format for user {UserId}", userId);
                return CreateValidationProblem("Code", _messageService.GetMessage(MessageKeys.Email.InvalidConfirmationLink));
            }

            IdentityResult result;
            if (!string.IsNullOrEmpty(changedEmail))
            {
                var emailValidator = new System.ComponentModel.DataAnnotations.EmailAddressAttribute();
                if (!emailValidator.IsValid(changedEmail))
                {
                    _logger.LogWarning("Email confirmation failed: Invalid changedEmail format {ChangedEmail} for user {UserId}", changedEmail, userId);
                    return CreateValidationProblem("ChangedEmail", _messageService.GetMessage(MessageKeys.Registration.InvalidEmailFormat));
                }

                _logger.LogInformation("Attempting to change email for user {UserId} to {ChangedEmail}", userId, changedEmail);
                result = await _userManager.ChangeEmailAsync(user, changedEmail, decodedCode);
            }
            else
            {
                result = await _userManager.ConfirmEmailAsync(user, decodedCode);
            }

            if (!result.Succeeded)
            {
                _logger.LogWarning("Email confirmation failed for user {UserId}: {Errors}", 
                    userId, string.Join(", ", result.Errors.Select(e => e.Description)));
                return CreateValidationProblem(TranslateIdentityErrors(result.Errors));
            }

            _logger.LogInformation("Email confirmed successfully for user {UserId}", userId);
            return TypedResults.Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during email confirmation for user {UserId}", userId);
            return CreateValidationProblem("ConfirmationError", _messageService.GetMessage(MessageKeys.Email.ConfirmationFailed));
        }
    }

    /// <inheritdoc />
    public async Task<Ok> ForgotPasswordAsync(ExtendedForgotPasswordRequest request)
    {
        try
        {
            if (request == null || string.IsNullOrWhiteSpace(request.Email))
            {
                _logger.LogWarning("Password reset request with invalid email data");
                return TypedResults.Ok(); // Don't reveal validation errors for security
            }

            _logger.LogInformation("Password reset request for email: {Email}", request.Email);

            var registrationPrefix = string.IsNullOrWhiteSpace(request.ClientApp) ? _options.DefaultTenantId.ToString() : request.ClientApp;

            var registrationUniqueEmail = $"{registrationPrefix}+{request.Email}";

            var user = await _userManager.FindByEmailAsync(registrationUniqueEmail);
            if (user is null || !await _userManager.IsEmailConfirmedAsync(user))
            {
                // Don't reveal that the user does not exist or is not confirmed for security
                _logger.LogInformation("Password reset request for non-existent or unconfirmed email: {Email}", request.Email);
                return TypedResults.Ok();
            }

            var code = await _userManager.GeneratePasswordResetTokenAsync(user);
            code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));

            var frontendUrl = _configuration["ApiSettings:PublicFacingService"] ?? "https://blenda.lat";
            var resetLink = $"{frontendUrl}/account/reset-password?email={Uri.EscapeDataString(request.Email)}&code={Uri.EscapeDataString(code)}";

            if (!string.IsNullOrWhiteSpace(request.Source))
            {
                resetLink += $"&source={request.Source}";
            }

            if (!string.IsNullOrWhiteSpace(request.ClientApp))
            {
                resetLink += $"&clientApp={request.ClientApp}";
            }

            await _emailSender.SendPasswordResetLinkAsync(user, request.Email, resetLink);

            _logger.LogInformation("Password reset email sent to {Email}", request.Email);
            return TypedResults.Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending password reset email to {Email}", request?.Email ?? "unknown");
            return TypedResults.Ok(); // Don't reveal errors to prevent enumeration
        }
    }

    /// <inheritdoc />
    public async Task<Results<Ok, ValidationProblem>> ResetPasswordAsync(ExtendedResetPasswordRequest request)
    {
        try
        {
            if (request == null)
            {
                return CreateValidationProblem("Request", _messageService.GetMessage(MessageKeys.Validation.Required));
            }

            if (string.IsNullOrWhiteSpace(request.Email))
            {
                return CreateValidationProblem("Email", _messageService.GetMessage(MessageKeys.Validation.Required));
            }

            if (string.IsNullOrWhiteSpace(request.ResetCode))
            {
                return CreateValidationProblem("ResetCode", _messageService.GetMessage(MessageKeys.Validation.Required));
            }

            if (string.IsNullOrWhiteSpace(request.NewPassword))
            {
                return CreateValidationProblem("NewPassword", _messageService.GetMessage(MessageKeys.Validation.Required));
            }

            _logger.LogInformation("Password reset attempt for email: {Email}", request.Email);

            var registrationPrefix = string.IsNullOrWhiteSpace(request.ClientApp) ? _options.DefaultTenantId.ToString() : request.ClientApp;

            var registrationUniqueEmail = $"{registrationPrefix}+{request.Email}";

            var user = await _userManager.FindByEmailAsync(registrationUniqueEmail);
            if (user is null)
            {
                _logger.LogWarning("Password reset failed: User not found for email {Email}", request.Email);
                return CreateValidationProblem("Email", _messageService.GetMessage(MessageKeys.UserManagement.UserNotFound));
            }

            IdentityResult result;
            try
            {
                var decodedToken = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(request.ResetCode));
                result = await _userManager.ResetPasswordAsync(user, decodedToken, request.NewPassword);
            }
            catch (FormatException)
            {
                _logger.LogWarning("Password reset failed: Invalid reset code format for {Email}", request.Email);
                return CreateValidationProblem("ResetCode", _messageService.GetMessage(MessageKeys.Authentication.TokenInvalid));
            }

            if (!result.Succeeded)
            {
                _logger.LogWarning("Password reset failed for {Email}: {Errors}", 
                    request.Email, string.Join(", ", result.Errors.Select(e => e.Description)));
                return CreateValidationProblem(TranslateIdentityErrors(result.Errors));
            }

            _logger.LogInformation("Password reset successful for {Email}", request.Email);
            return TypedResults.Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during password reset for {Email}", request?.Email ?? "unknown");
            return CreateValidationProblem("ResetError", _messageService.GetMessage(MessageKeys.UserManagement.PasswordChangeFailed));
        }
    }

    /// <inheritdoc />
    public async Task<Results<Ok, ValidationProblem>> CustomResetPasswordAsync(CustomResetPasswordRequest request)
    {
        try
        {
            if (request == null)
            {
                return CreateValidationProblem("Request", _messageService.GetMessage(MessageKeys.Validation.Required));
            }

            if (string.IsNullOrWhiteSpace(request.Email))
            {
                return CreateValidationProblem("Email", _messageService.GetMessage(MessageKeys.Validation.Required));
            }

            if (string.IsNullOrWhiteSpace(request.NewPassword))
            {
                return CreateValidationProblem("NewPassword", _messageService.GetMessage(MessageKeys.Validation.Required));
            }

            _logger.LogInformation("Custom password reset attempt for email: {Email}", request.Email);

            var user = await _userManager.FindByEmailAsync(request.Email);
            if (user is null)
            {
                _logger.LogWarning("Custom password reset failed: User not found for email {Email}", request.Email);
                return CreateValidationProblem("Email", _messageService.GetMessage(MessageKeys.UserManagement.UserNotFound));
            }

            IdentityResult result;
            if (!string.IsNullOrEmpty(request.ResetCode))
            {
                // Reset with code
                try
                {
                    var decodedToken = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(request.ResetCode));
                    result = await _userManager.ResetPasswordAsync(user, decodedToken, request.NewPassword);
                }
                catch (FormatException)
                {
                    _logger.LogWarning("Custom password reset failed: Invalid reset code format for {Email}", request.Email);
                    return CreateValidationProblem("ResetCode", _messageService.GetMessage(MessageKeys.Authentication.TokenInvalid));
                }
            }
            else
            {
                // Direct password change (requires additional authentication in real scenarios)
                _logger.LogWarning("Direct password change attempted without reset code for {Email} - this should require additional authentication", request.Email);
                var token = await _userManager.GeneratePasswordResetTokenAsync(user);
                result = await _userManager.ResetPasswordAsync(user, token, request.NewPassword);
            }

            if (!result.Succeeded)
            {
                _logger.LogWarning("Custom password reset failed for {Email}: {Errors}", 
                    request.Email, string.Join(", ", result.Errors.Select(e => e.Description)));
                return CreateValidationProblem(TranslateIdentityErrors(result.Errors));
            }

            _logger.LogInformation("Custom password reset successful for {Email}", request.Email);
            return TypedResults.Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during custom password reset for {Email}", request?.Email ?? "unknown");
            return CreateValidationProblem("ResetError", _messageService.GetMessage(MessageKeys.UserManagement.PasswordChangeFailed));
        }
    }

    /// <inheritdoc />
    public async Task<Results<Ok<InfoResponse>, ValidationProblem, NotFound>> GetUserInfoAsync(ClaimsPrincipal userPrincipal)
    {
        try
        {
            if (userPrincipal == null)
            {
                return CreateValidationProblem("User", _messageService.GetMessage(MessageKeys.Authentication.InvalidCredentials));
            }

            var user = await _userManager.GetUserAsync(userPrincipal);
            if (user == null)
            {
                _logger.LogWarning("GetUserInfo failed: User not found for principal");
                return TypedResults.NotFound();
            }

            var email = await _userManager.GetEmailAsync(user) ?? "";
            var isEmailConfirmed = await _userManager.IsEmailConfirmedAsync(user);

            _logger.LogDebug("Retrieved user info for user {UserId}", await _userManager.GetUserIdAsync(user));

            return TypedResults.Ok(new InfoResponse
            {
                Email = email,
                IsEmailConfirmed = isEmailConfirmed
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user info");
            return CreateValidationProblem("UserInfoError", _messageService.GetMessage(MessageKeys.System.UnexpectedError));
        }
    }

    /// <inheritdoc />
    public async Task<Results<Ok<InfoResponse>, ValidationProblem, NotFound>> PostUserInfoAsync(
        ClaimsPrincipal userPrincipal, 
        InfoRequest infoRequest)
    {
        try
        {
            if (userPrincipal == null)
            {
                return CreateValidationProblem("User", _messageService.GetMessage(MessageKeys.Authentication.InvalidCredentials));
            }

            if (infoRequest == null)
            {
                return CreateValidationProblem("Request", _messageService.GetMessage(MessageKeys.Validation.Required));
            }

            var user = await _userManager.GetUserAsync(userPrincipal);
            if (user == null)
            {
                _logger.LogWarning("PostUserInfo failed: User not found for principal");
                return TypedResults.NotFound();
            }

            var currentEmail = await _userManager.GetEmailAsync(user);

            // Handle email updates
            if (!string.IsNullOrEmpty(infoRequest.NewEmail) && infoRequest.NewEmail != currentEmail)
            {
                var emailValidator = new System.ComponentModel.DataAnnotations.EmailAddressAttribute();
                if (!emailValidator.IsValid(infoRequest.NewEmail))
                {
                    return CreateValidationProblem("NewEmail", _messageService.GetMessage(MessageKeys.Registration.InvalidEmailFormat));
                }

                var setEmailResult = await _userManager.SetEmailAsync(user, infoRequest.NewEmail);
                if (!setEmailResult.Succeeded)
                {
                    return CreateValidationProblem(TranslateIdentityErrors(setEmailResult.Errors));
                }

                var setUserNameResult = await _userManager.SetUserNameAsync(user, infoRequest.NewEmail);
                if (!setUserNameResult.Succeeded)
                {
                    return CreateValidationProblem(TranslateIdentityErrors(setUserNameResult.Errors));
                }

                _logger.LogInformation("Email updated for user {UserId} from {OldEmail} to {NewEmail}", 
                    await _userManager.GetUserIdAsync(user), currentEmail, infoRequest.NewEmail);
            }

            // Handle password updates
            if (!string.IsNullOrEmpty(infoRequest.NewPassword))
            {
                if (string.IsNullOrEmpty(infoRequest.OldPassword))
                {
                    return CreateValidationProblem("OldPassword", _messageService.GetMessage(MessageKeys.Validation.Required));
                }

                var result = await _userManager.ChangePasswordAsync(user, infoRequest.OldPassword, infoRequest.NewPassword);
                if (!result.Succeeded)
                {
                    _logger.LogWarning("Password change failed for user {UserId}", await _userManager.GetUserIdAsync(user));
                    return CreateValidationProblem(TranslateIdentityErrors(result.Errors));
                }

                _logger.LogInformation("Password changed successfully for user {UserId}", await _userManager.GetUserIdAsync(user));
            }

            // Return updated user information
            var email = await _userManager.GetEmailAsync(user) ?? "";
            var isEmailConfirmed = await _userManager.IsEmailConfirmedAsync(user);

            return TypedResults.Ok(new InfoResponse
            {
                Email = email,
                IsEmailConfirmed = isEmailConfirmed
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user info");
            return CreateValidationProblem("UpdateError", _messageService.GetMessage(MessageKeys.UserManagement.ProfileUpdateFailed));
        }
    }

    /// <inheritdoc />
    public async Task<Ok> ResendConfirmationEmailAsync(ExtendedResendConfirmationEmailRequest request)
    {
        try
        {
            _logger.LogInformation("Resend confirmation email request for: {Email}", request.Email);

            var registrationPrefix = string.IsNullOrWhiteSpace(request.ClientApp) ? _options.DefaultTenantId.ToString() : request.ClientApp;

            var registrationUniqueEmail = $"{registrationPrefix}+{request.Email}";

            var user = await _userManager.FindByEmailAsync(registrationUniqueEmail);
            if (user is null)
            {
                // Don't reveal that the user does not exist
                _logger.LogInformation("Resend confirmation email request for non-existent email: {Email}", request.Email);
                return TypedResults.Ok();
            }

            //Check if email is already confirmed
            if (await _userManager.IsEmailConfirmedAsync(user))
            {
                _logger.LogInformation("Resend confirmation email request for already confirmed email: {Email}", request.Email);
                return TypedResults.Ok(); // Don't reveal that email is already confirmed
            }

            // Generate new confirmation token
            var code = await _userManager.GenerateEmailConfirmationTokenAsync(user);
            code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));

            var userId = await _userManager.GetUserIdAsync(user);
            var frontendUrl = _configuration["ApiSettings:PublicFacingService"] ?? "https://blenda.lat";
            var confirmEmailUrl = $"{frontendUrl}/Account/confirm-email?userId={userId}&code={code}";

            if (!string.IsNullOrWhiteSpace(request.Source))
            {
                confirmEmailUrl += $"&source={request.Source}";
            }
            if (!string.IsNullOrWhiteSpace(request.ClientApp))
            {
                confirmEmailUrl += $"&clientApp={request.ClientApp}";
            }

            await _emailSender.SendConfirmationLinkAsync(user, request.Email, confirmEmailUrl);

            _logger.LogInformation("Confirmation email resent to {Email}", request.Email);
            return TypedResults.Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resending confirmation email to {Email}", request.Email);
            return TypedResults.Ok(); // Don't reveal errors to prevent enumeration
        }
    }

    /// <inheritdoc />
    public async Task<Results<Ok<ExtendedInfoResponse>, ValidationProblem, NotFound>> GetExtendedUserInfoAsync(ClaimsPrincipal userPrincipal)
    {
        try
        {
            var user = await _userManager.GetUserAsync(userPrincipal);
            if (user == null)
            {
                _logger.LogWarning("GetExtendedUserInfoAsync: User not found from principal");
                return TypedResults.NotFound();
            }

            // Cast to ApplicationUser to access GDPR properties
            if (user is not ApplicationUser appUser)
            {
                var userId = await _userManager.GetUserIdAsync(user);
                _logger.LogError("GetExtendedUserInfoAsync: User type not supported for user {UserId}", userId);
                var errorResponse = (ValidationErrorResponse)_communicationService.CreateValidationErrorResponse(
                    new Dictionary<string, string[]> { ["UserTypeError"] = [MessageKeys.Validation.InvalidUserType] }
                );
                return TypedResults.ValidationProblem(errorResponse.ValidationErrors);
            }

            // Basic profile and security information
            var email = await _userManager.GetEmailAsync(user) ?? "";
            var isEmailConfirmed = await _userManager.IsEmailConfirmedAsync(user);
            var twoFactorEnabled = await _userManager.GetTwoFactorEnabledAsync(user);
            var recoveryCodesCount = await _userManager.CountRecoveryCodesAsync(user);
            var hasAuthenticatorKey = !string.IsNullOrEmpty(await _userManager.GetAuthenticatorKeyAsync(user));

            // Calculate current session start (simplified - would be from session store in real implementation)
            var currentSessionStart = DateTime.UtcNow.AddHours(-2); // Example: session started 2 hours ago

            // TODO: In a real implementation, get marketing/analytics consents from UserConsents table
            // For now, we'll provide placeholder data that can be enhanced with GDPR service

            var response = new ExtendedInfoResponse
            {
                // Basic Profile
                Email = email,
                IsEmailConfirmed = isEmailConfirmed,
                CreatedAt = appUser.CreatedAt,

                // Session and Activity - "Último Acceso"
                LastLoginAt = appUser.LastLoginAt,
                CurrentSessionStart = currentSessionStart,
                LastAccessCountry = appUser.Country,
                AccountStatus = appUser.AccountStatus.ToString(),

                // Security Status (2FA)
                TwoFactorEnabled = twoFactorEnabled,
                RecoveryCodesCount = twoFactorEnabled ? recoveryCodesCount : null,
                HasAuthenticatorKey = hasAuthenticatorKey,

                // GDPR Compliance Information
                TermsOfServiceConsent = new ConsentInfo
                {
                    IsGranted = appUser.HasAcceptedTermsOfService,
                    GrantedAt = appUser.TermsOfServiceAcceptedAt,
                    PolicyVersion = appUser.TermsOfServiceVersion,
                    ConsentSource = "registration"
                },
                PrivacyPolicyConsent = new ConsentInfo
                {
                    IsGranted = appUser.HasAcceptedPrivacyPolicy,
                    GrantedAt = appUser.PrivacyPolicyAcceptedAt,
                    PolicyVersion = appUser.PrivacyPolicyVersion,
                    ConsentSource = "registration"
                },
                
                // TODO: Get real marketing/analytics consent from UserConsents table
                MarketingConsent = null, // Will be populated when GDPR service integration is complete
                AnalyticsConsent = null, // Will be populated when GDPR service integration is complete
                
                DataRetentionDays = appUser.DataRetentionDays,
                Country = appUser.Country,
                IsEUCitizen = appUser.IsEUCitizen,
                LastDataExportRequestAt = appUser.LastDataExportRequestAt,

                // Deletion Request Info
                DeletionRequest = appUser.IsMarkedForDeletion ? new DeletionRequestInfo
                {
                    IsMarkedForDeletion = true,
                    RequestedAt = appUser.DeletionRequestedAt,
                    ScheduledDeletionDate = appUser.ScheduledDeletionDate,
                    DaysUntilDeletion = appUser.ScheduledDeletionDate.HasValue 
                        ? (int)(appUser.ScheduledDeletionDate.Value - DateTime.UtcNow).TotalDays
                        : null
                } : null,

                // Recent Consent Activities (basic example - would be from audit logs in real implementation)
                RecentConsentActivities = new List<RecentConsentActivity>
                {
                    new()
                    {
                        ConsentType = "Essential",
                        Action = "Granted",
                        ActionDate = appUser.TermsOfServiceAcceptedAt ?? DateTime.UtcNow.AddDays(-30),
                        Source = "registration"
                    }
                }
            };

            _logger.LogInformation("Retrieved comprehensive profile information for user {UserId} including GDPR data and last access: {LastAccess}", 
                appUser.Id, appUser.LastLoginAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? "Never");
            
            return TypedResults.Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting extended user info with GDPR data");
            var errorResponse = (ValidationErrorResponse)_communicationService.CreateValidationErrorResponse(
                new Dictionary<string, string[]> { ["ExtendedUserInfoError"] = [MessageKeys.System.InternalError] }
            );
            return TypedResults.ValidationProblem(errorResponse.ValidationErrors);
        }
    }

    /// <inheritdoc />
    public async Task<Results<Ok<ExtendedInfoResponse>, ValidationProblem, NotFound>> PostExtendedUserInfoAsync(
        ClaimsPrincipal userPrincipal, 
        ExtendedInfoRequest infoRequest)
    {
        try
        {
            var user = await _userManager.GetUserAsync(userPrincipal);
            if (user == null)
            {
                _logger.LogWarning("PostExtendedUserInfoAsync: User not found from principal");
                return TypedResults.NotFound();
            }

            // Handle email updates
            if (!string.IsNullOrEmpty(infoRequest.NewEmail) && infoRequest.NewEmail != await _userManager.GetEmailAsync(user))
            {
                await _userManager.SetEmailAsync(user, infoRequest.NewEmail);
                await _userManager.SetUserNameAsync(user, infoRequest.NewEmail);
                var userId = await _userManager.GetUserIdAsync(user);
                _logger.LogInformation("Email updated for user {UserId}", userId);
            }

            // Handle password updates
            if (!string.IsNullOrEmpty(infoRequest.NewPassword))
            {
                if (string.IsNullOrEmpty(infoRequest.OldPassword))
                {
                    var userId = await _userManager.GetUserIdAsync(user);
                    _logger.LogWarning("PostExtendedUserInfoAsync: Old password required for user {UserId}", userId);
                    var errorResponse = (ValidationErrorResponse)_communicationService.CreateValidationErrorResponse(
                        new Dictionary<string, string[]> { ["OldPasswordRequired"] = [MessageKeys.Authentication.OldPasswordRequired] }
                    );
                    return TypedResults.ValidationProblem(errorResponse.ValidationErrors);
                }

                var result = await _userManager.ChangePasswordAsync(user, infoRequest.OldPassword, infoRequest.NewPassword);
                if (!result.Succeeded)
                {
                    var userId = await _userManager.GetUserIdAsync(user);
                    _logger.LogWarning("PostExtendedUserInfoAsync: Password change failed for user {UserId}", userId);
                    var errors = TranslateIdentityErrors(result.Errors);
                    return TypedResults.ValidationProblem(errors);
                }
                var passwordChangeUserId = await _userManager.GetUserIdAsync(user);
                _logger.LogInformation("Password changed successfully for user {UserId}", passwordChangeUserId);
            }

            // Return updated user information with 2FA status
            var email = await _userManager.GetEmailAsync(user) ?? "";
            var isEmailConfirmed = await _userManager.IsEmailConfirmedAsync(user);
            var twoFactorEnabled = await _userManager.GetTwoFactorEnabledAsync(user);
            var recoveryCodesCount = await _userManager.CountRecoveryCodesAsync(user);
            var hasAuthenticatorKey = !string.IsNullOrEmpty(await _userManager.GetAuthenticatorKeyAsync(user));

            var response = new ExtendedInfoResponse
            {
                Email = email,
                IsEmailConfirmed = isEmailConfirmed,
                TwoFactorEnabled = twoFactorEnabled,
                RecoveryCodesCount = twoFactorEnabled ? recoveryCodesCount : null,
                HasAuthenticatorKey = hasAuthenticatorKey
            };

            var finalUserId = await _userManager.GetUserIdAsync(user);
            _logger.LogInformation("Extended user info updated successfully for user {UserId}", finalUserId);
            return TypedResults.Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating extended user info");
            var errorResponse = (ValidationErrorResponse)_communicationService.CreateValidationErrorResponse(
                new Dictionary<string, string[]> { ["ExtendedUpdateError"] = [MessageKeys.System.InternalError] }
            );
            return TypedResults.ValidationProblem(errorResponse.ValidationErrors);
        }
    }

    private static ValidationProblem CreateValidationProblem(IdentityResult result)
    {
        return IdentityValidation.CreateValidationProblem(result);
    }

    private static ValidationProblem CreateValidationProblem(string field, string error)
    {
        return IdentityValidation.CreateValidationProblem(field, error);
    }

    /// <summary>
    /// Creates a validation problem from a dictionary of errors
    /// </summary>
    private static ValidationProblem CreateValidationProblem(Dictionary<string, string[]> errors)
    {
        return TypedResults.ValidationProblem(errors);
    }

    /// <summary>
    /// Translates ASP.NET Core Identity errors to localized user-friendly messages
    /// </summary>
    /// <param name="identityErrors">Collection of identity errors</param>
    /// <returns>Dictionary of field names and localized error messages</returns>
    private Dictionary<string, string[]> TranslateIdentityErrors(IEnumerable<IdentityError> identityErrors)
    {
        var errors = new Dictionary<string, string[]>();
        
        foreach (var error in identityErrors)
        {
            var messageKey = error.Code switch
            {
                "PasswordTooShort" => MessageKeys.Registration.PasswordTooWeak,
                "PasswordRequiresDigit" => MessageKeys.Registration.PasswordTooWeak,
                "PasswordRequiresLower" => MessageKeys.Registration.PasswordTooWeak,
                "PasswordRequiresUpper" => MessageKeys.Registration.PasswordTooWeak,
                "PasswordRequiresNonAlphanumeric" => MessageKeys.Registration.PasswordTooWeak,
                "PasswordMismatch" => MessageKeys.Authentication.InvalidCredentials,
                "DuplicateEmail" => MessageKeys.Registration.UserAlreadyExists,
                "InvalidEmail" => MessageKeys.Registration.InvalidEmailFormat,
                "InvalidToken" => MessageKeys.Authentication.TokenInvalid,
                _ => MessageKeys.Validation.InvalidValue
            };

            var fieldName = error.Code switch
            {
                var code when code.Contains("Password") => "Password",
                var code when code.Contains("Email") => "Email",
                var code when code.Contains("Token") => "Token",
                _ => "General"
            };

            if (!errors.ContainsKey(fieldName))
            {
                errors[fieldName] = [];
            }

            var existingErrors = errors[fieldName].ToList();
            existingErrors.Add(_messageService.GetMessage(messageKey));
            errors[fieldName] = existingErrors.ToArray();
        }

        return errors;
    }
}
