using System.Text.RegularExpressions;

namespace IdentityManager.Extensions
{
    /// <summary>
    /// String extension methods for identity management operations
    /// </summary>
    public static class StringExtensions
    {
        /// <summary>
        /// Converts PascalCase or camelCase strings to kebab-case
        /// Example: "AdminPolicy" -> "admin-policy"
        /// </summary>
        /// <param name="input">The input string to convert</param>
        /// <returns>Kebab-case formatted string</returns>
        public static string ToKebabCase(this string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                return input;
            }

            // Insert hyphen before uppercase letters (except the first character)
            var kebabCase = Regex.Replace(input, "(?<!^)([A-Z])", "-$1");
            
            // Convert to lowercase
            return kebabCase.ToLowerInvariant();
        }
    }
}
