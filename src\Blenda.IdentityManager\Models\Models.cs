// using Blenda.Core.DTO.Resources; // COMMENTED: Private package not available
using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IdentityManager.Models
{
    // =============================================================================
    // GDPR COMPLIANCE ENUMS
    // =============================================================================
    
    /// <summary>
    /// GDPR: Types of user consent for granular tracking
    /// </summary>
    public enum ConsentType
    {
        /// <summary>Essential services consent (required)</summary>
        Essential = 1,
        
        /// <summary>Analytics and performance consent (optional)</summary>
        Analytics = 2,
        
        /// <summary>Marketing communications consent (optional)</summary>
        Marketing = 3,
        
        /// <summary>Third-party services consent (optional)</summary>
        ThirdPartyServices = 4,
        
        /// <summary>Data processing for AI/ML purposes (optional)</summary>
        ArtificialIntelligence = 5,
        
        /// <summary>Cross-border data transfer consent (optional)</summary>
        CrossBorderTransfer = 6,
        
        /// <summary>Cookies and tracking consent (required/optional based on type)</summary>
        Cookies = 7
    }
    
    /// <summary>
    /// GDPR: Consent status tracking
    /// </summary>
    public enum ConsentStatus
    {
        /// <summary>User has given explicit consent</summary>
        Granted = 1,
        
        /// <summary>User has explicitly denied consent</summary>
        Denied = 2,
        
        /// <summary>User has withdrawn previously granted consent</summary>
        Withdrawn = 3,
        
        /// <summary>Consent is pending user action</summary>
        Pending = 4,
        
        /// <summary>Consent has expired and needs renewal</summary>
        Expired = 5
    }
    
    /// <summary>
    /// GDPR: Account status for compliance and security monitoring
    /// </summary>
    public enum UserAccountStatus
    {
        /// <summary>Active account in good standing</summary>
        Active = 1,
        
        /// <summary>Account temporarily suspended</summary>
        Suspended = 2,
        
        /// <summary>Account banned for violations</summary>
        Banned = 3,
        
        /// <summary>Account pending email verification</summary>
        PendingVerification = 4,
        
        /// <summary>Account marked for deletion (GDPR right to be forgotten)</summary>
        PendingDeletion = 5,
        
        /// <summary>Account deactivated by user</summary>
        Deactivated = 6,
        
        /// <summary>Account under compliance review</summary>
        UnderReview = 7
    }
    
    /// <summary>
    /// GDPR: Lawful basis for processing personal data under GDPR Article 6
    /// </summary>
    public enum ProcessingLawfulBasis
    {
        /// <summary>Article 6(1)(a) - Consent of the data subject</summary>
        Consent = 1,
        
        /// <summary>Article 6(1)(b) - Performance of a contract</summary>
        Contract = 2,
        
        /// <summary>Article 6(1)(c) - Legal obligation</summary>
        LegalObligation = 3,
        
        /// <summary>Article 6(1)(d) - Vital interests of the data subject</summary>
        VitalInterests = 4,
        
        /// <summary>Article 6(1)(e) - Public task or official authority</summary>
        PublicTask = 5,
        
        /// <summary>Article 6(1)(f) - Legitimate interests</summary>
        LegitimateInterests = 6
    }
    
    /// <summary>
    /// GDPR: Types of data processing activities for audit trail
    /// </summary>
    public enum DataProcessingActivity
    {
        /// <summary>User account creation</summary>
        AccountCreation = 1,
        
        /// <summary>User authentication and login</summary>
        Authentication = 2,
        
        /// <summary>Profile information updates</summary>
        ProfileUpdate = 3,
        
        /// <summary>Data export request processing</summary>
        DataExport = 4,
        
        /// <summary>Data deletion request processing</summary>
        DataDeletion = 5,
        
        /// <summary>Analytics data processing</summary>
        Analytics = 6,
        
        /// <summary>Marketing communications</summary>
        Marketing = 7,
        
        /// <summary>Third-party data sharing</summary>
        ThirdPartySharing = 8,
        
        /// <summary>Audit and compliance activities</summary>
        Audit = 9,
        
        /// <summary>Security monitoring and fraud prevention</summary>
        Security = 10
    }
    
    // =============================================================================
    // GDPR COMPLIANCE MODELS
    // =============================================================================
    
    /// <summary>
    /// GDPR: User consent tracking for comprehensive compliance
    /// </summary>
    public class UserConsent
    {
        /// <summary>Primary key for consent record</summary>
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();
        
        /// <summary>Reference to the user who gave/denied consent</summary>
        [Required]
        public string UserId { get; set; } = string.Empty;
        
        /// <summary>Navigation property to ApplicationUser</summary>
        [ForeignKey(nameof(UserId))]
        public ApplicationUser User { get; set; } = null!;
        
        /// <summary>Type of consent being tracked</summary>
        [Required]
        public ConsentType ConsentType { get; set; }
        
        /// <summary>Current status of the consent</summary>
        [Required]
        public ConsentStatus Status { get; set; }
        
        /// <summary>Timestamp when consent was granted/denied/withdrawn</summary>
        [Required]
        public DateTime ConsentDate { get; set; } = DateTime.UtcNow;
        
        /// <summary>Expiration date for the consent (if applicable)</summary>
        public DateTime? ExpirationDate { get; set; }
        
        /// <summary>IP address hash when consent was given (for audit purposes)</summary>
        [MaxLength(128)]
        public string? IpAddressHash { get; set; }
        
        /// <summary>User agent when consent was given</summary>
        [MaxLength(500)]
        public string? UserAgent { get; set; }
        
        /// <summary>Version of privacy policy/terms when consent was given</summary>
        [MaxLength(50)]
        public string? PolicyVersion { get; set; }
        
        /// <summary>Additional metadata about the consent</summary>
        [MaxLength(1000)]
        public string? Metadata { get; set; }
        
        /// <summary>Source of the consent (web, mobile app, etc.)</summary>
        [MaxLength(100)]
        public string? ConsentSource { get; set; }
        
        /// <summary>Withdrawal reason if consent was withdrawn</summary>
        [MaxLength(500)]
        public string? WithdrawalReason { get; set; }
    }
    
    /// <summary>
    /// GDPR: User privacy settings and preferences
    /// </summary>
    public class UserPrivacySettings
    {
        /// <summary>Primary key for privacy settings</summary>
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();
        
        /// <summary>Reference to the user</summary>
        [Required]
        public string UserId { get; set; } = string.Empty;
        
        /// <summary>Navigation property to ApplicationUser</summary>
        [ForeignKey(nameof(UserId))]
        public ApplicationUser User { get; set; } = null!;
        
        /// <summary>Allow analytics data collection</summary>
        public bool AllowAnalytics { get; set; } = false;
        
        /// <summary>Allow marketing communications</summary>
        public bool AllowMarketing { get; set; } = false;
        
        /// <summary>Allow third-party data sharing</summary>
        public bool AllowThirdPartySharing { get; set; } = false;
        
        /// <summary>Allow cross-border data transfers</summary>
        public bool AllowCrossBorderTransfer { get; set; } = false;
        
        /// <summary>Allow data processing for AI/ML purposes</summary>
        public bool AllowAIProcessing { get; set; } = false;
        
        /// <summary>Preferred data retention period (in days)</summary>
        public int PreferredDataRetentionDays { get; set; } = 2555; // 7 years default
        
        /// <summary>Preferred communication language for GDPR notices</summary>
        [MaxLength(10)]
        public string PreferredLanguage { get; set; } = "en-US";
        
        /// <summary>Frequency for privacy policy updates notifications</summary>
        [MaxLength(50)]
        public string NotificationFrequency { get; set; } = "Immediate";
        
        /// <summary>Last updated timestamp</summary>
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
        
        /// <summary>Settings version for tracking changes</summary>
        [MaxLength(50)]
        public string SettingsVersion { get; set; } = "1.0";
    }
    
    /// <summary>
    /// GDPR: Data processing audit trail for comprehensive compliance
    /// </summary>
    public class DataProcessingRecord
    {
        /// <summary>Primary key for processing record</summary>
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();
        
        /// <summary>Reference to the user whose data was processed</summary>
        [Required]
        public string UserId { get; set; } = string.Empty;
        
        /// <summary>Navigation property to ApplicationUser</summary>
        [ForeignKey(nameof(UserId))]
        public ApplicationUser User { get; set; } = null!;
        
        /// <summary>Type of data processing activity</summary>
        [Required]
        public DataProcessingActivity Activity { get; set; }
        
        /// <summary>Lawful basis for this specific processing activity</summary>
        [Required]
        public ProcessingLawfulBasis LawfulBasis { get; set; }
        
        /// <summary>Timestamp when processing occurred</summary>
        [Required]
        public DateTime ProcessingDate { get; set; } = DateTime.UtcNow;
        
        /// <summary>Description of the data processed</summary>
        [Required]
        [MaxLength(1000)]
        public string DataDescription { get; set; } = string.Empty;
        
        /// <summary>Purpose of the data processing</summary>
        [Required]
        [MaxLength(500)]
        public string ProcessingPurpose { get; set; } = string.Empty;
        
        /// <summary>Data categories involved in processing</summary>
        [MaxLength(500)]
        public string? DataCategories { get; set; }
        
        /// <summary>Third parties involved in processing (if any)</summary>
        [MaxLength(500)]
        public string? ThirdPartiesInvolved { get; set; }
        
        /// <summary>Data retention period for this processing</summary>
        public int RetentionDays { get; set; }
        
        /// <summary>System or service that performed the processing</summary>
        [MaxLength(200)]
        public string ProcessingSystem { get; set; } = string.Empty;
        
        /// <summary>Reference to related consent record (if applicable)</summary>
        public Guid? RelatedConsentId { get; set; }
        
        /// <summary>Navigation property to related consent</summary>
        [ForeignKey(nameof(RelatedConsentId))]
        public UserConsent? RelatedConsent { get; set; }
        
        /// <summary>Additional processing metadata</summary>
        [MaxLength(2000)]
        public string? ProcessingMetadata { get; set; }
        
        /// <summary>Hash of processed data for integrity verification</summary>
        [MaxLength(128)]
        public string? DataHash { get; set; }
    }

    // =============================================================================
    // APPLICATION USER MODEL WITH GDPR COMPLIANCE
    // =============================================================================
    public record RegisterUserDto(string Email, string Password, string FullName, Guid TenantId);
    public record LoginUserDto(string Email, string Password);
    public record CreateRoleDto(string RoleName);
    public record AssignRoleDto(string Email, string RoleName);
    public record CreateTenantDto(string Name, string Subdomain);
    public record CreateSubscriptionDto(string Email, string PlanName, DateTime StartDate, DateTime? EndDate);
    public record UpdateSubscriptionDto(Guid SubscriptionId, string PlanName, DateTime? EndDate, bool IsActive);
    public record ResendConfirmationEmailRequest(string Email);

    public partial class ApplicationUser : IdentityUser
    {
        // =============================================================================
        // GDPR COMPLIANCE PROPERTIES
        // =============================================================================

        /// <summary>
        /// GDPR: User consent records for comprehensive compliance tracking
        /// </summary>
        public List<UserConsent> UserConsents { get; set; } = new();

        /// <summary>
        /// GDPR: Current privacy settings and preferences
        /// </summary>
        public UserPrivacySettings? PrivacySettings { get; set; }

        /// <summary>
        /// GDPR: Data processing audit trail
        /// </summary>
        public List<DataProcessingRecord> DataProcessingRecords { get; set; } = new();

        /// <summary>
        /// GDPR: User's explicit consent to terms of service (required)
        /// </summary>
        public bool HasAcceptedTermsOfService { get; set; } = false;

        /// <summary>
        /// GDPR: Timestamp when terms of service were accepted
        /// </summary>
        public DateTime? TermsOfServiceAcceptedAt { get; set; }

        /// <summary>
        /// GDPR: Version of terms of service accepted by user
        /// </summary>
        public string? TermsOfServiceVersion { get; set; }

        /// <summary>
        /// GDPR: User's explicit consent to privacy policy (required)
        /// </summary>
        public bool HasAcceptedPrivacyPolicy { get; set; } = false;

        /// <summary>
        /// GDPR: Timestamp when privacy policy was accepted
        /// </summary>
        public DateTime? PrivacyPolicyAcceptedAt { get; set; }

        /// <summary>
        /// GDPR: Version of privacy policy accepted by user
        /// </summary>
        public string? PrivacyPolicyVersion { get; set; }

        /// <summary>
        /// GDPR: Data retention period preference (in days)
        /// </summary>
        public int DataRetentionDays { get; set; } = 2555; // Default: 7 years for compliance

        /// <summary>
        /// GDPR: User's right to be forgotten request status
        /// </summary>
        public bool IsMarkedForDeletion { get; set; } = false;

        /// <summary>
        /// GDPR: Timestamp when deletion was requested
        /// </summary>
        public DateTime? DeletionRequestedAt { get; set; }

        /// <summary>
        /// GDPR: Scheduled deletion date (accounting for legal retention requirements)
        /// </summary>
        public DateTime? ScheduledDeletionDate { get; set; }

        /// <summary>
        /// GDPR: Data export request tracking
        /// </summary>
        public DateTime? LastDataExportRequestAt { get; set; }

        /// <summary>
        /// GDPR: User's country for jurisdiction-specific compliance
        /// </summary>
        public string? Country { get; set; }

        /// <summary>
        /// GDPR: EU citizen status for GDPR applicability
        /// </summary>
        public bool IsEUCitizen { get; set; } = false;

        // =============================================================================
        // AUDIT AND COMPLIANCE PROPERTIES
        // =============================================================================

        /// <summary>
        /// Audit: Account creation timestamp (Oracle-optimized)
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Audit: Last account modification timestamp
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Audit: Last successful login timestamp
        /// </summary>
        public DateTime? LastLoginAt { get; set; }

        /// <summary>
        /// Audit: IP address of last login (hashed for privacy)
        /// </summary>
        public string? LastLoginIPHash { get; set; }

        /// <summary>
        /// Audit: User agent of last login (for security monitoring)
        /// </summary>
        public string? LastLoginUserAgent { get; set; }

        /// <summary>
        /// Security: Failed login attempts counter
        /// </summary>
        public int FailedLoginAttempts { get; set; } = 0;

        /// <summary>
        /// Security: Account status for compliance and security
        /// </summary>
        public UserAccountStatus AccountStatus { get; set; } = UserAccountStatus.Active;

        /// <summary>
        /// Security: Account suspension reason
        /// </summary>
        public string? SuspensionReason { get; set; }

        /// <summary>
        /// Security: Account suspension timestamp
        /// </summary>
        public DateTime? SuspendedAt { get; set; }

        /// <summary>
        /// GDPR: Lawful basis for processing personal data
        /// </summary>
        public ProcessingLawfulBasis LawfulBasisForProcessing { get; set; } = ProcessingLawfulBasis.Consent;

        // =============================================================================
        // MULTI-TENANT REFERENCE PROPERTIES
        // =============================================================================

        /// <summary>
        /// Multi-Tenant: Reference to tenant in GRP transactional database
        /// This ID corresponds to a tenant managed by the GRP API
        /// </summary>
        public Guid? TenantId { get; set; }

        /// <summary>
        /// Multi-Tenant: Timestamp when user joined tenant organization
        /// </summary>
        public DateTime? TenantJoinedAt { get; set; }

        /// <summary>
        /// Multi-Tenant: User has administrative privileges within tenant
        /// </summary>
        public bool IsTenantAdmin { get; set; } = false;

        
    }

    /// <summary>
    /// Tenant reference model - for caching tenant info from GRP API
    /// The actual tenant administration is handled by GRP transactional API
    /// </summary>
    public class Tenant
    {
        /// <summary>Tenant ID that corresponds to GRP database tenant</summary>
        public Guid Id { get; set; }
        
        /// <summary>Tenant name (cached from GRP API)</summary>
        public required string Name { get; set; }
        
        /// <summary>Tenant subdomain (cached from GRP API)</summary>
        public required string Subdomain { get; set; }
        
        /// <summary>Last time tenant info was synced from GRP API</summary>
        public DateTime LastSyncAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>Tenant status (cached from GRP API)</summary>
        public bool IsActive { get; set; } = true;
    }


    public class ApiSettings
    {   
        public string PublicFacingService { get; set; } = string.Empty;
        public string InternalService { get; set; } = string.Empty;
    }
}