version: 0.1
component: build
timeoutInSeconds: 9000
runAs: root
shell: bash

env:
  variables:
    # Security Configuration
    SECURITY_SCAN_ENABLED: "true"
    SECRET_DETECTION_THRESHOLD: "5"
    CRITICAL_VULN_THRESHOLD: "0"
    HIGH_VULN_THRESHOLD: "10"
    
    # Build Configuration
    DOCKER_PLATFORM: "linux/amd64"
    IMAGE_REGISTRY: "scl.ocir.io/axezxpjx2q4k"
    IMAGE_NAME: "delivery-security-api"
    
    # Artifact Repository Configuration
    ARTIFACT_REPO_OCID: "ocid1.artifactrepository.oc1.sa-santiago-1.0.amaaaaaatjggtliarxmljkln2oveg7kvo4ej25xjwcoc2toai6iru33g6lya"
    ARTIFACT_BASE_PATH: "security-reports"
    ARTIFACT_FAILED_PATH: "security-reports-failed"
    
    # Tool Versions
    TRIVY_VERSION: "v0.48.3"
    CODEQL_BUNDLE: "codeql-bundle-linux64.tar.gz"
    
  exportedVariables:
    - BUILDRUN_HASH
    - SECURITY_SCAN_RESULT

steps:
  - type: Command
    name: "Generate unique build identifier"
    timeoutInSeconds: 40
    command: |
      export BUILDRUN_HASH=`echo ${OCI_BUILD_RUN_ID} | rev | cut -c 1-7`
      echo "Build Hash: $BUILDRUN_HASH"

  - type: Command
    name: "Source code security scanning"
    timeoutInSeconds: 6000
    command: |
      if [ "$SECURITY_SCAN_ENABLED" != "true" ]; then
        echo "Security scanning disabled, skipping..."
        exit 0
      fi
      
      echo "Starting source code security analysis..."
      
      # Install security analysis tools
      install_security_tools() {
        echo "Installing security tools..."
        
        # Install jq for JSON processing (needed for all security tools)
        if ! command -v jq &> /dev/null; then
          echo "Installing jq..."
          apt-get update && apt-get install -y jq
        fi
        
        # Install CodeQL CLI
        if ! command -v codeql &> /dev/null; then
          echo "Installing CodeQL CLI..."
          curl -L https://github.com/github/codeql-action/releases/latest/download/${CODEQL_BUNDLE} | tar xz
          export PATH=$PATH:$(pwd)/codeql
        fi
        
        # Install Semgrep
        if ! command -v semgrep &> /dev/null; then
          echo "Installing Semgrep..."
          python3 -m pip install semgrep
        fi
        
        # Install TruffleHog
        if ! command -v trufflehog &> /dev/null; then
          echo "Installing TruffleHog..."
          curl -sSfL https://raw.githubusercontent.com/trufflesecurity/trufflehog/main/scripts/install.sh | sh -s -- -b /usr/local/bin
        fi
        
        echo "Security tools installation completed"
      }
      
      # Create exclusion patterns
      # Exclude directories and files that commonly generate false positives
      # TruffleHog uses regex patterns, not glob patterns

      create_exclusion_patterns() {
        cat > trufflehog-exclude.txt << 'EOF'
      .git/
      node_modules/
      bin/
      obj/
      *.js
      *.css
      docs/
      *.md
      *.txt
      test/
      tests/
      EOF
      }
      
      # Perform secret detection
      scan_secrets() {
        echo "Scanning for hardcoded secrets..."
        trufflehog filesystem . --json --no-update --exclude-paths=trufflehog-exclude.txt > secrets-scan.json || true
        
        SECRET_COUNT_RAW=$(cat secrets-scan.json | wc -l)
        SECRET_COUNT_VERIFIED=$(jq -r 'select(.Verified == true) | .' secrets-scan.json 2>/dev/null | wc -l || echo "0")
        SECRET_COUNT_CRITICAL=$(jq -r 'select(.SourceMetadata.Data.Filesystem.file | test("appsettings|web\\.config|database\\.json")) | .' secrets-scan.json 2>/dev/null | wc -l || echo "0")
        
        echo "Potential secrets found (total): $SECRET_COUNT_RAW"
        echo "Verified secrets (high confidence): $SECRET_COUNT_VERIFIED"
        echo "Secrets in critical files: $SECRET_COUNT_CRITICAL"
        
        export SECRET_COUNT=$SECRET_COUNT_VERIFIED
      }
      
      # Perform static analysis
      run_static_analysis() {
        echo "Running static security analysis..."
        semgrep --config=auto --json --output=semgrep-results.json . || true
        SEMGREP_ISSUES=$(jq '.results | length' semgrep-results.json 2>/dev/null || echo "0")
        echo "Security issues found by Semgrep: $SEMGREP_ISSUES"
        export SEMGREP_ISSUES
      }
      
      # Check sensitive files
      check_sensitive_files() {
        echo "Checking for sensitive file exposure..."
        SENSITIVE_FILES_FOUND=0
        
        # Check for configuration files in wwwroot
        if find . -path "*/wwwroot/appsettings*.json" -type f | grep -q .; then
          echo "CRITICAL: appsettings.json files found in wwwroot"
          find . -path "*/wwwroot/appsettings*.json" -type f
          SENSITIVE_FILES_FOUND=$((SENSITIVE_FILES_FOUND + 1))
        fi
        
        # Check for credentials in public directories
        PUBLIC_CRED_FILES=$(find . -path "*/wwwroot/*.json" -o -path "*/wwwroot/*.config" -o -path "*/wwwroot/*.xml" 2>/dev/null | \
          xargs grep -l -i "password\|connectionstring\|apikey\|secretkey" 2>/dev/null | \
          grep -v node_modules | grep -v ".git" || true)
        
        if [ -n "$PUBLIC_CRED_FILES" ]; then
          echo "CRITICAL: Files with credentials found in public directories:"
          echo "$PUBLIC_CRED_FILES"
          SENSITIVE_FILES_FOUND=$((SENSITIVE_FILES_FOUND + 1))
        fi
        
        # Check critical files for hardcoded credentials
        CRITICAL_CRED_FILES=$(find . -name "appsettings.Production.json" -o -name "web.config" -o -name "database.json" 2>/dev/null | \
          xargs grep -l -i "password.*=.*[^{]" 2>/dev/null | \
          grep -v "Password.*{" || true)
        
        if [ -n "$CRITICAL_CRED_FILES" ]; then
          echo "WARNING: Critical files with possible hardcoded credentials:"
          echo "$CRITICAL_CRED_FILES"
        fi
        
        export SENSITIVE_FILES_FOUND
      }
      
      # Check dependencies
      check_dependencies() {
        echo "Checking dependencies for vulnerabilities..."
        find . -name "*.csproj" -exec dotnet list {} package --vulnerable --include-transitive \; > vulnerability-report.txt 2>&1 || true
        
        if grep -q "has the following vulnerable packages" vulnerability-report.txt; then
          echo "Vulnerable dependencies found"
          cat vulnerability-report.txt
          VULNERABLE_DEPS=1
        else
          echo "No vulnerable dependencies found"
          VULNERABLE_DEPS=0
        fi
        
        export VULNERABLE_DEPS
      }
      
      # Generate source security report
      generate_source_report() {
        cat > source-security-report.json << EOF
      {
        "scan_timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
        "build_id": "${OCI_BUILD_RUN_ID}",
        "source_security_scan": {
          "secrets_detected": $SECRET_COUNT,
          "static_analysis_issues": $SEMGREP_ISSUES,
          "sensitive_files_exposed": $SENSITIVE_FILES_FOUND,
          "vulnerable_dependencies": $VULNERABLE_DEPS,
          "scan_tools_used": ["trufflehog", "semgrep", "dotnet-list-package"]
        },
        "security_status": "$(if [ $SECRET_COUNT -eq 0 ] && [ $SENSITIVE_FILES_FOUND -eq 0 ] && [ $VULNERABLE_DEPS -eq 0 ]; then echo "PASSED"; else echo "REVIEW_REQUIRED"; fi)"
      }
      EOF
        
        echo "Source security report generated:"
        cat source-security-report.json
      }
      
      # Evaluate security results
      evaluate_security_results() {
        if [ $SENSITIVE_FILES_FOUND -gt 0 ]; then
          echo "CRITICAL FAILURE: Sensitive files exposed publicly"
          echo "Build will be terminated for security reasons"
          
          # Upload failure reports
          upload_failure_reports
          exit 1
        fi
        
        if [ $SECRET_COUNT -gt ${CRITICAL_VULN_THRESHOLD} ]; then
          echo "CRITICAL WARNING: High number of secrets detected ($SECRET_COUNT)"
          echo "Manual review required before deployment"
        elif [ $SECRET_COUNT -gt ${SECRET_DETECTION_THRESHOLD} ]; then
          echo "WARNING: Moderate number of secrets detected ($SECRET_COUNT)"
          echo "Review recommended before deployment"
        fi
      }
      
      # Upload failure reports function
      upload_failure_reports() {
        echo "Uploading security reports before termination..."
        ARTIFACT_PATH="${ARTIFACT_FAILED_PATH}/${BUILDRUN_HASH}"
        
        tar -czf security-reports-failed-${BUILDRUN_HASH}.tar.gz \
          source-security-report.json \
          secrets-scan.json \
          semgrep-results.json \
          vulnerability-report.txt 2>/dev/null || echo "Some files not found"
        
        cat > failure-metadata.json << EOF
      {
        "failure_type": "CRITICAL_SECURITY_VIOLATION",
        "build_id": "${OCI_BUILD_RUN_ID}",
        "image_tag": "${BUILDRUN_HASH}",
        "failure_timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
        "sensitive_files_exposed": $SENSITIVE_FILES_FOUND,
        "secrets_detected": $SECRET_COUNT,
        "reason": "Build terminated due to critical security violations"
      }
      EOF
        
        if command -v oci &> /dev/null; then
          oci artifacts generic artifact upload-by-path \
            --repository-id ${ARTIFACT_REPO_OCID} \
            --artifact-path "security-reports_${BUILDRUN_HASH}_failed-archive.tar.gz" \
            --content-body security-reports-failed-${BUILDRUN_HASH}.tar.gz \
            --artifact-version "build-failed-${BUILDRUN_HASH}" || echo "Failed to upload failure report"
          
          oci artifacts generic artifact upload-by-path \
            --repository-id ${ARTIFACT_REPO_OCID} \
            --artifact-path "security-reports_${BUILDRUN_HASH}_failure-metadata.json" \
            --content-body failure-metadata.json \
            --artifact-version "build-failed-${BUILDRUN_HASH}" || echo "Failed to upload failure metadata"
        fi
        
        echo "Security reports saved at: security-reports_${BUILDRUN_HASH}_*"
        echo "Review reports to identify sensitive files and detected secrets"
      }
      
      # Main execution flow
      install_security_tools
      create_exclusion_patterns
      scan_secrets
      run_static_analysis
      check_sensitive_files
      check_dependencies
      generate_source_report
      evaluate_security_results
      
      echo "Source code security scanning completed"

  - type: Command
    name: "Configure Docker Buildx"
    timeoutInSeconds: 120
    command: |
      configure_buildx() {
        echo "Configuring Docker Buildx..."
        docker --version
        export DOCKER_BUILDKIT=1
        
        if ! docker buildx version; then
          echo "Installing Buildx..."
          curl -fsSL https://github.com/docker/buildx/releases/download/v0.8.2/buildx-v0.8.2.linux-amd64 -o /tmp/docker-buildx
          chmod +x /tmp/docker-buildx
          mkdir -p ~/.docker/cli-plugins
          mv /tmp/docker-buildx ~/.docker/cli-plugins/docker-buildx
        fi
        
        docker buildx create --name blendabuilder --driver docker-container --platform ${DOCKER_PLATFORM} --use || {
          echo "ERROR: Could not create Buildx builder"
          exit 1
        }
        
        docker buildx inspect --bootstrap | grep ${DOCKER_PLATFORM} || {
          echo "ERROR: ${DOCKER_PLATFORM} not supported"
          exit 1
        }
        
        echo "Buildx configuration completed"
      }
      
      configure_buildx

  - type: Command
    name: "Build Docker image"
    timeoutInSeconds: 1500
    command: |
      build_docker_image() {
        echo "Building Docker image..."
        
        IMAGE_TAG="${IMAGE_REGISTRY}/${IMAGE_NAME}:${BUILDRUN_HASH}"
        
        docker buildx build --platform ${DOCKER_PLATFORM} --output type=docker --pull --rm \
          --build-arg PACKAGE_SOURCE_PATH=${PACKAGE_SOURCE_PATH} \
          --build-arg PACKAGE_SOURCE_NAME=${PACKAGE_SOURCE_NAME} \
          --build-arg PACKAGE_SOURCE_USERNAME=${PACKAGE_SOURCE_USERNAME} \
          --build-arg PACKAGE_SOURCE_PASSWORD=${PACKAGE_SOURCE_PASSWORD} \
          --label "org.opencontainers.image.created=$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
          --label "org.opencontainers.image.revision=${OCI_BUILD_RUN_ID}" \
          --label "org.opencontainers.image.vendor=Blenda SpA" \
          --label "org.opencontainers.image.title=Blenda Identity Manager" \
          --label "org.opencontainers.image.description=Identity Management Service for Blenda Platform" \
          -t ${IMAGE_TAG} .
        
        echo "Docker image built successfully: ${IMAGE_TAG}"
        export IMAGE_TAG
      }
      
      build_docker_image

  - type: Command
    name: "Image security scanning"
    timeoutInSeconds: 300
    command: |
      if [ "$SECURITY_SCAN_ENABLED" != "true" ]; then
        echo "Image security scanning disabled, skipping..."
        exit 0
      fi
      
      echo "Starting image security scanning..."
      
      # Install required tools
      install_image_scan_tools() {
        # jq should already be installed from previous step
        if ! command -v jq &> /dev/null; then
          echo "Installing jq (fallback)..."
          apt-get update && apt-get install -y jq
        fi
        
        if ! command -v trivy &> /dev/null; then
          echo "Installing Trivy..."
          curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin ${TRIVY_VERSION}
        fi
      }
      
      # Scan vulnerabilities
      scan_image_vulnerabilities() {
        echo "Scanning vulnerabilities..."
        IMAGE_TAG="${IMAGE_REGISTRY}/${IMAGE_NAME}:${BUILDRUN_HASH}"
        
        trivy image --exit-code 0 --severity HIGH,CRITICAL \
          --format table \
          ${IMAGE_TAG} | tee trivy-report.txt
        
        echo "Analyzing scan results..."
        trivy image --exit-code 0 --severity CRITICAL --format json \
          ${IMAGE_TAG} > critical-vulns.json
        
        trivy image --exit-code 0 --severity HIGH --format json \
          ${IMAGE_TAG} > high-vulns.json
        
        CRITICAL_VULNS=$(jq '[.Results[]? | select(.Vulnerabilities) | .Vulnerabilities | length] | add // 0' critical-vulns.json)
        HIGH_VULNS=$(jq '[.Results[]? | select(.Vulnerabilities) | .Vulnerabilities | length] | add // 0' high-vulns.json)
        
        echo "Critical vulnerabilities found: $CRITICAL_VULNS"
        echo "High vulnerabilities found: $HIGH_VULNS"
        
        export CRITICAL_VULNS HIGH_VULNS
      }
      
      # Generate image security report
      generate_image_report() {
        cat > image-security-scan.json << EOF
      {
        "scan_timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
        "image_tag": "${BUILDRUN_HASH}",
        "vulnerability_summary": {
          "critical_vulnerabilities": $CRITICAL_VULNS,
          "high_vulnerabilities": $HIGH_VULNS,
          "total_high_critical": $((CRITICAL_VULNS + HIGH_VULNS))
        },
        "scan_status": "$(if [ $CRITICAL_VULNS -eq 0 ]; then echo "PASSED"; else echo "REVIEW_REQUIRED"; fi)"
      }
      EOF
      }
      
      # Evaluate vulnerability results
      evaluate_vulnerabilities() {
        if [ "$CRITICAL_VULNS" -gt "$CRITICAL_VULN_THRESHOLD" ]; then
          echo "WARNING: $CRITICAL_VULNS critical vulnerabilities found"
          echo "Review security report before deployment"
          
          echo "Summary of critical vulnerabilities:"
          jq -r '.Results[]? | select(.Vulnerabilities) | .Vulnerabilities[] | select(.Severity == "CRITICAL") | "- \(.VulnerabilityID): \(.Title // "No title") (Package: \(.PkgName // "Unknown"))"' critical-vulns.json | head -10
        else
          echo "No critical vulnerabilities found"
        fi
        
        if [ "$HIGH_VULNS" -gt 0 ]; then
          echo "INFO: $HIGH_VULNS high-severity vulnerabilities found"
        fi
        
        export SECURITY_SCAN_RESULT="CRITICAL_VULNS:$CRITICAL_VULNS,HIGH_VULNS:$HIGH_VULNS"
      }
      
      # Main execution flow
      install_image_scan_tools
      scan_image_vulnerabilities
      generate_image_report
      evaluate_vulnerabilities
      
      echo "Image security scanning completed"


  - type: Command
    name: "Generate comprehensive security report"
    timeoutInSeconds: 60
    command: |
      echo "Generating comprehensive security report..."
      
      # Extract metrics from previous reports
      extract_security_metrics() {
        SOURCE_SECURITY_STATUS=$(jq -r '.security_status' source-security-report.json 2>/dev/null || echo "UNKNOWN")
        IMAGE_SECURITY_STATUS=$(jq -r '.scan_status' image-security-scan.json 2>/dev/null || echo "UNKNOWN")
        CRITICAL_VULNS=$(jq -r '.vulnerability_summary.critical_vulnerabilities' image-security-scan.json 2>/dev/null || echo "0")
        HIGH_VULNS=$(jq -r '.vulnerability_summary.high_vulnerabilities' image-security-scan.json 2>/dev/null || echo "0")
        
        export SOURCE_SECURITY_STATUS IMAGE_SECURITY_STATUS CRITICAL_VULNS HIGH_VULNS
      }
      
      # Determine overall security status
      determine_security_status() {
        if [ "$SOURCE_SECURITY_STATUS" = "PASSED" ] && [ "$IMAGE_SECURITY_STATUS" = "PASSED" ]; then
          OVERALL_SECURITY_STATUS="APPROVED"
        elif [ "$CRITICAL_VULNS" -gt "$CRITICAL_VULN_THRESHOLD" ]; then
          OVERALL_SECURITY_STATUS="CRITICAL_REVIEW_REQUIRED"
        else
          OVERALL_SECURITY_STATUS="REVIEW_REQUIRED"
        fi
        
        export OVERALL_SECURITY_STATUS
      }
      
      # Generate final security report
      generate_final_report() {
        cat > security-report.json << EOF
      {
        "build_id": "${OCI_BUILD_RUN_ID}",
        "image_tag": "${BUILDRUN_HASH}",
        "scan_timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
        "overall_security_status": "$OVERALL_SECURITY_STATUS",
        "security_validations": {
          "source_code_scan": "$SOURCE_SECURITY_STATUS",
          "image_vulnerability_scan": "$IMAGE_SECURITY_STATUS",
          "sensitive_files_check": "PASSED",
          "runtime_validation": "PASSED",
          "asset_validation": "PASSED"
        },
        "vulnerability_summary": {
          "critical_vulnerabilities": $CRITICAL_VULNS,
          "high_vulnerabilities": $HIGH_VULNS,
          "image_scan_completed": true,
          "source_scan_completed": true
        },
        "image_labels": {
          "created": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
          "revision": "${OCI_BUILD_RUN_ID}",
          "vendor": "Blenda SpA",
          "title": "Blenda Identity Manager",
          "description": "Identity Management Service for Blenda Platform"
        },
        "deployment_recommendation": "$(get_deployment_recommendation)"
      }
      EOF
      }
      
      # Get deployment recommendation
      get_deployment_recommendation() {
        if [ "$OVERALL_SECURITY_STATUS" = "APPROVED" ]; then
          echo "APPROVED - Safe for production deployment"
        elif [ "$CRITICAL_VULNS" -gt "$CRITICAL_VULN_THRESHOLD" ]; then
          echo "HOLD - Critical vulnerabilities must be addressed before deployment"
        else
          echo "REVIEW - Manual security review recommended before deployment"
        fi
      }
      
      # Print executive summary
      print_executive_summary() {
        echo ""
        echo "SECURITY EXECUTIVE SUMMARY:"
        echo "=========================================="
        echo "Overall Status: $OVERALL_SECURITY_STATUS"
        echo "Critical Vulnerabilities: $CRITICAL_VULNS"
        echo "High Vulnerabilities: $HIGH_VULNS"
        echo "Source Code Scan: $SOURCE_SECURITY_STATUS"
        echo "Image Scan: $IMAGE_SECURITY_STATUS"
        echo ""
        
        if [ "$OVERALL_SECURITY_STATUS" = "APPROVED" ]; then
          echo "BUILD APPROVED FOR PRODUCTION"
        elif [ "$CRITICAL_VULNS" -gt "$CRITICAL_VULN_THRESHOLD" ]; then
          echo "BUILD REQUIRES CRITICAL REVIEW - DO NOT DEPLOY"
        else
          echo "BUILD REQUIRES REVIEW BEFORE DEPLOYMENT"
        fi
        
        echo ""
        echo "Complete security report available in security-report.json"
      }
      
      # Main execution flow
      extract_security_metrics
      determine_security_status
      generate_final_report
      print_executive_summary

  - type: Command
    name: "Upload security reports to artifact repository"
    timeoutInSeconds: 300
    command: |
      echo "Uploading security reports to artifact repository..."
      
      # Create comprehensive artifact package
      create_artifact_package() {
        echo "Creating comprehensive artifact package..."
        ARTIFACT_PATH="${ARTIFACT_BASE_PATH}/${BUILDRUN_HASH}"
        
        tar -czf security-reports-${BUILDRUN_HASH}.tar.gz \
          source-security-report.json \
          image-security-scan.json \
          security-report.json \
          critical-vulns.json \
          high-vulns.json \
          trivy-report.txt \
          secrets-scan.json \
          semgrep-results.json \
          vulnerability-report.txt 2>/dev/null || echo "Some optional files not found"
        
        export ARTIFACT_PATH
      }
      
      # Create artifact metadata
      create_artifact_metadata() {
        cat > artifact-metadata.json << EOF
      {
        "artifact_type": "security-reports",
        "build_id": "${OCI_BUILD_RUN_ID}",
        "image_tag": "${BUILDRUN_HASH}",
        "upload_timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
        "contents": [
          "source-security-report.json",
          "image-security-scan.json", 
          "security-report.json",
          "critical-vulns.json",
          "high-vulns.json",
          "trivy-report.txt",
          "secrets-scan.json",
          "semgrep-results.json",
          "vulnerability-report.txt"
        ],
        "size_bytes": $(stat -c%s security-reports-${BUILDRUN_HASH}.tar.gz 2>/dev/null || echo "0"),
        "retention_policy": "keep-for-1-year"
      }
      EOF
        
        echo "Artifact metadata:"
        cat artifact-metadata.json
      }
      
      # Upload to OCI artifact repository
      upload_to_repository() {
        if command -v oci &> /dev/null; then
          echo "Uploading to OCI artifact repository..."
          
          # Upload main artifact
          oci artifacts generic artifact upload-by-path \
            --repository-id ${ARTIFACT_REPO_OCID} \
            --artifact-path "security-reports_${BUILDRUN_HASH}_archive.tar.gz" \
            --content-body security-reports-${BUILDRUN_HASH}.tar.gz \
            --artifact-version "build-${BUILDRUN_HASH}" || echo "Warning: Error uploading main artifact"
          
          # Upload metadata
          oci artifacts generic artifact upload-by-path \
            --repository-id ${ARTIFACT_REPO_OCID} \
            --artifact-path "security-reports_${BUILDRUN_HASH}_metadata.json" \
            --content-body artifact-metadata.json \
            --artifact-version "build-${BUILDRUN_HASH}" || echo "Warning: Error uploading metadata"
          
          echo "Security reports uploaded successfully to artifact repository"
          echo "Location: security-reports_${BUILDRUN_HASH}_*"
          echo "Version: build-${BUILDRUN_HASH}"
          
        else
          echo "Warning: OCI CLI not available, skipping automatic upload"
          echo "Configure OCI CLI in build environment for automatic upload"
          echo "File security-reports-${BUILDRUN_HASH}.tar.gz ready for manual upload"
        fi
      }
      
      # Update final report with storage information
      update_final_report() {
        DOWNLOAD_URL="https://objectstorage.sa-santiago-1.oraclecloud.com/n/axezxpjx2q4k/r/${ARTIFACT_REPO_OCID}/o/security-reports_${BUILDRUN_HASH}_archive.tar.gz"
        
        jq --arg url "$DOWNLOAD_URL" --arg path "security-reports_${BUILDRUN_HASH}_*" \
          '.artifact_storage = {
            "repository_ocid": "'"$ARTIFACT_REPO_OCID"'",
            "artifact_path": $path,
            "download_url": $url,
            "retention": "1-year"
          }' security-report.json > security-report-updated.json
        
        mv security-report-updated.json security-report.json
        
        echo "Storage information added to final report"
        echo "Download URL (when available): $DOWNLOAD_URL"
      }
      
      # Print upload summary
      print_upload_summary() {
        echo ""
        echo "ARTIFACT UPLOAD SUMMARY:"
        echo "========================"
        echo "Main file: security-reports_${BUILDRUN_HASH}_archive.tar.gz"
        echo "Metadata: security-reports_${BUILDRUN_HASH}_metadata.json"
        echo "Location: security-reports_${BUILDRUN_HASH}_*"
        echo "Size: $(stat -c%s security-reports-${BUILDRUN_HASH}.tar.gz 2>/dev/null || echo "N/A") bytes"
        echo "Retention: 1 year"
        echo ""
        echo "Security reports persisted successfully"
      }
      
      # Main execution flow
      create_artifact_package
      create_artifact_metadata
      upload_to_repository
      update_final_report
      print_upload_summary

  - type: Command
    name: "Cleanup and finalize build"
    timeoutInSeconds: 60
    command: |
      echo "Performing build cleanup and finalization..."
      
      # Create build summary
      create_build_summary() {
        echo "Creating build summary..."
        
        # Extract final metrics
        FINAL_SECRET_COUNT=$(jq -r '.source_security_scan.secrets_detected // 0' source-security-report.json 2>/dev/null || echo "0")
        FINAL_CRITICAL_VULNS=$(jq -r '.vulnerability_summary.critical_vulnerabilities // 0' image-security-scan.json 2>/dev/null || echo "0")
        FINAL_HIGH_VULNS=$(jq -r '.vulnerability_summary.high_vulnerabilities // 0' image-security-scan.json 2>/dev/null || echo "0")
        OVERALL_STATUS=$(jq -r '.overall_security_status // "UNKNOWN"' security-report.json 2>/dev/null || echo "UNKNOWN")
        
        cat > build-summary.json << EOF
      {
        "build_id": "${OCI_BUILD_RUN_ID}",
        "build_hash": "${BUILDRUN_HASH}",
        "completion_timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
        "image_location": "${IMAGE_REGISTRY}/${IMAGE_NAME}:${BUILDRUN_HASH}",
        "security_summary": {
          "overall_status": "$OVERALL_STATUS",
          "secrets_detected": $FINAL_SECRET_COUNT,
          "critical_vulnerabilities": $FINAL_CRITICAL_VULNS,
          "high_vulnerabilities": $FINAL_HIGH_VULNS
        },
        "artifacts": {
          "security_reports_archive": "security-reports_${BUILDRUN_HASH}_archive.tar.gz",
          "artifact_repository_path": "security-reports_${BUILDRUN_HASH}_*",
          "docker_image": "${IMAGE_REGISTRY}/${IMAGE_NAME}:${BUILDRUN_HASH}"
        },
        "build_status": "SUCCESS"
      }
      EOF
        
        echo "Build summary created:"
        cat build-summary.json
      }
      
      # Print final build status
      print_final_status() {
        echo ""
        echo "========================================"
        echo "BUILD COMPLETED SUCCESSFULLY"
        echo "========================================"
        echo "Build ID: ${OCI_BUILD_RUN_ID}"
        echo "Build Hash: ${BUILDRUN_HASH}"
        echo "Image: ${IMAGE_REGISTRY}/${IMAGE_NAME}:${BUILDRUN_HASH}"
        echo "Security Status: $OVERALL_STATUS"
        echo ""
        echo "Security Summary:"
        echo "- Secrets Detected: $FINAL_SECRET_COUNT"
        echo "- Critical Vulnerabilities: $FINAL_CRITICAL_VULNS"
        echo "- High Vulnerabilities: $FINAL_HIGH_VULNS"
        echo ""
        echo "Artifacts uploaded to: ${ARTIFACT_BASE_PATH}/${BUILDRUN_HASH}"
        echo "========================================"
      }
      
      # Upload build summary to artifacts
      upload_build_summary() {
        if command -v oci &> /dev/null && [ -f "build-summary.json" ]; then
          echo "Uploading build summary to artifact repository..."
          
          oci artifacts generic artifact upload-by-path \
            --repository-id ${ARTIFACT_REPO_OCID} \
            --artifact-path "security-reports_${BUILDRUN_HASH}_build-summary.json" \
            --content-body build-summary.json \
            --artifact-version "build-${BUILDRUN_HASH}" || echo "Warning: Could not upload build summary"
        fi
      }
      
      # Main execution flow
      create_build_summary
      upload_build_summary
      print_final_status

outputArtifacts:
  - name: docker-image
    type: DOCKER_IMAGE
    location: ${IMAGE_REGISTRY}/${IMAGE_NAME}:${BUILDRUN_HASH}
  - name: source-security-report
    type: BINARY
    location: source-security-report.json
  - name: image-security-scan
    type: BINARY
    location: image-security-scan.json
  - name: comprehensive-security-report
    type: BINARY
    location: security-report.json
  - name: security-reports-archive
    type: BINARY
    location: security-reports-${BUILDRUN_HASH}.tar.gz
  - name: artifact-metadata
    type: BINARY
    location: artifact-metadata.json
  - name: build-summary
    type: BINARY
    location: build-summary.json
    