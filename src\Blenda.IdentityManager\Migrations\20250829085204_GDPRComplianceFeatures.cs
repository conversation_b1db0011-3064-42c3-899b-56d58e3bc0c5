﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace IdentityManager.Migrations
{
    /// <inheritdoc />
    public partial class GDPRComplianceFeatures : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "AccountStatus",
                table: "AspNetUsers",
                type: "NUMBER(10)",
                nullable: false,
                defaultValue: 1);

            migrationBuilder.AddColumn<string>(
                name: "Country",
                table: "AspNetUsers",
                type: "NVARCHAR2(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "AspNetUsers",
                type: "TIMESTAMP(6)",
                nullable: false,
                defaultValueSql: "SYS_EXTRACT_UTC(SYSTIMESTAMP)");

            migrationBuilder.AddColumn<int>(
                name: "DataRetentionDays",
                table: "AspNetUsers",
                type: "NUMBER(10)",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionRequestedAt",
                table: "AspNetUsers",
                type: "TIMESTAMP(6)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "FailedLoginAttempts",
                table: "AspNetUsers",
                type: "NUMBER(10)",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<bool>(
                name: "HasAcceptedPrivacyPolicy",
                table: "AspNetUsers",
                type: "NUMBER(1)",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "HasAcceptedTermsOfService",
                table: "AspNetUsers",
                type: "NUMBER(1)",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsEUCitizen",
                table: "AspNetUsers",
                type: "NUMBER(1)",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsMarkedForDeletion",
                table: "AspNetUsers",
                type: "NUMBER(1)",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastDataExportRequestAt",
                table: "AspNetUsers",
                type: "TIMESTAMP(6)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastLoginAt",
                table: "AspNetUsers",
                type: "TIMESTAMP(6)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LastLoginIPHash",
                table: "AspNetUsers",
                type: "NVARCHAR2(128)",
                unicode: false,
                maxLength: 128,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LastLoginUserAgent",
                table: "AspNetUsers",
                type: "NVARCHAR2(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "LawfulBasisForProcessing",
                table: "AspNetUsers",
                type: "NUMBER(10)",
                nullable: false,
                defaultValue: 1);

            migrationBuilder.AddColumn<DateTime>(
                name: "PrivacyPolicyAcceptedAt",
                table: "AspNetUsers",
                type: "TIMESTAMP(6)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PrivacyPolicyVersion",
                table: "AspNetUsers",
                type: "NVARCHAR2(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ScheduledDeletionDate",
                table: "AspNetUsers",
                type: "TIMESTAMP(6)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "SuspendedAt",
                table: "AspNetUsers",
                type: "TIMESTAMP(6)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SuspensionReason",
                table: "AspNetUsers",
                type: "NVARCHAR2(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "TermsOfServiceAcceptedAt",
                table: "AspNetUsers",
                type: "TIMESTAMP(6)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TermsOfServiceVersion",
                table: "AspNetUsers",
                type: "NVARCHAR2(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedAt",
                table: "AspNetUsers",
                type: "TIMESTAMP(6)",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "UserConsents",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false, defaultValueSql: "SYS_GUID()"),
                    UserId = table.Column<string>(type: "NVARCHAR2(450)", maxLength: 450, nullable: false),
                    ConsentType = table.Column<int>(type: "NUMBER(10)", nullable: false),
                    Status = table.Column<int>(type: "NUMBER(10)", nullable: false),
                    ConsentDate = table.Column<DateTime>(type: "TIMESTAMP(6)", nullable: false, defaultValueSql: "SYS_EXTRACT_UTC(SYSTIMESTAMP)"),
                    ExpirationDate = table.Column<DateTime>(type: "TIMESTAMP(6)", nullable: true),
                    IpAddressHash = table.Column<string>(type: "NVARCHAR2(128)", unicode: false, maxLength: 128, nullable: true),
                    UserAgent = table.Column<string>(type: "NVARCHAR2(500)", maxLength: 500, nullable: true),
                    PolicyVersion = table.Column<string>(type: "NVARCHAR2(50)", unicode: false, maxLength: 50, nullable: true),
                    Metadata = table.Column<string>(type: "NCLOB", maxLength: 1000, nullable: true),
                    ConsentSource = table.Column<string>(type: "NVARCHAR2(100)", maxLength: 100, nullable: true),
                    WithdrawalReason = table.Column<string>(type: "NVARCHAR2(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserConsents", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserConsents_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "UserPrivacySettings",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false, defaultValueSql: "SYS_GUID()"),
                    UserId = table.Column<string>(type: "NVARCHAR2(450)", maxLength: 450, nullable: false),
                    AllowAnalytics = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    AllowMarketing = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    AllowThirdPartySharing = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    AllowCrossBorderTransfer = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    AllowAIProcessing = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    PreferredDataRetentionDays = table.Column<int>(type: "NUMBER(10)", nullable: false, defaultValue: 2555),
                    PreferredLanguage = table.Column<string>(type: "NVARCHAR2(10)", unicode: false, maxLength: 10, nullable: false, defaultValue: "en-US"),
                    NotificationFrequency = table.Column<string>(type: "NVARCHAR2(50)", unicode: false, maxLength: 50, nullable: false, defaultValue: "Immediate"),
                    LastUpdated = table.Column<DateTime>(type: "TIMESTAMP(6)", nullable: false, defaultValueSql: "SYS_EXTRACT_UTC(SYSTIMESTAMP)"),
                    SettingsVersion = table.Column<string>(type: "NVARCHAR2(50)", unicode: false, maxLength: 50, nullable: false, defaultValue: "1.0")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserPrivacySettings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserPrivacySettings_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DataProcessingRecords",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false, defaultValueSql: "SYS_GUID()"),
                    UserId = table.Column<string>(type: "NVARCHAR2(450)", maxLength: 450, nullable: false),
                    Activity = table.Column<int>(type: "NUMBER(10)", nullable: false),
                    LawfulBasis = table.Column<int>(type: "NUMBER(10)", nullable: false),
                    ProcessingDate = table.Column<DateTime>(type: "TIMESTAMP(6)", nullable: false, defaultValueSql: "SYS_EXTRACT_UTC(SYSTIMESTAMP)"),
                    DataDescription = table.Column<string>(type: "NCLOB", maxLength: 1000, nullable: false),
                    ProcessingPurpose = table.Column<string>(type: "NVARCHAR2(500)", maxLength: 500, nullable: false),
                    DataCategories = table.Column<string>(type: "NVARCHAR2(500)", maxLength: 500, nullable: true),
                    ThirdPartiesInvolved = table.Column<string>(type: "NVARCHAR2(500)", maxLength: 500, nullable: true),
                    RetentionDays = table.Column<int>(type: "NUMBER(10)", nullable: false),
                    ProcessingSystem = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false),
                    RelatedConsentId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    ProcessingMetadata = table.Column<string>(type: "NCLOB", maxLength: 2000, nullable: true),
                    DataHash = table.Column<string>(type: "NVARCHAR2(128)", unicode: false, maxLength: 128, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataProcessingRecords", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DataProcessingRecords_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DataProcessingRecords_UserConsents_RelatedConsentId",
                        column: x => x.RelatedConsentId,
                        principalTable: "UserConsents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ApplicationUser_AccountStatus",
                table: "AspNetUsers",
                column: "AccountStatus");

            migrationBuilder.CreateIndex(
                name: "IX_ApplicationUser_Country_IsEUCitizen",
                table: "AspNetUsers",
                columns: new[] { "Country", "IsEUCitizen" });

            migrationBuilder.CreateIndex(
                name: "IX_ApplicationUser_IsEUCitizen",
                table: "AspNetUsers",
                column: "IsEUCitizen");

            migrationBuilder.CreateIndex(
                name: "IX_ApplicationUser_IsMarkedForDeletion",
                table: "AspNetUsers",
                column: "IsMarkedForDeletion");

            migrationBuilder.CreateIndex(
                name: "IX_ApplicationUser_ScheduledDeletionDate",
                table: "AspNetUsers",
                column: "ScheduledDeletionDate",
                filter: "ScheduledDeletionDate IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_DataProcessingRecord_Activity",
                table: "DataProcessingRecords",
                column: "Activity");

            migrationBuilder.CreateIndex(
                name: "IX_DataProcessingRecord_LawfulBasis",
                table: "DataProcessingRecords",
                column: "LawfulBasis");

            migrationBuilder.CreateIndex(
                name: "IX_DataProcessingRecord_ProcessingDate",
                table: "DataProcessingRecords",
                column: "ProcessingDate");

            migrationBuilder.CreateIndex(
                name: "IX_DataProcessingRecord_UserId",
                table: "DataProcessingRecords",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_DataProcessingRecord_UserId_Activity_Date",
                table: "DataProcessingRecords",
                columns: new[] { "UserId", "Activity", "ProcessingDate" });

            migrationBuilder.CreateIndex(
                name: "IX_DataProcessingRecords_RelatedConsentId",
                table: "DataProcessingRecords",
                column: "RelatedConsentId");

            migrationBuilder.CreateIndex(
                name: "IX_UserConsent_ConsentDate",
                table: "UserConsents",
                column: "ConsentDate");

            migrationBuilder.CreateIndex(
                name: "IX_UserConsent_ExpirationDate",
                table: "UserConsents",
                column: "ExpirationDate",
                filter: "ExpirationDate IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_UserConsent_Status",
                table: "UserConsents",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_UserConsent_UserId_ConsentType",
                table: "UserConsents",
                columns: new[] { "UserId", "ConsentType" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserPrivacySettings_UserId",
                table: "UserPrivacySettings",
                column: "UserId",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DataProcessingRecords");

            migrationBuilder.DropTable(
                name: "UserPrivacySettings");

            migrationBuilder.DropTable(
                name: "UserConsents");

            migrationBuilder.DropIndex(
                name: "IX_ApplicationUser_AccountStatus",
                table: "AspNetUsers");

            migrationBuilder.DropIndex(
                name: "IX_ApplicationUser_Country_IsEUCitizen",
                table: "AspNetUsers");

            migrationBuilder.DropIndex(
                name: "IX_ApplicationUser_IsEUCitizen",
                table: "AspNetUsers");

            migrationBuilder.DropIndex(
                name: "IX_ApplicationUser_IsMarkedForDeletion",
                table: "AspNetUsers");

            migrationBuilder.DropIndex(
                name: "IX_ApplicationUser_ScheduledDeletionDate",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "AccountStatus",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "Country",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "DataRetentionDays",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "DeletionRequestedAt",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "FailedLoginAttempts",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "HasAcceptedPrivacyPolicy",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "HasAcceptedTermsOfService",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "IsEUCitizen",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "IsMarkedForDeletion",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "LastDataExportRequestAt",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "LastLoginAt",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "LastLoginIPHash",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "LastLoginUserAgent",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "LawfulBasisForProcessing",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "PrivacyPolicyAcceptedAt",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "PrivacyPolicyVersion",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "ScheduledDeletionDate",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "SuspendedAt",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "SuspensionReason",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "TermsOfServiceAcceptedAt",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "TermsOfServiceVersion",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "UpdatedAt",
                table: "AspNetUsers");
        }
    }
}
