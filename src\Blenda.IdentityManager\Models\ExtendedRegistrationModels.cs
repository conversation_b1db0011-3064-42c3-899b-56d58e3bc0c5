using System.ComponentModel.DataAnnotations;
using ForgotPasswordRequest = Microsoft.AspNetCore.Identity.Data.ForgotPasswordRequest;

namespace IdentityManager.Models;

/// <summary>
/// Extended registration request that includes explicit consent to Terms of Service and Privacy Policy
/// This model ensures GDPR compliance by requiring explicit consent during registration
/// </summary>
public class ExtendedRegisterRequest
{
    /// <summary>
    /// Email address for the user account (required)
    /// </summary>
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email address format")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Password for the user account (required)
    /// </summary>
    [Required(ErrorMessage = "Password is required")]
    [MinLength(8, ErrorMessage = "Password must be at least 8 characters long")]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// User must explicitly accept Terms of Service to register (required for GDPR compliance)
    /// </summary>
    [Required(ErrorMessage = "You must accept the Terms of Service to register")]
    public bool AcceptTermsOfService { get; set; } = false;

    /// <summary>
    /// User must explicitly accept Privacy Policy to register (required for GDPR compliance)
    /// </summary>
    [Required(ErrorMessage = "You must accept the Privacy Policy to register")]
    public bool AcceptPrivacyPolicy { get; set; } = false;

    /// <summary>
    /// Optional: User consent for marketing communications (GDPR Article 7)
    /// </summary>
    public bool AcceptMarketingCommunications { get; set; } = false;

    /// <summary>
    /// Optional: User consent for analytics and performance tracking
    /// </summary>
    public bool AcceptAnalytics { get; set; } = false;

    /// <summary>
    /// Version of Terms of Service being accepted (for audit purposes)
    /// </summary>
    public string? TermsOfServiceVersion { get; set; }

    /// <summary>
    /// Version of Privacy Policy being accepted (for audit purposes)
    /// </summary>
    public string? PrivacyPolicyVersion { get; set; }


    /// <summary>
    /// Optional: Referral code if user was referred by another user or campaign
    /// </summary>
    public string? ReferralCode { get; set; }

    /// <summary>
    /// Optional: Campaign code if user came through a specific marketing campaign
    /// </summary>
    public string? CampaignCode { get; set; }

    /// <summary>
    /// Optional: Source of the registration (e.g. "BlendaTechnology", "DT", "B2B")
    /// </summary>
    public string? Source { get; set; }

    /// <summary>
    /// Optional: Client application name (e.g. "BlendaApp", "BlendaWeb", "BlendaDT")
    /// </summary>
    public string? ClientApp { get; set; }

}

/// <summary>
/// Response model for user registration with GDPR consent information
/// </summary>
public class ExtendedRegisterResponse
{
    /// <summary>
    /// Indicates if registration was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Message about the registration result
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// List of recorded consent IDs for audit purposes
    /// </summary>
    public List<ConsentRecord> RecordedConsents { get; set; } = new();

    /// <summary>
    /// Timestamp when registration was completed
    /// </summary>
    public DateTime RegisteredAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Information about a recorded consent during registration
/// </summary>
public class ConsentRecord
{
    /// <summary>
    /// Unique identifier for the consent record
    /// </summary>
    public Guid ConsentId { get; set; }

    /// <summary>
    /// Type of consent that was recorded
    /// </summary>
    public ConsentType ConsentType { get; set; }

    /// <summary>
    /// Policy version that was accepted
    /// </summary>
    public string? PolicyVersion { get; set; }

    /// <summary>
    /// When the consent was recorded
    /// </summary>
    public DateTime ConsentDate { get; set; } = DateTime.UtcNow;
}


public class ExtendedForgotPasswordRequest
{
    /// <summary>
    /// Email address associated with the user account (required)
    /// </summary>
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email address format")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Optional: Client application name (e.g. "BlendaApp", "BlendaWeb", "BlendaDT")
    /// </summary>
    public string? ClientApp { get; set; }

    /// <summary>
    /// Optional: Source of the password reset request (e.g. "BlendaTechnology", "DT", "B2B")
    /// </summary>
    public string? Source { get; set; }
}

public class ExtendedResetPasswordRequest
{
    
    /// <summary>
    /// The email address for the user requesting a password reset. This should match <see cref="ForgotPasswordRequest.Email"/>.
    /// </summary>
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email address format")]
    public required string Email { get; init; }

    /// <summary>
    /// The code sent to the user's email to reset the password. To get the reset code, first make a "/forgotPassword" request.
    /// </summary>
    [Required(ErrorMessage = "Reset code is required")]
    public required string ResetCode { get; init; }

    /// <summary>
    /// The new password the user with the given <see cref="Email"/> should login with. This will replace the previous password.
    /// </summary>
    [Required(ErrorMessage = "New password is required")]
    public required string NewPassword { get; init; }

    /// <summary>
    /// Optional: Client application name (e.g. "DefaultTenantId")
    /// </summary>
    public string? ClientApp { get; set; }

    /// <summary>
    /// Optional: Source of the password reset request (e.g. "BlendaTechnology", "DT", "B2B")
    /// </summary>
    public string? Source { get; set; }
}

public class ExtendedResendConfirmationEmailRequest
{
    /// <summary>
    /// Email address associated with the user account (required)
    /// </summary>
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email address format")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Optional: Client application name (e.g. "BlendaApp", "BlendaWeb", "BlendaDT")
    /// </summary>
    public string? ClientApp { get; set; }

    /// <summary>
    /// Optional: Source of the request (e.g. "BlendaTechnology", "DT", "B2B")
    /// </summary>
    public string? Source { get; set; }
}

