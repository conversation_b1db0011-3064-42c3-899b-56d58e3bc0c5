using IdentityManager.Models;

namespace IdentityManager.Services.Interfaces;

/// <summary>
/// Interface for Two-Factor Authentication service operations
/// </summary>
/// <typeparam name="TUser">The type of user entity</typeparam>
public interface ITwoFactorService<TUser> where TUser : class
{
    /// <summary>
    /// Sets up Two-Factor Authentication for a user
    /// </summary>
    /// <param name="user">The user to setup 2FA for</param>
    /// <returns>Setup response containing QR code and manual key</returns>
    Task<TwoFactorSetupResponse> SetupTwoFactorAsync(TUser user);

    /// <summary>
    /// Verifies the Two-Factor Authentication setup for a user
    /// </summary>
    /// <param name="user">The user to verify 2FA setup for</param>
    /// <param name="code">The verification code from authenticator app</param>
    /// <returns>Verification response with recovery codes if successful</returns>
    Task<TwoFactorVerifyResponse> VerifyTwoFactorSetupAsync(TUser user, string code);

    /// <summary>
    /// Disables Two-Factor Authentication for a user
    /// </summary>
    /// <param name="user">The user to disable 2FA for</param>
    /// <param name="password">The user's password for verification</param>
    /// <returns>True if disabled successfully, false otherwise</returns>
    Task<bool> DisableTwoFactorAsync(TUser user, string password);

    /// <summary>
    /// Gets the current Two-Factor Authentication status for a user
    /// </summary>
    /// <param name="user">The user to get 2FA status for</param>
    /// <returns>Status response containing current 2FA state</returns>
    Task<TwoFactorStatusResponse> GetTwoFactorStatusAsync(TUser user);

    /// <summary>
    /// Regenerates recovery codes for a user
    /// </summary>
    /// <param name="user">The user to regenerate recovery codes for</param>
    /// <param name="password">The user's password for verification</param>
    /// <returns>Array of new recovery codes</returns>
    Task<string[]> RegenerateRecoveryCodesAsync(TUser user, string password);

    /// <summary>
    /// Validates a Two-Factor Authentication code
    /// </summary>
    /// <param name="user">The user to validate code for</param>
    /// <param name="code">The 2FA code to validate</param>
    /// <returns>True if valid, false otherwise</returns>
    Task<bool> ValidateTwoFactorCodeAsync(TUser user, string code);

    /// <summary>
    /// Validates a recovery code
    /// </summary>
    /// <param name="user">The user to validate recovery code for</param>
    /// <param name="recoveryCode">The recovery code to validate</param>
    /// <returns>True if valid, false otherwise</returns>
    Task<bool> ValidateRecoveryCodeAsync(TUser user, string recoveryCode);
}
