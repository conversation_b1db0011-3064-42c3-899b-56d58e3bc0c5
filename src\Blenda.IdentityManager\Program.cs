
using IdentityManager.Data;
using IdentityManager;
using IdentityManager.Models;
using IdentityManager.Services;
using IdentityManager.Services.Configuration;
using System.Security.Claims;

var builder = WebApplication.CreateBuilder(args);

// SECURITY: Configure application in logical sections
builder.ConfigureEnvironment()
       .ConfigureKestrel()
       .ConfigureLogging()
       .ConfigureDatabase()
       .ConfigureSwagger()
       .ConfigureIdentity()
       .ConfigureAuthentication()
       .ConfigureAuthorization()
       .ConfigureCors()
       .ConfigureApplicationServices();

var app = builder.Build();

// SECURITY: Configure middleware pipeline in logical order
app.ConfigureAdvancedSecurityHeaders()  // ENHANCEMENT: Use advanced security headers middleware
   .ConfigureEnhancedCors()
   .ConfigureDevelopmentMiddleware()
   .ConfigureProductionSecurity()
   .ConfigureMiddlewarePipeline()
   .InitializeDatabase()
   .ConfigureEndpoints();

// SECURITY: Run the application with error handling
try
{
    app.Run();
}
catch (Exception ex)
{
    // Use System.Console instead of disposed service provider
    Console.WriteLine($"Application failed to start: {ex.Message}");
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
    throw;
}
