namespace IdentityManager.Models;

/// <summary>
/// Configuration options for GRP API integration
/// </summary>
public class GrpApiOptions
{
    public const string SectionName = "GrpApi";

    public string BaseUrl { get; set; } = "https://api.blenda.lat";
    public int TimeoutSeconds { get; set; } = 30;
    public int RetryCount { get; set; } = 3;
    public int RetryDelaySeconds { get; set; } = 2;
    public bool EnableSslVerification { get; set; } = true;
    public string? ClientCertificatePath { get; set; }
    public string? ClientCertificatePassword { get; set; }
    public Guid DefaultTenantId { get; set; } = Guid.Parse("550e8400-e29b-41d4-a716-************");
    public Guid DefaultLifecycleState { get; set; } = Guid.Parse("550e8400-e29b-41d4-a716-************");
    public bool EnableDetailedLogging { get; set; } = false;
    public bool EnableHealthChecks { get; set; } = true;
    public int HealthCheckIntervalSeconds { get; set; } = 30;
    public int StatusCheckTimeoutSeconds { get; set; } = 10;
    
    // Additional properties required by GrpResourceService
    public string Environment { get; set; } = "Production";
    public string ApiKey { get; set; } = string.Empty;
}