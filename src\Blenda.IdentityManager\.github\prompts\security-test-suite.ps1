# Security Audit Test Suite - Blenda Core Identity
# Generated by Security Auditor Mode on August 31, 2025

Write-Host "🔒 BLENDA CORE IDENTITY - SECURITY AUDIT TEST SUITE" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

# Test 1: API Availability and Health
Write-Host "`n🏥 Test 1: Health Check Security" -ForegroundColor Yellow
try {
    $healthResponse = Invoke-WebRequest -Uri "http://localhost:5160/health" -UseBasicParsing -TimeoutSec 5
    $healthData = $healthResponse.Content | ConvertFrom-Json
    Write-Host "✅ API Status: $($healthData.status)" -ForegroundColor Green
    Write-Host "✅ Memory Usage: $($healthData.checks[1].data.memory_usage_percent)%" -ForegroundColor Green
    
    # Security Check: Verify no sensitive info in health endpoint
    if ($healthResponse.Content -match "password|secret|key|token") {
        Write-Host "❌ SECURITY RISK: Sensitive data exposed in health endpoint" -ForegroundColor Red
    } else {
        Write-Host "✅ Health endpoint secure - no sensitive data exposed" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ API not available: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "⚠️  Cannot perform live security tests" -ForegroundColor Yellow
    exit 1
}

# Test 2: Authentication Rate Limiting
Write-Host "`n🔐 Test 2: Authentication Rate Limiting Security" -ForegroundColor Yellow
$authResults = @()
for ($i = 1; $i -le 12; $i++) {
    $loginData = @{
        email = "<EMAIL>"
        password = "InvalidPassword123!"
    } | ConvertTo-Json
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:5160/api/auth/login" -Method POST -Body $loginData -ContentType "application/json" -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 429) {
            Write-Host "✅ Rate limiting ACTIVE - Request $i blocked (429)" -ForegroundColor Green
            break
        } else {
            Write-Host "⚠️  Request $i allowed - Status: $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.Value__
        if ($statusCode -eq 429) {
            Write-Host "✅ Rate limiting ACTIVE - Request $i blocked (429)" -ForegroundColor Green
            break
        }
    }
    Start-Sleep -Milliseconds 200
}

# Test 3: GDPR Endpoint Security (requires authentication)
Write-Host "`n🛡️ Test 3: GDPR Endpoint Security" -ForegroundColor Yellow

# Try to access GDPR endpoint without authentication
try {
    $gdprResponse = Invoke-WebRequest -Uri "http://localhost:5160/api/gdpr/privacy/settings" -Method GET -ErrorAction SilentlyContinue
    Write-Host "❌ SECURITY RISK: GDPR endpoint accessible without authentication" -ForegroundColor Red
} catch {
    $statusCode = $_.Exception.Response.StatusCode.Value__
    if ($statusCode -eq 401) {
        Write-Host "✅ GDPR endpoint properly secured - 401 Unauthorized" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Unexpected response: $statusCode" -ForegroundColor Yellow
    }
}

# Test 4: SQL Injection Attempt (should be blocked by EF Core)
Write-Host "`n💉 Test 4: SQL Injection Protection" -ForegroundColor Yellow
$sqlInjectionPayloads = @(
    "admin'; DROP TABLE SecurityAuditLogs; --",
    "' OR '1'='1",
    "'; EXEC xp_cmdshell('dir'); --",
    "admin' UNION SELECT * FROM AspNetUsers --"
)

foreach ($payload in $sqlInjectionPayloads) {
    $injectionData = @{
        email = $payload
        password = "test123"
    } | ConvertTo-Json
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:5160/api/auth/login" -Method POST -Body $injectionData -ContentType "application/json" -ErrorAction SilentlyContinue
        # Any response other than proper validation error is concerning
        if ($response.StatusCode -eq 200) {
            Write-Host "❌ POTENTIAL SQL INJECTION VULNERABILITY" -ForegroundColor Red
        }
    } catch {
        Write-Host "✅ SQL injection blocked - proper input validation" -ForegroundColor Green
    }
}

# Test 5: JWT Security Validation
Write-Host "`n🎫 Test 5: JWT Security Headers" -ForegroundColor Yellow

# Test with invalid JWT
$invalidJWT = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature"
try {
    $jwtResponse = Invoke-WebRequest -Uri "http://localhost:5160/api/gdpr/privacy/settings" -Method GET -Headers @{"Authorization" = $invalidJWT} -ErrorAction SilentlyContinue
    Write-Host "❌ SECURITY RISK: Invalid JWT accepted" -ForegroundColor Red
} catch {
    $statusCode = $_.Exception.Response.StatusCode.Value__
    if ($statusCode -eq 401) {
        Write-Host "✅ Invalid JWT properly rejected - 401 Unauthorized" -ForegroundColor Green
    }
}

# Test 6: Error Message Information Disclosure
Write-Host "`n🚨 Test 6: Error Message Security" -ForegroundColor Yellow

# Test various malformed requests to check error responses
$errorTests = @(
    @{ Endpoint = "/api/auth/login"; Method = "POST"; Body = "invalid json"; ContentType = "application/json" }
    @{ Endpoint = "/api/gdpr/nonexistent"; Method = "GET"; Body = $null; ContentType = $null }
    @{ Endpoint = "/api/auth/login"; Method = "POST"; Body = '{"email":"invalid"}'; ContentType = "application/json" }
)

foreach ($test in $errorTests) {
    try {
        if ($test.Body) {
            $response = Invoke-WebRequest -Uri "http://localhost:5160$($test.Endpoint)" -Method $test.Method -Body $test.Body -ContentType $test.ContentType -ErrorAction SilentlyContinue
        } else {
            $response = Invoke-WebRequest -Uri "http://localhost:5160$($test.Endpoint)" -Method $test.Method -ErrorAction SilentlyContinue
        }
        
        # Check if error responses expose sensitive information
        if ($response.Content -match "stack trace|exception|database|connection|server path") {
            Write-Host "❌ INFORMATION DISCLOSURE: Sensitive data in error response" -ForegroundColor Red
        }
    } catch {
        # This is expected for most error tests
        $errorContent = $_.ErrorDetails.Message
        if ($errorContent -match "stack trace|exception|database|connection|server path") {
            Write-Host "❌ INFORMATION DISCLOSURE: Sensitive data in error response" -ForegroundColor Red
        } else {
            Write-Host "✅ Error responses properly sanitized" -ForegroundColor Green
        }
    }
}

# Test 7: Security Headers Validation
Write-Host "`n🛡️ Test 7: Security Headers Check" -ForegroundColor Yellow
try {
    $headersResponse = Invoke-WebRequest -Uri "http://localhost:5160/health" -Method GET
    $headers = $headersResponse.Headers
    
    $securityHeaders = @(
        "X-Content-Type-Options",
        "X-Frame-Options", 
        "X-XSS-Protection",
        "Strict-Transport-Security"
    )
    
    foreach ($header in $securityHeaders) {
        if ($headers.ContainsKey($header)) {
            Write-Host "✅ $header present" -ForegroundColor Green
        } else {
            Write-Host "⚠️  $header missing - consider adding" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "❌ Could not check security headers" -ForegroundColor Red
}

Write-Host "`n🎯 SECURITY AUDIT COMPLETE" -ForegroundColor Cyan
Write-Host "Review any ❌ or ⚠️ items above for security improvements" -ForegroundColor Cyan
