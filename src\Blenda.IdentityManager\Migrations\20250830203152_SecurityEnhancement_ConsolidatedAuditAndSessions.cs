﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace IdentityManager.Migrations
{
    /// <inheritdoc />
    public partial class SecurityEnhancement_ConsolidatedAuditAndSessions : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ActiveSessionCount",
                table: "AspNetUsers",
                type: "NUMBER(10)",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "ConsecutiveFailedLogins",
                table: "AspNetUsers",
                type: "NUMBER(10)",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastActivityAt",
                table: "AspNetUsers",
                type: "TIMESTAMP(7)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastFailedLoginResetAt",
                table: "AspNetUsers",
                type: "TIMESTAMP(7)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LastIpAddressHash",
                table: "AspNetUsers",
                type: "NVARCHAR2(128)",
                maxLength: 128,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MaxConcurrentSessions",
                table: "AspNetUsers",
                type: "NUMBER(10)",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "SecurityAuditLogs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false, defaultValueSql: "SYS_GUID()"),
                    UserId = table.Column<string>(type: "NVARCHAR2(450)", maxLength: 450, nullable: true),
                    EventType = table.Column<string>(type: "NVARCHAR2(100)", unicode: false, maxLength: 100, nullable: false),
                    Email = table.Column<string>(type: "NVARCHAR2(256)", maxLength: 256, nullable: true),
                    Success = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    FailureReason = table.Column<string>(type: "NVARCHAR2(500)", maxLength: 500, nullable: true),
                    IpAddressHash = table.Column<string>(type: "NVARCHAR2(128)", unicode: false, maxLength: 128, nullable: true),
                    UserAgent = table.Column<string>(type: "NVARCHAR2(1000)", maxLength: 1000, nullable: true),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    SecurityMetadata = table.Column<string>(type: "NCLOB", maxLength: 2000, nullable: true),
                    EventTimestamp = table.Column<DateTime>(type: "TIMESTAMP(6)", nullable: false, defaultValueSql: "SYS_EXTRACT_UTC(SYSTIMESTAMP)"),
                    SessionId = table.Column<string>(type: "NVARCHAR2(128)", unicode: false, maxLength: 128, nullable: true),
                    RiskScore = table.Column<int>(type: "NUMBER(10)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SecurityAuditLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SecurityAuditLogs_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "UserSessions",
                columns: table => new
                {
                    SessionId = table.Column<string>(type: "NVARCHAR2(128)", unicode: false, maxLength: 128, nullable: false),
                    UserId = table.Column<string>(type: "NVARCHAR2(450)", maxLength: 450, nullable: false),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TIMESTAMP(6)", nullable: false, defaultValueSql: "SYS_EXTRACT_UTC(SYSTIMESTAMP)"),
                    LastActivityAt = table.Column<DateTime>(type: "TIMESTAMP(6)", nullable: false),
                    ExpiresAt = table.Column<DateTime>(type: "TIMESTAMP(6)", nullable: false),
                    IsActive = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: true),
                    DeviceInfo = table.Column<string>(type: "NVARCHAR2(500)", maxLength: 500, nullable: true),
                    IpAddressHash = table.Column<string>(type: "NVARCHAR2(128)", unicode: false, maxLength: 128, nullable: true),
                    Location = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: true),
                    TerminationReason = table.Column<string>(type: "NVARCHAR2(100)", maxLength: 100, nullable: true),
                    TerminatedAt = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    RefreshTokenHash = table.Column<string>(type: "NVARCHAR2(128)", unicode: false, maxLength: 128, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserSessions", x => x.SessionId);
                    table.ForeignKey(
                        name: "FK_UserSessions_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SecurityAuditLog_EventTimestamp",
                table: "SecurityAuditLogs",
                column: "EventTimestamp");

            migrationBuilder.CreateIndex(
                name: "IX_SecurityAuditLog_EventType",
                table: "SecurityAuditLogs",
                column: "EventType");

            migrationBuilder.CreateIndex(
                name: "IX_SecurityAuditLog_EventType_Success",
                table: "SecurityAuditLogs",
                columns: new[] { "EventType", "Success" });

            migrationBuilder.CreateIndex(
                name: "IX_SecurityAuditLog_TenantId",
                table: "SecurityAuditLogs",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_SecurityAuditLog_TenantId_EventType",
                table: "SecurityAuditLogs",
                columns: new[] { "TenantId", "EventType" });

            migrationBuilder.CreateIndex(
                name: "IX_SecurityAuditLog_UserId",
                table: "SecurityAuditLogs",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserSession_ExpiresAt",
                table: "UserSessions",
                column: "ExpiresAt",
                filter: "ExpiresAt IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_UserSession_LastActivityAt",
                table: "UserSessions",
                column: "LastActivityAt");

            migrationBuilder.CreateIndex(
                name: "IX_UserSession_TenantId",
                table: "UserSessions",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_UserSession_TenantId_IsActive",
                table: "UserSessions",
                columns: new[] { "TenantId", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_UserSession_UserId",
                table: "UserSessions",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserSession_UserId_IsActive",
                table: "UserSessions",
                columns: new[] { "UserId", "IsActive" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SecurityAuditLogs");

            migrationBuilder.DropTable(
                name: "UserSessions");

            migrationBuilder.DropColumn(
                name: "ActiveSessionCount",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "ConsecutiveFailedLogins",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "LastActivityAt",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "LastFailedLoginResetAt",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "LastIpAddressHash",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "MaxConcurrentSessions",
                table: "AspNetUsers");
        }
    }
}
