using Microsoft.AspNetCore.Identity;
using IdentityManager.Models;
using IdentityManager.Data;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Microsoft.AspNetCore.DataProtection;
using IdentityManager.Services.Core;
using BearerTokenResponse = Microsoft.AspNetCore.Authentication.BearerToken.AccessTokenResponse;

namespace IdentityManager.Services.Core;

/// <summary>
/// Service for JWT token generation, validation, and management
/// </summary>
public class TokenService : ITokenService
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IConfiguration _configuration;
    private readonly ILogger<TokenService> _logger;
    private readonly ISecureDataFormat<AuthenticationTicket> _secureDataFormat;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public TokenService(
        UserManager<ApplicationUser> userManager,
        IConfiguration configuration,
        ILogger<TokenService> logger,
        ISecureDataFormat<AuthenticationTicket> secureDataFormat,
        IHttpContextAccessor httpContextAccessor)
    {
        _userManager = userManager;
        _configuration = configuration;
        _logger = logger;
        _secureDataFormat = secureDataFormat;
        _httpContextAccessor = httpContextAccessor;
    }

    /// <inheritdoc />
    public async Task<BearerTokenResponse> GenerateTokenResponseAsync(ApplicationUser user)
    {

        var userEmail = await _userManager.GetEmailAsync(user) ?? throw new InvalidOperationException("User email is required");
            
            var userId = await _userManager.GetUserIdAsync(user);
            var roles = await _userManager.GetRolesAsync(user);
            var securityStamp = await _userManager.GetSecurityStampAsync(user);

            var expiration = TimeSpan.FromMinutes(int.Parse(_configuration["Jwt:TokenLifetimeInMinutes"] ?? "15"));
            var key = Encoding.UTF8.GetBytes(_configuration["Jwt:Key"] ?? throw new InvalidOperationException("JWT key not configured"));

            // Generate a secure session ID
            var sessionId = GenerateSecureSessionId();

            // Create JWT claims
            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, userId),
                new(ClaimTypes.Email, userEmail),
                new("session_id", sessionId),
                new("security_stamp", securityStamp ?? string.Empty)
            };

            // Add role claims
            foreach (var role in roles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role));
            }

            // Create JWT token
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.Add(expiration),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256),
                Issuer = _configuration["Jwt:Issuer"],
                Audience = _configuration["Jwt:Audience"]
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var token = tokenHandler.CreateToken(tokenDescriptor);
            var tokenString = tokenHandler.WriteToken(token);

            // Create refresh token using ASP.NET Core Data Protection
            // SECURITY: Create authenticated ClaimsIdentity for refresh token
            var refreshIdentity = new ClaimsIdentity(claims, JwtBearerDefaults.AuthenticationScheme, ClaimTypes.NameIdentifier, ClaimTypes.Role);
            
            var refreshTicket = new AuthenticationTicket(
                new ClaimsPrincipal(refreshIdentity),
                new AuthenticationProperties
                {
                    IssuedUtc = DateTimeOffset.UtcNow,
                    ExpiresUtc = DateTimeOffset.UtcNow.AddDays(int.Parse(_configuration["Jwt:RefreshTokenLifetimeInDays"] ?? "30")),
                    IsPersistent = false,
                    AllowRefresh = true
                },
                JwtBearerDefaults.AuthenticationScheme);
            
            var refreshToken = _secureDataFormat.Protect(refreshTicket);

            // Set refresh token as HTTP-only cookie
            SetRefreshTokenCookie(refreshToken);

            _logger.LogDebug("Generated tokens for user {Email}", userEmail);

            return new BearerTokenResponse
            {
                AccessToken = tokenString,
                RefreshToken = refreshToken,
                ExpiresIn = (int)expiration.TotalSeconds
            };
                
    }

    /// <inheritdoc />
    public async Task<ApplicationUser?> ValidateRefreshTokenAsync(string refreshToken)
    {
        try
        {
            var ticket = _secureDataFormat.Unprotect(refreshToken);
            if (ticket?.Properties?.ExpiresUtc is not { } expiresUtc)
            {
                _logger.LogDebug("Refresh token has no expiration time");
                return null;
            }

            if (expiresUtc < DateTimeOffset.UtcNow)
            {
                _logger.LogDebug("Refresh token has expired");
                return null;
            }

            var userIdClaim = ticket.Principal.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim?.Value is not { } userId)
            {
                _logger.LogDebug("Refresh token does not contain user ID");
                return null;
            }

            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                _logger.LogDebug("User not found for refresh token: {UserId}", userId);
                return null;
            }

            // Validate security stamp to ensure token is still valid
            var securityStampClaim = ticket.Principal.FindFirst("security_stamp");
            var currentSecurityStamp = await _userManager.GetSecurityStampAsync(user);
            
            if (securityStampClaim?.Value != currentSecurityStamp)
            {
                _logger.LogDebug("Security stamp validation failed for user {UserId}", userId);
                return null;
            }

            return user;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating refresh token");
            return null;
        }
    }

    /// <inheritdoc />
    public string GenerateSecureSessionId()
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[32];
        rng.GetBytes(bytes);
        return Convert.ToBase64String(bytes);
    }

    /// <inheritdoc />
    public void SetRefreshTokenCookie(string refreshToken)
    {
        try
        {
            var context = _httpContextAccessor.HttpContext;
            if (context == null)
            {
                _logger.LogDebug("No HTTP context available for setting refresh token cookie");
                return;
            }

            bool shouldUseSecureCookies = DetermineCookieSecurityPolicy(context);

            var cookieOptions = new CookieOptions
            {
                HttpOnly = true,
                Secure = shouldUseSecureCookies,
                SameSite = SameSiteMode.Lax,
                Path = "/",
                Expires = DateTimeOffset.UtcNow.AddDays(int.Parse(_configuration["Jwt:RefreshTokenLifetimeInDays"] ?? "30"))
            };

            context.Response.Cookies.Append("refresh_token", refreshToken, cookieOptions);
            _logger.LogDebug("Refresh token cookie set successfully");
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Could not set refresh_token cookie (non-fatal)");
        }
    }

    /// <summary>
    /// Determines whether cookies should be secure based on environment and request context
    /// </summary>
    /// <param name="httpContext">The current HTTP context</param>
    /// <returns>True if cookies should be secure, false otherwise</returns>
    private bool DetermineCookieSecurityPolicy(HttpContext httpContext)
    {
        // Always use secure cookies in production
        var environment = _configuration["ASPNETCORE_ENVIRONMENT"];
        if (environment == "Production")
        {
            return true;
        }

        // In development, check if the request is HTTPS
        return httpContext.Request.IsHttps;
    }
}