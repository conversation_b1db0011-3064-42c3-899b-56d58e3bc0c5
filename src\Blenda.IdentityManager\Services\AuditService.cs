using Microsoft.EntityFrameworkCore;
using Blenda.IdentityManager.Models;
using IdentityManager.Data;
using IdentityManager.Models;

namespace Blenda.IdentityManager.Services;

/// <summary>
/// Service for managing audit logs and security events
/// </summary>
public interface IAuditService
{
    Task LogRequestAsync(ApiAuditEntry auditEntry);
    Task LogSecurityEventAsync(SecurityAuditEntry securityEntry);
    Task LogAuthenticationAttemptAsync(string email, bool success, string? ipAddress, string? reason = null);
    Task LogPasswordResetEventAsync(string email, bool success, string? reason = null);
    Task LogAuthorizationFailureAsync(string userId, string resource, string action, string? reason = null);
    Task<List<SecurityAuditEntry>> GetSecurityEventsAsync(string userId, DateTime? from = null, DateTime? to = null);
}

/// <summary>
/// Implementation of audit service for comprehensive logging
/// </summary>
public class AuditService : IAuditService
{
    private readonly ILogger<AuditService> _logger;
    private readonly IServiceScopeFactory _scopeFactory;

    public AuditService(ILogger<AuditService> logger, IServiceScopeFactory scopeFactory)
    {
        _logger = logger;
        _scopeFactory = scopeFactory;
    }

    public async Task LogRequestAsync(ApiAuditEntry auditEntry)
    {
        try
        {
            using var scope = _scopeFactory.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            // Convert ApiAuditEntry to SecurityAuditLog for unified logging
            var securityAuditLog = new SecurityAuditLog
            {
                Id = Guid.NewGuid(),
                EventType = "API_REQUEST",
                UserId = auditEntry.UserId,
                Success = auditEntry.Success,
                FailureReason = auditEntry.ErrorMessage,
                IpAddressHash = HashIpAddress(auditEntry.IpAddress),
                UserAgent = TruncateUserAgent(auditEntry.UserAgent),
                EventTimestamp = auditEntry.Timestamp,
                RiskScore = CalculateApiRiskScore(auditEntry),
                SecurityMetadata = System.Text.Json.JsonSerializer.Serialize(new
                {
                    Method = auditEntry.Method,
                    Path = auditEntry.Path,
                    StatusCode = auditEntry.StatusCode,
                    ResponseTimeMs = auditEntry.ResponseTimeMs,
                    RequestSize = auditEntry.RequestSize,
                    ResponseContentType = auditEntry.ResponseContentType,
                    Headers = auditEntry.Headers
                })
            };

            context.SecurityAuditLogs.Add(securityAuditLog);
            await context.SaveChangesAsync();

            _logger.LogInformation("API audit logged to SecurityAuditLogs: {Method} {Path} - {StatusCode} ({ResponseTime}ms)",
                auditEntry.Method, auditEntry.Path, auditEntry.StatusCode, auditEntry.ResponseTimeMs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to log API audit entry: {Method} {Path}", auditEntry.Method, auditEntry.Path);
        }
    }

    public async Task LogSecurityEventAsync(SecurityAuditEntry securityEntry)
    {
        try
        {
            using var scope = _scopeFactory.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            await context.Database.ExecuteSqlRawAsync(@"
                INSERT INTO SecurityAudits (EventType, UserId, Success, IpAddress, UserAgent, Timestamp, Reason, SessionId, Resource, Action)
                VALUES ({0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}, {9})",
                securityEntry.EventType,
                securityEntry.UserId,
                securityEntry.Success,
                securityEntry.IpAddress,
                securityEntry.UserAgent,
                securityEntry.Timestamp,
                securityEntry.Reason,
                securityEntry.SessionId,
                securityEntry.Resource,
                securityEntry.Action);

            _logger.LogWarning("Security event logged: {EventType} for user {UserId} - Success: {Success}",
                securityEntry.EventType, securityEntry.UserId, securityEntry.Success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to log security audit entry: {EventType} for user {UserId}",
                securityEntry.EventType, securityEntry.UserId);
        }
    }

    public async Task LogAuthenticationAttemptAsync(string email, bool success, string? ipAddress, string? reason = null)
    {
        var securityEntry = new SecurityAuditEntry
        {
            EventType = "Authentication",
            UserId = email,
            Success = success,
            IpAddress = ipAddress,
            Timestamp = DateTime.UtcNow,
            Reason = reason
        };

        await LogSecurityEventAsync(securityEntry);

        if (success)
        {
            _logger.LogInformation("User authentication successful for {Email} from {IpAddress}", email, ipAddress);
        }
        else
        {
            _logger.LogWarning("User authentication failed for {Email} from {IpAddress}. Reason: {Reason}",
                email, ipAddress, reason ?? "Unknown");
        }
    }

    public async Task LogPasswordResetEventAsync(string email, bool success, string? reason = null)
    {
        var securityEntry = new SecurityAuditEntry
        {
            EventType = "PasswordReset",
            UserId = email,
            Success = success,
            Timestamp = DateTime.UtcNow,
            Reason = reason
        };

        await LogSecurityEventAsync(securityEntry);

        _logger.LogWarning("Password reset {Status} for {Email}. Reason: {Reason}",
            success ? "successful" : "failed", email, reason ?? "Not specified");
    }

    public async Task LogAuthorizationFailureAsync(string userId, string resource, string action, string? reason = null)
    {
        var securityEntry = new SecurityAuditEntry
        {
            EventType = "AuthorizationFailure",
            UserId = userId,
            Success = false,
            Timestamp = DateTime.UtcNow,
            Resource = resource,
            Action = action,
            Reason = reason
        };

        await LogSecurityEventAsync(securityEntry);

        _logger.LogWarning("Authorization failure for user {UserId} accessing {Resource} with action {Action}. Reason: {Reason}",
            userId, resource, action, reason ?? "Access denied");
    }

    public async Task<List<SecurityAuditEntry>> GetSecurityEventsAsync(string userId, DateTime? from = null, DateTime? to = null)
    {
        try
        {
            using var scope = _scopeFactory.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            var fromDate = from ?? DateTime.UtcNow.AddDays(-30);
            var toDate = to ?? DateTime.UtcNow;

            // Note: This would require proper DbSet configuration in ApplicationDbContext
            // For now, return empty list as this is a demonstration
            await Task.CompletedTask; // Make method actually async
            return new List<SecurityAuditEntry>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve security events for user {UserId}", userId);
            return new List<SecurityAuditEntry>();
        }
    }

    // =============================================================================
    // PRIVATE HELPER METHODS FOR API AUDIT PROCESSING
    // =============================================================================

    /// <summary>
    /// Hash IP address for privacy compliance while maintaining audit capability
    /// </summary>
    private string? HashIpAddress(string? ipAddress)
    {
        if (string.IsNullOrEmpty(ipAddress))
            return null;

        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hashBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes($"IP_SALT_{ipAddress}"));
        return Convert.ToBase64String(hashBytes)[..32]; // Take first 32 chars for storage efficiency
    }

    /// <summary>
    /// Truncate User Agent to fit database constraints while preserving key information
    /// </summary>
    private string? TruncateUserAgent(string? userAgent)
    {
        if (string.IsNullOrEmpty(userAgent))
            return null;

        // Keep first 500 characters which usually contain the most relevant information
        return userAgent.Length > 500 ? userAgent[..500] : userAgent;
    }

    /// <summary>
    /// Calculate risk score for API requests based on various factors
    /// </summary>
    private int CalculateApiRiskScore(ApiAuditEntry auditEntry)
    {
        int riskScore = 0;

        // Base score for failed requests
        if (!auditEntry.Success)
            riskScore += 20;

        // Higher risk for authentication endpoints
        if (auditEntry.Path?.Contains("/auth/", StringComparison.OrdinalIgnoreCase) == true)
            riskScore += 10;

        // Risk based on status code
        riskScore += auditEntry.StatusCode switch
        {
            401 => 15, // Unauthorized
            403 => 15, // Forbidden
            429 => 25, // Rate limited
            >= 500 => 10, // Server errors
            _ => 0
        };

        // Risk for very slow responses (potential DoS)
        if (auditEntry.ResponseTimeMs > 5000)
            riskScore += 15;

        // Risk for large requests (potential abuse)
        if (auditEntry.RequestSize > 1024 * 1024) // > 1MB
            riskScore += 10;

        // Ensure score stays within 0-100 range
        return Math.Min(100, Math.Max(0, riskScore));
    }
}
