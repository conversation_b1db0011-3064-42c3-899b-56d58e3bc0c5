# Copilot Instructions - Blenda Core Identity & Access Management System

## Project Architecture Overview

This is an **ASP.NET Core Identity API** (.NET 9.0) application designed for **enterprise-grade identity and access management** with comprehensive security controls, Oracle Autonomous Database 23ai integration, advanced error handling, and multi-factor authentication:

### Documentation Standards Compliance
All project documentation follows a standardized 7-section structure:
1. **Overview** - Purpose, scope, and key features
2. **Implementation Details** - Technical specifications and architecture
3. **Configuration** - Setup, environment variables, and settings
4. **Examples** - Code samples, API usage, and practical demonstrations
5. **Troubleshooting** - Common issues, debugging, and resolution steps
6. **Security Considerations** - Security controls, best practices, and compliance
7. **Support** - Resources, contacts, and additional documentation

When creating or updating documentation, always ensure all 7 sections are present and properly structured.

### Core Architecture Components

#### **`IdentityManager` (ASP.NET Core Identity API)**
- **Purpose**: Centralized identity and access management, user authentication, JWT token management, and secure email workflows
- **Key Files**:
  - `Program.cs`: ASP.NET Core Identity configuration with Oracle integration, JWT authentication, and security middleware
  - `Services/CustomIdentityApi.cs`: Custom identity endpoints with secure email confirmation and authentication flows
  - `Services/JwtAuthenticationMiddleware.cs`: JWT token validation and authentication middleware
  - `Services/RefreshTokenProtector.cs`: Refresh token encryption using ASP.NET Core Data Protection API
  - `Services/EmailSender.cs` & `Services/EmailService.cs`: Secure email service with template validation
  - `Data/ApplicationDbContext.cs`: Entity Framework Core context with Oracle Autonomous Database 23ai integration
- **Security Features**:
  - Oracle Autonomous Database 23ai integration with secure connection string management and wallet authentication
  - Custom JWT authentication with comprehensive token validation
  - Email confirmation with Base64URL-encoded secure tokens
  - Comprehensive ****word policy enforcement
  - Secure error handling with information disclosure prevention
  - Input validation on all authentication endpoints
  - Multi-factor authentication support
  - Audit logging for all identity operations with Oracle 23ai JSON features

### **Critical Architecture Decisions**

#### **Security-First Design Principles**

1. **Sensitive Data Protection for Identity Systems**
   - **Connection String Security**: Oracle Autonomous Database 23ai connection strings stored exclusively in environment variables (NEVER hardcoded)
   - **Rationale**: Prevents database credential exposure in source code or configuration files
   - **Security Benefit**: Separation of identity and business database connections with secure wallet-based authentication
   - **Implementation**: All database connections use environment variable configuration with TNS_ADMIN and wallet security
   - **JWT Security**: Secure JWT signing keys managed via environment variables with proper rotation capabilities

2. **Comprehensive Error Handling and Exception Management for Identity Operations**
   - **Authentication Error Handling**: Centralized exception management for all identity operations with security-conscious responses
   - **Information Disclosure Prevention**: Error messages sanitized to prevent user enumeration and system information exposure
   - **Database Connection Security**: Secure handling of Oracle Autonomous Database 23ai connection failures without exposing connection details
   - **Email Service Resilience**: Graceful degradation when email services are unavailable with proper user feedback

3. **Enterprise Input Validation for Identity Management**
   - **Authentication Endpoint Validation**: Email format validation, ****word complexity validation, and user registration input sanitization
   - **Database Input Validation**: Parameterized queries to prevent SQL injection across all Oracle 23ai database operations
   - **Email Content Validation**: HTML template validation and sanitization to prevent template injection attacks
   - **API Request Validation**: Content-Type validation, request size limits, and JSON deserialization security

4. **Comprehensive Audit Logging and Compliance for Identity Systems**
   - **Security Event Logging**: Authentication attempts, ****word resets, email confirmations, MFA events, and administrative actions
   - **Audit Trail Integrity**: Immutable security logs with tamper protection and correlation capabilities using Oracle 23ai JSON features
   - **Regulatory Compliance**: GDPR compliance for user data processing, SOC 2 controls, and industry-specific requirements
   - **Real-time Monitoring**: Continuous security monitoring with anomaly detection and alerting using Oracle 23ai ML capabilities

5. **Environment-Based Configuration for Identity Services**
   - **Development**: Oracle Autonomous Database 23ai shared instances with development-specific settings
   - **Production**: Secure Oracle Autonomous Database 23ai dedicated instances with production security configurations
   - **Security Benefit**: Prevents hardcoded production credentials in development builds with wallet-based authentication
   - **Email Configuration**: SMTP settings secured via environment variables with proper encryption

6. **Custom Identity Management with ASP.NET Core Integration**
   - **Pattern**: Custom identity endpoints with ASP.NET Core Identity framework integration
   - **Security Features**: Secure user management, ****word policies, email confirmation flows, and MFA support
   - **Database Integration**: Oracle Autonomous Database 23ai backend with Entity Framework Core and secure data access patterns
   - **Error Resilience**: Graceful handling of identity failures with comprehensive logging and user feedback

## Security Implementation Framework

### **Data Protection Implementation for Identity Management**

#### **Secure Connection String and Database Management**
```csharp
// CRITICAL: Always use environment variables for Oracle Autonomous Database 23ai connection strings
// Location: source/Program.cs
var connectionStringIdentity = configuration.GetConnectionString("OracleConnectionIdentity")
    ?? throw new InvalidOperationException("Connection string 'OracleConnectionIdentity' not found.");

var connectionStringGRP = configuration.GetConnectionString("OracleConnectionGRP")
    ?? throw new InvalidOperationException("Connection string 'OracleConnectionGRP' not found.");

// NEVER hardcode connection strings - security vulnerability
// BAD: var connectionString = "Data Source=server;User Id=user;Password=****;";

// Secure Oracle Autonomous Database 23ai configuration with wallet authentication
builder.Services.AddDbContext<ApplicationDbContext>(options => options.UseOracle(connectionStringIdentity,
    oracleOptions => {
        oracleOptions.UseOracleSQLCompatibility(OracleSQLCompatibility.DatabaseVersion23);
        // Enhanced security features for Oracle 23ai
        oracleOptions.CommandTimeout(30);
    }));
```

#### **Environment-Based Configuration for Identity Services**
```csharp
// Location: source/Program.cs and configuration files
// PRODUCTION: Uses secure Oracle cloud connections
// DEVELOPMENT: Uses local development database
builder.Configuration.SetBasePath(AppContext.BaseDirectory);
builder.Configuration.AddEnvironmentVariables(); // CRITICAL: Load from environment

// Security rationale: Prevents hardcoded production database credentials in development builds
var frontendUrl = configuration["ApiSettings:PublicFacingService"] ?? "https://blenda.lat";
```

### **Error Handling and Exception Management for Identity Systems**

#### **Comprehensive Identity Exception Handling Pattern**
```csharp
// Pattern used throughout identity authentication services
// Location: source/Services/CustomIdentityApi.cs
try 
{
    // Email confirmation with secure token validation
    code = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(code));
    
    IdentityResult result = string.IsNullOrEmpty(changedEmail)
        ? await userManager.ConfirmEmailAsync(user, code)
        : await userManager.ChangeEmailAsync(user, changedEmail, code);
        
    if (!result.Succeeded)
    {
        // Secure redirect without exposing internal failure details
        return TypedResults.Redirect($"{frontendUrl}/account/login?error=confirmation-failed");
    }

    return TypedResults.Redirect($"{frontendUrl}/account/login?success=email-confirmed");
}
catch (FormatException)
{
    // Specific error handling for token format issues
    return TypedResults.Redirect($"{frontendUrl}/account/login?error=invalid-code");
}
catch (Exception ex) 
{
    // Generic error handling - never expose internals to user
    _logger.LogError(ex, "Unexpected error during email confirmation for user {UserId}", userId);
    return TypedResults.Redirect($"{frontendUrl}/account/login?error=confirmation-failed");
}
```

#### **Secure Database Connection Error Handling**
```csharp
// Location: source/Program.cs
// Security-conscious database connection validation
var connectionStringIdentity = configuration.GetConnectionString("OracleConnectionIdentity")
    ?? throw new InvalidOperationException("Connection string 'OracleConnectionIdentity' not found.");

// Configure comprehensive logging without exposing sensitive information
builder.Services.AddLogging(logging =>
{
    logging.AddConsole();
    logging.SetMinimumLevel(LogLevel.Warning); // Avoid exposing debug information in production
    logging.AddFilter("Microsoft.IdentityModel", LogLevel.Warning);
});
```

### **Input Validation Implementation for Identity Endpoints**

#### **Comprehensive Identity Input Validation Pattern**
```csharp
// Location: source/Services/CustomIdentityApi.cs
// Email address validation using secure DataAnnotations pattern
private static readonly EmailAddressAttribute _emailAddressAttribute = new();

// Secure user lookup with input validation
if (await userManager.FindByIdAsync(userId) is not { } user)
{
    // Generic error response prevents user enumeration
    return TypedResults.Redirect($"{frontendUrl}/account/login?error=invalid-confirmation");
}

// Secure token validation with proper encoding/decoding
try
{
    code = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(code));
}
catch (FormatException)
{
    // Specific error handling without exposing system internals
    return TypedResults.Redirect($"{frontendUrl}/account/login?error=invalid-code");
}
```

#### **Password Policy and Validation Implementation**
```csharp
// Location: source/Program.cs
// Enterprise-grade ****word policy configuration
builder.Services.AddIdentity<ApplicationUser, IdentityRole>(options =>
{
    // Password security settings - comprehensive requirements
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireNonAlphanumeric = true;
    options.Password.RequireUppercase = true;
    options.Password.RequiredLength = 8;
    options.Password.RequiredUniqueChars = 1;

    // Lockout protection against brute force attacks
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
    options.Lockout.MaxFailedAccessAttempts = 5;
    options.Lockout.AllowedForNewUsers = true;

    // User validation settings
    options.User.AllowedUserNameCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
    options.User.RequireUniqueEmail = true;
});
```
### **Audit Logging and Security Monitoring for Identity Systems**

#### **Security Event Logging Service for Identity Operations**
```csharp
// Location: source/Services/CustomIdentityApi.cs and throughout identity services
public class IdentitySecurityLoggingService
{
    public void LogAuthenticationAttempt(string email, bool success, string? reason = null)
    {
        var logEntry = new IdentitySecurityLogEntry
        {
            EventType = "Authentication",
            Email = email,
            Success = success,
            Reason = reason,
            Timestamp = DateTime.UtcNow,
            IpAddress = GetClientIpAddress(),
            UserAgent = GetUserAgent()
        };
        
        if (success)
        {
            _logger.LogInformation("User authentication successful for {Email}", email);
        }
        else
        {
            _logger.LogWarning("User authentication failed for {Email}. Reason: {Reason}", 
                email, reason ?? "Unknown");
        }
        
        // Store audit trail for compliance and security monitoring
        await _auditService.StoreIdentitySecurityEvent(logEntry);
    }

    public void LogEmailConfirmation(string userId, bool success, string? errorDetails = null)
    {
        _logger.LogInformation("Email confirmation attempt for user {UserId}: {Success}", 
            userId, success ? "Success" : "Failed");
        
        if (!success && !string.IsNullOrEmpty(errorDetails))
        {
            _logger.LogWarning("Email confirmation failed for user {UserId}: {ErrorDetails}", 
                userId, errorDetails);
        }
    }

    public void LogPasswordReset(string email, bool success, string? reason = null)
    {
        _logger.LogInformation("Password reset attempt for {Email}: {Success}", 
            email, success ? "Success" : "Failed");
            
        // Critical security event - always audit ****word resets
        await _auditService.StorePasswordResetEvent(email, success, reason);
    }
}
```

#### **JWT Authentication and Refresh Token Security**
```csharp
// Location: source/Program.cs and source/Services/RefreshTokenProtector.cs
// Configure Data Protection for Refresh Tokens - CRITICAL for security
builder.Services.AddDataProtection()
    .PersistKeysToDbContext<ApplicationDbContext>() // Store keys securely in database
    .SetApplicationName("BlendaIdentityManager");

builder.Services.AddSingleton<ISecureDataFormat<AuthenticationTicket>, RefreshTokenProtector>();

// JWT Bearer authentication configuration with comprehensive security
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = configuration["JwtSettings:Issuer"],
        ValidAudience = configuration["JwtSettings:Audience"],
        IssuerSigningKey = new SymmetricSecurityKey(
            Encoding.UTF8.GetBytes(configuration["JwtSettings:SecretKey"])),
        ClockSkew = TimeSpan.Zero // Reduce clock skew for enhanced security
    };
});
```

## Key Development Patterns

### **Security-First Page Development**

#### **Authentication-Aware Page Template with Error Handling**
```csharp
@page "/account/login"
@rendermode InteractiveWebAssembly  // CRITICAL: Required for all auth pages
@using Microsoft.AspNetCore.Components.Authorization
@using Pfsweb.Services

// Essential service injections for authentication pages
@inject IAuthenticationService AuthService
@inject CustomAuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation
@inject IJSRuntime JS
@inject ISecurityLoggingService SecurityLogging

// Comprehensive error and success state management
@if (!string.IsNullOrEmpty(successMessage))
{
    <div class="alert alert-success" role="alert">
        <i class="fas fa-check-circle me-2"></i>@successMessage
    </div>
}

@if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="alert alert-danger" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>@errorMessage
    </div>
}
```

## Parameterization for Replication

### **Environment Variables Configuration**
```bash
# Required for all project types
JWT_SECRET_KEY=your-256-bit-secret-key
JWT_ISSUER=https://your-domain.com
JWT_AUDIENCE=your-api-audience
JWT_EXPIRY_MINUTES=60
CORS_ALLOWED_ORIGINS=https://your-domain.com
LOG_LEVEL=Warning
ENVIRONMENT=Production

# Health monitoring configuration
HEALTH_CHECK_TIMEOUT_SECONDS=30
MEMORY_LIMIT_MIB=400
CPU_THRESHOLD_PERCENT=80

# Minimal API specific (for API-only projects)
API_VERSION=v1
RATE_LIMIT_REQUESTS_PER_MINUTE=100
MAX_REQUEST_SIZE_MB=10
```

### **Adaptation for Minimal APIs**
```csharp
// Program.cs structure for Minimal API projects
var builder = WebApplication.CreateBuilder(args);

// Security services for API projects
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options => {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["JWT_ISSUER"],
            ValidAudience = builder.Configuration["JWT_AUDIENCE"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["JWT_SECRET_KEY"]))
        };
    });

builder.Services.AddAuthorization();
builder.Services.AddHealthChecks()
    .AddCheck<ApplicationHealthCheck>("application");

// Security services
builder.Services.AddScoped<ISecurityLoggingService, SecurityLoggingService>();
builder.Services.AddScoped<IRateLimitingService, RateLimitingService>();

var app = builder.Build();

// Security middleware
app.UseAuthentication();
app.UseAuthorization();
app.MapHealthChecks("/health");

// API endpoints with security
app.MapGet("/api/secure-endpoint", [Authorize] () => 
{
    return Results.Ok(new { message = "Secure response" });
});
```

## Build & Deployment

### **Development Environment Setup with Identity Management Security Validation**

#### **Local Development Commands**
```powershell
# Standard identity service startup
dotnet run --project source/IdentityManager.csproj
# Identity API serves at http://localhost:5160 (or configured port)

# Build verification without running
dotnet build source/IdentityManager.csproj

# Security validation commands for Identity API
Invoke-WebRequest -Uri "http://localhost:5160/health" -UseBasicParsing
# Should return: 200 OK with health status JSON

# Verify no configuration exposure
Invoke-WebRequest -Uri "http://localhost:5160/appsettings.json" -UseBasicParsing -ErrorAction SilentlyContinue
# Should return: 404 Not Found (configuration files must not be accessible)

# Test identity endpoints (POST examples)
$loginData = @{
    email = "<EMAIL>"
    ****word = "TestPassword123!"
} | ConvertTo-Json
Invoke-WebRequest -Uri "http://localhost:5160/login" -Method POST -Body $loginData -ContentType "application/json"

# Clean build (useful for EF Core migration conflicts)
dotnet clean source/ && dotnet build source/IdentityManager.csproj
```

## Security Reminders and Best Practices for Identity Management

### **Critical Security Rules for Identity Systems**
1. **Sensitive Data Protection**:
   - **Never** hardcode Oracle connection strings in source code
   - **Always** use environment variables for database connections and JWT secrets
   - **Always** implement secure refresh token protection using Data Protection API
   - **Never** expose database credentials or connection details in error messages

2. **Comprehensive Error Handling**:
   - **Always** implement try-catch blocks for all identity operations
   - **Never** expose internal exception details in authentication responses
   - **Always** log security exceptions with proper context for audit trails
   - **Always** provide generic error messages to prevent user enumeration

3. **Enterprise Input Validation**:
   - **Always** validate email formats using DataAnnotations for all identity endpoints
   - **Always** implement secure Base64URL token decoding with proper exception handling
   - **Always** validate ****word complexity according to enterprise policies
   - **Never** trust client-side validation alone - always validate server-side

4. **Identity Audit Logging**:
   - **Always** log authentication attempts (success and failure) with IP tracking
   - **Always** log ****word reset requests and email confirmations
   - **Always** log administrative actions and privilege escalations
   - **Always** maintain immutable audit trails for compliance requirements

5. **Database and API Security**:
   - **Always** use parameterized queries with Entity Framework Core to prevent SQL injection
   - **Always** implement proper JWT token validation with comprehensive security parameters
   - **Always** configure Oracle database connections with appropriate security settings
   - **Never** expose database schema or connection information in API responses

### **Identity Management Development Guidelines**

#### **When Implementing Authentication Flows:**
- Use ASP.NET Core Identity framework with custom extensions for enterprise requirements
- Implement secure email confirmation with Base64URL-encoded tokens and expiration validation
- Configure comprehensive ****word policies with complexity requirements and lockout protection
- Use secure JWT signing and validation with environment-based configuration

#### **When Working with Oracle Database:**
- Use Entity Framework Core with Oracle provider and proper SQL compatibility settings
- Implement secure connection string management via configuration and environment variables
- Use database migrations with proper security validation and version control
- Implement audit logging for all database operations involving user data

#### **When Developing Email Services:**
- Validate and sanitize all email template content to prevent template injection
- Use secure SMTP configuration loaded from environment variables
- Implement rate limiting for email operations to prevent abuse
- Log all email activities for audit purposes without exposing content

This identity management system implements enterprise-grade security through comprehensive error handling, secure data protection, robust input validation, and detailed audit logging to meet regulatory compliance and security standards.

## Replication Instructions for Different Project Types

### **For Blazor WebAssembly Projects**
- Use this guide as-is
- Focus on client-side security model
- Implement sessionStorage token management
- Add comprehensive error handling

### **For Minimal API Projects**
- Replace Blazor authentication with JWT bearer authentication
- Implement API-specific error response formatting
- Add request validation middleware
- Focus on API security patterns and rate limiting

### **For Blazor Server Projects**
- Modify for server-side authentication with cookies
- Implement SignalR security for real-time features
- Add CSRF protection and secure session management

## Development Workflow Enhancement

### Prompt Files and Reusable Commands
This repository supports GitHub Copilot prompt files located in `.github/prompts/` for consistent development workflows:

- **Security Review**: `/security-review` - Analyzes code for security vulnerabilities and audit compliance
- **Documentation**: `/docs-update` - Updates documentation following our 7-section standards
- **Testing**: `/test-coverage` - Generates comprehensive test cases for identity features
- **Compliance**: `/compliance-check` - Validates enterprise compliance requirements
- **Audit Debug**: `/audit-debug` - Analyzes audit logging and SecurityAuditLogs data integrity
- **Database Review**: `/db-review` - Reviews Oracle database schema and Entity Framework migrations
- **API Debug**: `/api-debug` - Comprehensive API endpoint testing and debugging workflows
- **Rate Limiting**: `/rate-limit-check` - Validates rate limiting policies and performance

### Custom Chat Modes
Available custom chat modes for specialized assistance:

#### **Identity DBA Mode**
- Oracle Autonomous Database 23ai administration and performance optimization
- Entity Framework Core migration analysis and troubleshooting with Oracle 23ai features
- SecurityAuditLogs table optimization and indexing strategies for Oracle 23ai
- Connection string security validation and wallet-based authentication configuration
- Oracle 23ai JSON features integration and performance tuning

#### **Security Auditor Mode**
- Comprehensive security analysis with focus on OWASP Top 10
- Audit trail validation and SecurityAuditLogs data integrity verification
- JWT token security analysis and refresh token protection review
- Rate limiting effectiveness and DDoS protection assessment
- Input validation and SQL injection prevention analysis

#### **API Architect Mode**
- RESTful API design patterns and endpoint optimization
- ASP.NET Core Identity integration and custom authentication flows
- Middleware architecture review and performance analysis
- CORS configuration and cross-origin security validation

#### **Documentation Writer Mode**
- Technical writing following our standardized 7-section documentation structure
- API documentation generation with OpenAPI/Swagger integration
- Security documentation for compliance and audit requirements
- Troubleshooting guides with step-by-step resolution procedures

#### **DevOps Engineer Mode**
- Docker containerization and deployment optimization
- Health check configuration and monitoring setup
- Environment variable management and secrets handling
- CI/CD pipeline configuration for secure deployments

### Advanced Debugging Workflows

#### **API Debugging Commands**
```powershell
# Health Check Validation
Invoke-WebRequest -Uri "http://localhost:5160/health" -UseBasicParsing

# Authentication Flow Testing
$loginData = @{
    email = "<EMAIL>"
    ****word = "TestPassword123!"
} | ConvertTo-Json
Invoke-WebRequest -Uri "http://localhost:5160/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"

# GDPR Endpoint Testing
Invoke-WebRequest -Uri "http://localhost:5160/api/gdpr/privacy/settings" -Method GET -Headers @{"Authorization"="Bearer $token"}

# Rate Limiting Validation
for ($i=1; $i -le 35; $i++) {
    Invoke-WebRequest -Uri "http://localhost:5160/api/gdpr/data/export" -Method GET
    Write-Host "Request $i completed"
}
```

#### **Database Audit Analysis**
```sql
-- Recent Security Events Analysis
SELECT TOP 20 
    EventType,
    UserId,
    Success,
    FailureReason,
    IpAddressHash,
    UserAgent,
    SecurityMetadata,
    Timestamp
FROM USER_IDENTITY.SecurityAuditLogs 
ORDER BY Timestamp DESC;

-- Risk Score Analysis
SELECT 
    EventType,
    AVG(RiskScore) as AvgRiskScore,
    COUNT(*) as EventCount,
    COUNT(CASE WHEN Success = 0 THEN 1 END) as FailureCount
FROM USER_IDENTITY.SecurityAuditLogs 
WHERE Timestamp >= DATEADD(hour, -24, GETDATE())
GROUP BY EventType
ORDER BY AvgRiskScore DESC;

-- User Authentication Patterns
SELECT 
    UserId,
    COUNT(*) as LoginAttempts,
    COUNT(CASE WHEN Success = 1 THEN 1 END) as SuccessfulLogins,
    COUNT(CASE WHEN Success = 0 THEN 1 END) as FailedLogins,
    MIN(Timestamp) as FirstAttempt,
    MAX(Timestamp) as LastAttempt
FROM USER_IDENTITY.SecurityAuditLogs 
WHERE EventType IN ('LoginEnhanced', 'API_REQUEST')
    AND Timestamp >= DATEADD(hour, -24, GETDATE())
GROUP BY UserId
ORDER BY FailedLogins DESC;
```

### Code Review Checklist
When reviewing code changes, always check:

#### **Security Validation**
1. **Connection Strings**: All database connections use environment variables (NEVER hardcoded)
2. **JWT Security**: Proper token validation with comprehensive security parameters
3. **Audit Logging**: All security events logged to SecurityAuditLogs with proper risk scoring
4. **Input Validation**: Sanitized inputs and parameterized queries throughout
5. **Rate Limiting**: Appropriate policies for all endpoints with proper sliding window configuration

#### **Architecture Compliance**
6. **Error Handling**: Proper exception management without information disclosure
7. **Entity Framework**: Use EF Core entities instead of raw SQL for maintainability
8. **Middleware Order**: Authentication, authorization, and audit middleware properly sequenced
9. **Environment Configuration**: Development vs Production settings properly separated

#### **Quality Assurance**
10. **Documentation**: Updated documentation following 7-section standards
11. **Testing**: Unit tests for new features and security scenarios
12. **Performance**: Database queries optimized and health checks responsive
13. **Compliance**: GDPR, SOC 2, and enterprise security standards met

### Debugging Decision Matrix

#### **When API Returns Errors:**
1. Check health endpoint first: `/health`
2. Validate environment configuration (.env file)
3. Review SecurityAuditLogs for failed requests
4. Test rate limiting policies if 429 errors occur

#### **When Database Errors Occur:**
1. Verify Oracle connection strings in environment variables
2. Check Entity Framework migrations are up to date
3. Validate SecurityAuditLogs table exists and has proper schema
4. Review audit service configuration for proper EF Core usage

#### **When Authentication Fails:**
1. Verify JWT configuration in environment variables
2. Check user exists in AspNetUsers table
3. Review SecurityAuditLogs for authentication events
4. Validate ****word policies and lockout settings
- Implement server-side session state management
- Add antiforgery tokens for all forms
- Focus on SignalR security

### **For Traditional MVC Projects**
- Adapt for cookie-based authentication
- Implement action-level authorization attributes
- Add comprehensive model validation
- Focus on view state protection

Review `docs/security_audit_metadata.json` for detailed implementation guidance and `docs/SECURITY_AUDIT_REPORT.md` before making security-related changes.

## Custom Chat Mode Activation

To activate specialized chat modes, use these commands:

### **Identity DBA Mode** 
```
I need help with Oracle database and Entity Framework issues. Please switch to Identity DBA mode and focus on:
- Oracle Autonomous Database 23ai schema optimization
- SecurityAuditLogs performance analysis with Oracle 23ai features
- Wallet-based authentication and connection string security validation
- Migration troubleshooting with Oracle 23ai compatibility
- JSON features and ML capabilities integration
```

### **Security Auditor Mode**
```
I need a comprehensive security review. Please switch to Security Auditor mode and analyze:
- OWASP Top 10 compliance
- Audit trail integrity
- JWT token security
- Rate limiting effectiveness
- Input validation security
```

### **API Architect Mode**
```
I need help with API design and architecture. Please switch to API Architect mode and review:
- RESTful endpoint design
- Authentication flow optimization
- Middleware architecture
- CORS and security configurations
```

### **Documentation Writer Mode**
```
I need documentation following our 7-section standards. Please switch to Documentation Writer mode and create:
- Technical documentation with Overview, Implementation, Configuration, Examples, Troubleshooting, Security, Support sections
- API documentation
- Security compliance documentation
```

### **DevOps Engineer Mode**
```
I need help with deployment and operations. Please switch to DevOps Engineer mode and assist with:
- Docker containerization
- Health check monitoring
- Environment configuration
- CI/CD pipeline setup
```

### **Quick Command Reference**
- `/security-review` - Instant security analysis
- `/audit-debug` - SecurityAuditLogs troubleshooting  
- `/api-debug` - API endpoint testing
- `/db-review` - Database schema analysis
- `/rate-limit-check` - Rate limiting validation
- `/docs-update` - Documentation enhancement
- `/test-coverage` - Test case generation
- `/compliance-check` - Regulatory compliance validation
