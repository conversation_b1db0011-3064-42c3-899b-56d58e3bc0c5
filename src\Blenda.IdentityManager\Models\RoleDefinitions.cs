// AUTO-GENERATED: Role definitions for RBAC system
using System.ComponentModel;
using System.Collections.Generic;

namespace IdentityManager.Models
{
    /// <summary>
    /// Definiciones centralizadas de roles y claims para el sistema RBAC
    /// Generado automáticamente - No modificar manualmente
    /// </summary>
    public static class RoleDefinitions
    {
        /// <summary>
        /// Roles disponibles en el sistema
        /// </summary>
        public static class Roles
        {
            // NUEVOS: Roles implementados
            [Description("Administrador de Plataforma - Gestiona infraestructura y configuración")]
            public const string PlatformAdmin = "PlatformAdmin";
            
            [Description("Administrador Empresarial - Gestiona empresa cliente")]
            public const string BusinessAdmin = "BusinessAdmin";
            
            [Description("Gestor de Recursos Humanos - Supervisa datos de asistencia")]
            public const string HRManager = "HRManager";
            
            [Description("Operaciones - Planificación de turnos y cumplimiento de servicios")]
            public const string Operations = "Operations";
            
            [Description("Supervisor - Gestión de equipos y grupos dentro de estructura jerárquica")]
            public const string Supervisor = "Supervisor";
            
            [Description("Auditor Externo - Acceso de solo lectura para auditorías")]
            public const string ExternalAuditor = "ExternalAuditor";
            
            [Description("Inspector Laboral - Supervisión gubernamental de cumplimiento")]
            public const string LaborInspector = "LaborInspector";
            
            [Description("Empleado - Registro de asistencia personal")]
            public const string Employee = "Employee";
            
            [Description("Soporte Técnico - Asistencia técnica limitada")]
            public const string TechnicalSupport = "TechnicalSupport";
            
            // NUEVOS: Roles RTM Críticos para Compliance y Regulatorio
            [Description("Oficial de Cumplimiento - Monitoreo de compliance y métricas")]
            public const string ComplianceOfficer = "ComplianceOfficer";
            
            [Description("Asesor Legal - Interpretación legal y cambios normativos")]
            public const string LegalCounsel = "LegalCounsel";
            
            [Description("Especialista Relaciones DT - Enlace directo con Dirección del Trabajo")]
            public const string DTRelationsSpecialist = "DTRelationsSpecialist";
            
            [Description("Líder Técnico - Supervisión arquitectónica y implementación técnica")]
            public const string TechnicalLead = "TechnicalLead";
            
            // EXISTENTES: Mantener compatibilidad
            [Description("Administrador del sistema")]
            public const string Admin = "Admin";
            
            [Description("Gestor de identidades")]
            public const string IdentityManager = "IdentityManager";
            
            [Description("Suscriptor - Rol legacy en proceso de migración")]
            [System.Obsolete("Use BusinessAdmin instead")]
            public const string Suscriptor = "Suscriptor";
        }

        /// <summary>
        /// Claims utilizados en el sistema
        /// </summary>
        public static class Claims
        {
            // Scopes de acceso
            public const string Scope = "scope";
            public const string Permission = "permission";
            
            // Multi-tenancy
            public const string Tenant = "tenant";
            public const string Department = "department";
            
            // Identificadores específicos
            public const string EmployeeId = "employee.id";
            public const string ContractId = "contract.id";
            
            // Autoridades gubernamentales
            public const string GovernmentAuthority = "government.authority";
            public const string InspectionLicense = "inspection.license";
            
            // Soporte técnico
            public const string SupportLevel = "support.level";
            
            // Auditoría
            public const string AuditAccess = "audit.access";
            public const string AuditFirm = "audit.firm";
            
            // NUEVOS: Claims RTM para Roles Críticos
            public const string Authority = "authority";
            public const string CrossTenant = "cross.tenant";
            public const string DTAuthorized = "dt.authorized";
            public const string ComplianceLevel = "compliance.level";
            public const string LegalPrivilege = "legal.privilege";
        }

        /// <summary>
        /// Scopes específicos por funcionalidad
        /// </summary>
        public static class Scopes
        {
            public const string PlatformManage = "platform.manage";
            public const string TenantManage = "tenant.manage";
            public const string BusinessManage = "business.manage";
            public const string HRManage = "hr.manage";
            public const string OperationsManage = "operations.manage";
            public const string ShiftsManage = "shifts.manage";
            public const string TeamManage = "team.manage";
            public const string EmployeeSupervise = "employee.supervise";
            public const string SupportAccess = "support.access";
            public const string SelfAccess = "self.access";
            public const string GovernmentAudit = "government.audit";
            public const string IdentityManage = "identity.manage";
            public const string ApiAccess = "api.access";
            
            // NUEVOS: Scopes RTM para Compliance y Regulatorio
            public const string ComplianceManage = "compliance.manage";
            public const string LegalInterpret = "legal.interpret";
            public const string DTRelationsManage = "dt.relations.manage";
            public const string TechnicalArchitecture = "technical.architecture";
            public const string CertificationMaintain = "certification.maintain";
            public const string RegulatoryReporting = "regulatory.reporting.coordinate";
            public const string AuditLogsRead = "audit.logs.read";
            public const string ComplianceReportsRead = "compliance.reports.read";
            public const string ComplianceReportsGenerate = "compliance.reports.generate";
        }

        /// <summary>
        /// Jerarquía de roles para herencia de permisos
        /// </summary>
        public static class RoleHierarchy
        {
            public static readonly Dictionary<string, string[]> Hierarchy = new()
            {
                { Roles.PlatformAdmin, new[] { Roles.BusinessAdmin, Roles.HRManager, Roles.Operations, Roles.Supervisor, Roles.TechnicalSupport, Roles.TechnicalLead, Roles.Admin } },
                { Roles.BusinessAdmin, new[] { Roles.HRManager, Roles.Operations, Roles.Supervisor, Roles.Employee, Roles.ComplianceOfficer, Roles.LegalCounsel } },
                { Roles.HRManager, new[] { Roles.Supervisor, Roles.Employee } },
                { Roles.Operations, new[] { Roles.Supervisor, Roles.Employee } },
                { Roles.Supervisor, new[] { Roles.Employee } },
                { Roles.ComplianceOfficer, new[] { Roles.ExternalAuditor } },
                { Roles.LegalCounsel, new[] { Roles.ComplianceOfficer } },
                { Roles.DTRelationsSpecialist, new[] { Roles.ComplianceOfficer, Roles.LegalCounsel } },
                { Roles.TechnicalLead, new[] { Roles.TechnicalSupport } },
                { Roles.Admin, new[] { Roles.IdentityManager } }
            };
        }
    }
}
