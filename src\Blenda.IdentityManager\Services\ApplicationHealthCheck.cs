using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace IdentityManager.Services
{
    /// <summary>
    /// Application health check for Blenda Core Identity & Access Management
    /// Includes memory usage, CPU usage, and system metrics monitoring
    /// </summary>
    public class ApplicationHealthCheck : IHealthCheck
    {
        private readonly IWebHostEnvironment _environment;
        private readonly IConfiguration _configuration;

        public ApplicationHealthCheck(IWebHostEnvironment environment, IConfiguration configuration)
        {
            _environment = environment;
            _configuration = configuration;
        }

        public Task<HealthCheckResult> CheckHealthAsync(
            HealthCheckContext context,
            CancellationToken cancellationToken = default)
        {
            try
            {
                // Basic application health checks
                var healthData = new Dictionary<string, object>
                {
                    ["environment"] = _environment.EnvironmentName,
                    ["application"] = "Blenda Core Identity & Access Management",
                    ["version"] = "*******",
                    ["timestamp"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                    ["uptime_ms"] = Environment.TickCount64
                };

                // Check if running in container (safe to expose as boolean only)
                healthData["container"] = Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") == "true";

                // Memory usage monitoring
                var workingSet = Environment.WorkingSet;
                healthData["memory_usage_bytes"] = workingSet;

                // Get memory limits from environment variables (set by Kubernetes or Docker)
                var memLimitStr = Environment.GetEnvironmentVariable("MEMORY_LIMIT_MIB") 
                    ?? Environment.GetEnvironmentVariable("MEMORY_LIMIT") // fallback for custom env
                    ?? "400"; // default to 400 MiB if not set

                if (!int.TryParse(memLimitStr.Replace("Mi", "").Replace("MiB", ""), out var memLimitMiB))
                    memLimitMiB = 400;

                var memLimitBytes = memLimitMiB * 1024 * 1024;
                healthData["memory_limit_bytes"] = memLimitBytes;
                healthData["memory_limit_mib"] = memLimitMiB;

                // Calculate memory usage percentage
                var memoryUsagePercent = (double)workingSet / memLimitBytes;
                healthData["memory_usage_percent"] = Math.Round(memoryUsagePercent * 100, 2);
                healthData["memory_usage_mib"] = Math.Round((double)workingSet / (1024 * 1024), 2);

                // CPU usage estimation
                var process = System.Diagnostics.Process.GetCurrentProcess();
                var totalCpuTime = process.TotalProcessorTime.TotalMilliseconds;
                var uptimeMs = Math.Max(1, Environment.TickCount64); // avoid divide by zero
                
                // Calculate CPU usage as percentage
                var cpuUsageRatio = totalCpuTime / uptimeMs;
                var cpuUsagePercent = Math.Round(cpuUsageRatio * 100, 2);
                healthData["cpu_usage_percent"] = cpuUsagePercent;
                healthData["cpu_total_time_ms"] = totalCpuTime;

                // Get CPU threshold from environment (default 80%)
                var cpuThresholdStr = Environment.GetEnvironmentVariable("CPU_THRESHOLD_PERCENT") ?? "80";
                if (!double.TryParse(cpuThresholdStr, out var cpuThreshold))
                    cpuThreshold = 80.0;

                healthData["cpu_threshold_percent"] = cpuThreshold;

                // Additional system metrics for Identity Management
                healthData["thread_count"] = process.Threads.Count;
                healthData["handle_count"] = process.HandleCount;
                healthData["gc_gen0_collections"] = GC.CollectionCount(0);
                healthData["gc_gen1_collections"] = GC.CollectionCount(1);
                healthData["gc_gen2_collections"] = GC.CollectionCount(2);

                // Check for degraded performance conditions
                var warnings = new List<string>();

                // Memory warning threshold (80% of limit)
                if (memoryUsagePercent > 0.8)
                {
                    warnings.Add($"Memory usage high: {healthData["memory_usage_percent"]}% of {memLimitMiB}MiB limit");
                }

                // CPU warning threshold (configurable, default 80%)
                if (cpuUsagePercent > cpuThreshold)
                {
                    warnings.Add($"CPU usage high: {cpuUsagePercent}% (threshold: {cpuThreshold}%)");
                }

                // Thread count warning (high thread count may indicate issues)
                if (process.Threads.Count > 100)
                {
                    warnings.Add($"High thread count: {process.Threads.Count} threads");
                }

                // GC pressure warning (frequent Gen2 collections)
                var gen2Collections = GC.CollectionCount(2);
                if (gen2Collections > 50) // arbitrary threshold
                {
                    warnings.Add($"High GC pressure: {gen2Collections} Gen2 collections");
                }

                // Return health status based on warnings
                if (warnings.Any())
                {
                    var warningMessage = string.Join("; ", warnings);
                    return Task.FromResult(HealthCheckResult.Degraded(
                        $"Performance warnings detected: {warningMessage}",
                        null, healthData));
                }

                // Additional check for critical conditions
                if (memoryUsagePercent > 0.95)
                {
                    return Task.FromResult(HealthCheckResult.Unhealthy(
                        $"Critical memory usage: {healthData["memory_usage_percent"]}% of limit",
                        null, healthData));
                }

                if (cpuUsagePercent > 95)
                {
                    return Task.FromResult(HealthCheckResult.Unhealthy(
                        $"Critical CPU usage: {cpuUsagePercent}%",
                        null, healthData));
                }

                return Task.FromResult(HealthCheckResult.Healthy(
                    $"Identity Management system healthy - Memory: {healthData["memory_usage_percent"]}%, CPU: {cpuUsagePercent}%",
                    healthData));
            }
            catch (Exception ex)
            {
                // Log the exception details but don't expose them in health check response
                var errorData = new Dictionary<string, object>
                {
                    ["error"] = "Health check execution failed",
                    ["timestamp"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ")
                };

                return Task.FromResult(HealthCheckResult.Unhealthy(
                    "Application health check failed - see logs for details", 
                    ex, errorData));
            }
        }
    }
}
