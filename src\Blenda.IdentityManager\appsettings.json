{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Warning", "Microsoft.IdentityModel": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"OracleConnectionGRP": "PLACEHOLDER_FOR_ENVIRONMENT_VARIABLE", "OracleConnectionIdentity": "PLACEHOLDER_FOR_ENVIRONMENT_VARIABLE"}, "Jwt": {"Key": "PLACEHOLDER_FOR_ENVIRONMENT_VARIABLE", "Issuer": "https://identity.blenda.lat", "Audience": "https://identity.blenda.lat", "TokenLifetimeInMinutes": 15, "Lifetime": "0.00:15:00", "RefreshTokenLifetime": "7.00:00:00", "RefreshTokenSlidingExpiration": true, "RefreshTokenAbsoluteExpiration": true}, "Email": {"UseGraphApi": true, "From": "<EMAIL>", "FromName": "Blenda Identity & Access Management"}, "AzureAd": {"TenantId": "PLACEHOLDER_FOR_ENVIRONMENT_VARIABLE", "ClientId": "PLACEHOLDER_FOR_ENVIRONMENT_VARIABLE", "ClientSecret": "PLACEHOLDER_FOR_ENVIRONMENT_VARIABLE"}, "ApiSettings": {"PublicFacingService": "https://www.blenda.lat", "InternalService": "https://identity.blenda.lat"}, "GrpApi": {"BaseUrl": "https://api.blenda.lat", "TimeoutSeconds": 30, "ApiKey": "PLACEHOLDER_FOR_ENVIRONMENT_VARIABLE", "DefaultTenantId": "550e8400-e29b-41d4-a716-446655440000", "Environment": "Development", "EnableRetry": true, "MaxRetryAttempts": 3, "RetryDelayMs": 1000, "EnableAuditLogging": true, "LogLevel": "Information"}, "GRP": {"DefaultTenantId": "blenda-corp"}, "Security": {"RequireHttps": true, "CookieSecure": true, "SameSiteMode": "Strict"}}