using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using IdentityManager.Services.Handlers;
using IdentityManager.Models;
using IdentityManager.Services;
using Microsoft.AspNetCore.Identity.Data;
using System.Security.Claims;
using Microsoft.AspNetCore.Http.HttpResults;

using LoginRequest = Microsoft.AspNetCore.Identity.Data.LoginRequest;
using RegisterRequest = Microsoft.AspNetCore.Identity.Data.RegisterRequest;
using RefreshRequest = Microsoft.AspNetCore.Identity.Data.RefreshRequest;
using ForgotPasswordRequest = Microsoft.AspNetCore.Identity.Data.ForgotPasswordRequest;
using ResetPasswordRequest = Microsoft.AspNetCore.Identity.Data.ResetPasswordRequest;
using ResendConfirmationEmailRequest = IdentityManager.Models.ResendConfirmationEmailRequest;

namespace IdentityManager.Services.Configuration;

/// <summary>
/// Configures authentication endpoints with proper documentation and handlers
/// </summary>
public static class AuthenticationEndpoints
{
    /// <summary>
    /// Maps authentication endpoints (login, logout, token refresh)
    /// </summary>
    public static RouteGroupBuilder MapAuthenticationEndpoints<TUser>(this RouteGroupBuilder group)
        where TUser : class, new()
    {

        group.MapPost("/login", async (
            [FromBody] EnhancedLoginRequest request,
            [FromServices] IUserAuthenticationHandler authHandler) =>
        {
            return await authHandler.LoginEnhancedAsync(request);
        })
        .WithName("Login")
        .WithSummary("Enhanced login with Two-Factor Authentication support")
        .WithDescription("Authenticates a user with optional 2FA support. Returns JWT access token and refresh token or 2FA requirement.")
        .WithTags("Authentication")
        .AllowAnonymous();

        // Recovery code login
        group.MapPost("/recovery-code-login", async (
            [FromBody] RecoveryCodeLoginRequest request,
            [FromServices] IUserAuthenticationHandler authHandler) =>
        {
            return await authHandler.RecoveryCodeLoginAsync(request);
        })
        .WithName("RecoveryCodeLogin")
        .WithSummary("Login using Two-Factor Authentication recovery code")
        .WithDescription("Authenticates a user using a recovery code when 2FA device is unavailable.")
        .WithTags("Authentication")
        .AllowAnonymous();

        // Token refresh
        group.MapPost("/refresh-token", async (
            HttpContext context,
            [FromServices] IUserAuthenticationHandler authHandler) =>
        {
            // Crear RefreshRequest manualmente sin model binding problemático
            var refreshToken = await TryGetRefreshTokenAsync(context);
            var request = new RefreshRequest { RefreshToken = refreshToken ?? "" };

            return await authHandler.RefreshTokenAsync(request);
        })
        .WithName("RefreshToken")
        .WithSummary("Refresh access token")
        .WithDescription("Generates a new access token using a valid refresh token.")
        .WithTags("Authentication")
        .AllowAnonymous();
        
        static async Task<string?> TryGetRefreshTokenAsync(HttpContext context)
        {
            // Intentar JSON body primero
            try 
            {
                var body = await context.Request.ReadFromJsonAsync<Dictionary<string, object?>>();
                var token = body?.GetValueOrDefault("refreshToken")?.ToString();
                if (!string.IsNullOrEmpty(token)) return token;
            }
            catch { }
            
            // Fallback a cookie
            return context.Request.Cookies["refresh_token"];
        }

             
        // Logout
        group.MapPost("/logout", async (
            [FromServices] IUserAuthenticationHandler authHandler) =>
        {
            return await authHandler.LogoutAsync();
        })
        .WithName("Logout")
        .WithSummary("Sign out the current user")
        .WithDescription("Signs out the current authenticated user from the application.")
        .WithTags("Authentication")
        .RequireAuthorization();

        return group;
    }
}

/// <summary>
/// Configures user registration endpoints with proper documentation and handlers
/// </summary>
public static class RegistrationEndpoints
{
    /// <summary>
    /// Maps user registration endpoints
    /// </summary>
    public static RouteGroupBuilder MapRegistrationEndpoints<TUser>(this RouteGroupBuilder group)
        where TUser : class, new()
    {
        // Standard user registration with explicit GDPR consent
        group.MapPost("/register", async (
            [FromBody] ExtendedRegisterRequest request,
            [FromServices] EnhancedUserRegistrationHandler registrationHandler) =>
        {
            return await registrationHandler.RegisterUserWithConsentAsync(request, "BusinessAdmin");
        })
        .WithName("RegisterWithConsent")
        .WithSummary("Register a new user account with explicit GDPR consent")
        .WithDescription("Creates a new user account with explicit Terms of Service and Privacy Policy consent recording. Implements: 'cuando un usuario se registra en la plataforma acepta explicitamente las condiciones de uso y la política de privacidad'")
        .WithTags("Registration", "GDPR")
        .AllowAnonymous();

        // Labor inspector registration
        group.MapPost("/register-labor-inspector", async (
            [FromBody] ExtendedRegisterRequest request,
            [FromServices] EnhancedUserRegistrationHandler registrationHandler) =>
        {
            // return await registrationHandler.RegisterUserWithConsentAsync(request, "LaborInspector",
            //     email => email.EndsWith("@dt.gob.cl", StringComparison.OrdinalIgnoreCase));

            return await registrationHandler.RegisterUserWithConsentAsync(request, "LaborInspector",
                email => email.EndsWith("@blenda.cl", StringComparison.OrdinalIgnoreCase));                
        })        
        .WithName("RegisterLaborInspector")
        .WithSummary("Register a new labor inspector account")
        .WithDescription("Creates a new labor inspector account. Email must be from @dt.gob.cl domain.")
        .WithTags("Registration")
        .AllowAnonymous();

        // HR Manager registration (requires BusinessAdmin authorization)
        group.MapPost("/register-hr-manager", async (
            [FromBody] RegisterRequest request,
            [FromServices] IUserRegistrationHandler<TUser> registrationHandler) =>
        {
            return await registrationHandler.RegisterUserAsync(request, "HRManager");
        })
        .WithName("RegisterHRManager")
        .WithSummary("Register a new HR manager account")
        .WithDescription("Creates a new HR manager account with appropriate permissions. Requires BusinessAdmin authorization.")
        .WithTags("Registration")
        .RequireAuthorization("BusinessAdminPolicy");

        // Operations Manager registration (requires BusinessAdmin authorization) - NUEVO para Módulo Asistencia DT
        group.MapPost("/register-operations", async (
            [FromBody] RegisterRequest request,
            [FromServices] IUserRegistrationHandler<TUser> registrationHandler) =>
        {
            return await registrationHandler.RegisterUserAsync(request, "Operations");
        })
        .WithName("RegisterOperations")
        .WithSummary("Register a new operations manager account")
        .WithDescription("Creates a new operations manager account with shift planning and operations management permissions. Requires BusinessAdmin authorization.")
        .WithTags("Registration")
        .RequireAuthorization("BusinessAdminPolicy");

        // Supervisor registration (requires BusinessAdmin OR HRManager authorization) - NUEVO para Módulo Asistencia DT
        group.MapPost("/register-supervisor", async (
            [FromBody] RegisterRequest request,
            [FromServices] IUserRegistrationHandler<TUser> registrationHandler) =>
        {
            return await registrationHandler.RegisterUserAsync(request, "Supervisor");
        })
        .WithName("RegisterSupervisor")
        .WithSummary("Register a new supervisor account")
        .WithDescription("Creates a new supervisor account with team management and employee supervision permissions. Requires BusinessAdmin OR HRManager authorization.")
        .WithTags("Registration")
        .RequireAuthorization("UserManagementPolicy");

        // External Auditor registration (requires BusinessAdmin authorization)
        group.MapPost("/register-external-auditor", async (
            [FromBody] RegisterRequest request,
            [FromServices] IUserRegistrationHandler<TUser> registrationHandler) =>
        {
            return await registrationHandler.RegisterUserAsync(request, "ExternalAuditor");
        })
        .WithName("RegisterExternalAuditor")
        .WithSummary("Register a new external auditor account")
        .WithDescription("Creates a new external auditor account with read-only audit permissions. Requires BusinessAdmin authorization.")
        .WithTags("Registration")
        .RequireAuthorization("BusinessAdminPolicy");

        // Employee registration (requires BusinessAdmin OR HRManager authorization)
        group.MapPost("/register-employee", async (
            [FromBody] RegisterRequest request,
            [FromServices] IUserRegistrationHandler<TUser> registrationHandler) =>
        {
            return await registrationHandler.RegisterUserAsync(request, "Employee");
        })
        .WithName("RegisterEmployee")
        .WithSummary("Register a new employee account")
        .WithDescription("Creates a new employee account with standard user permissions. Requires BusinessAdmin OR HRManager authorization.")
        .WithTags("Registration")
        .RequireAuthorization("UserManagementPolicy");

        // Technical Support registration (requires PlatformAdmin authorization)
        group.MapPost("/register-technical-support", async (
            [FromBody] RegisterRequest request,
            [FromServices] IUserRegistrationHandler<TUser> registrationHandler) =>
        {
            return await registrationHandler.RegisterUserAsync(request, "TechnicalSupport");
        })
        .WithName("RegisterTechnicalSupport")
        .WithSummary("Register a new technical support account")
        .WithDescription("Creates a new technical support account with support tool access. Requires PlatformAdmin authorization.")
        .WithTags("Registration")
        .RequireAuthorization("PlatformAdminPolicy");

        // RTM CRITICAL ROLES: Registration endpoints for critical compliance roles

        // ComplianceOfficer registration (requires PlatformAdmin authorization)
        group.MapPost("/register-compliance-officer", async (
            [FromBody] RegisterRequest request,
            [FromServices] IUserRegistrationHandler<TUser> registrationHandler) =>
        {
            return await registrationHandler.RegisterUserAsync(request, "ComplianceOfficer");
        })
        .WithName("RegisterComplianceOfficer")
        .WithSummary("Register a new compliance officer account")
        .WithDescription("Creates a new compliance officer account with regulatory compliance management permissions. Requires PlatformAdmin authorization.")
        .WithTags("Registration", "RTM Critical Roles")
        .RequireAuthorization("PlatformAdminPolicy");

        // LegalCounsel registration (requires ComplianceOfficer OR PlatformAdmin authorization)
        group.MapPost("/register-legal-counsel", async (
            [FromBody] RegisterRequest request,
            [FromServices] IUserRegistrationHandler<TUser> registrationHandler) =>
        {
            return await registrationHandler.RegisterUserAsync(request, "LegalCounsel");
        })
        .WithName("RegisterLegalCounsel")
        .WithSummary("Register a new legal counsel account")
        .WithDescription("Creates a new legal counsel account with specialized legal advisory permissions. Requires ComplianceOfficer OR PlatformAdmin authorization.")
        .WithTags("Registration", "RTM Critical Roles")
        .RequireAuthorization("ComplianceManagementPolicy");

        // DTRelationsSpecialist registration (requires LegalCounsel OR ComplianceOfficer authorization)
        group.MapPost("/register-dt-relations-specialist", async (
            [FromBody] RegisterRequest request,
            [FromServices] IUserRegistrationHandler<TUser> registrationHandler) =>
        {
            return await registrationHandler.RegisterUserAsync(request, "DTRelationsSpecialist");
        })
        .WithName("RegisterDTRelationsSpecialist")
        .WithSummary("Register a new DT relations specialist account")
        .WithDescription("Creates a new DT relations specialist account with labor directorate liaison permissions. Requires LegalCounsel OR ComplianceOfficer authorization.")
        .WithTags("Registration", "RTM Critical Roles")
        .RequireAuthorization("LegalAffairsPolicy");

        // TechnicalLead registration (requires PlatformAdmin authorization)
        group.MapPost("/register-technical-lead", async (
            [FromBody] RegisterRequest request,
            [FromServices] IUserRegistrationHandler<TUser> registrationHandler) =>
        {
            return await registrationHandler.RegisterUserAsync(request, "TechnicalLead");
        })
        .WithName("RegisterTechnicalLead")
        .WithSummary("Register a new technical lead account")
        .WithDescription("Creates a new technical lead account with technical architecture and team leadership permissions. Requires PlatformAdmin authorization.")
        .WithTags("Registration", "RTM Critical Roles")
        .RequireAuthorization("PlatformAdminPolicy");

        return group;
    }
}

/// <summary>
/// Configures account management endpoints with proper documentation and handlers
/// </summary>
public static class AccountManagementEndpoints
{
    /// <summary>
    /// Maps account management endpoints
    /// </summary>
    public static RouteGroupBuilder MapAccountManagementEndpoints<TUser>(this RouteGroupBuilder group)
        where TUser : class, new()
    {
        // Email confirmation
        group.MapGet("/confirm-email", async (
            [FromQuery] string userId,
            [FromQuery] string code,
            [FromQuery] string? changedEmail,
            [FromServices] IUserAccountHandler<TUser> accountHandler) =>
        {
            return await accountHandler.ConfirmEmailAsync(userId, code, changedEmail);
        })
        .WithName("ConfirmEmail")
        .WithSummary("Confirm user email address")
        .WithDescription("Confirms a user's email address using the confirmation token.")
        .WithTags("Account Management")
        .AllowAnonymous();

        // Forgot password
        group.MapPost("/forgot-password", async (
            [FromBody] ExtendedForgotPasswordRequest request,
            [FromServices] IUserAccountHandler<TUser> accountHandler) =>
        {
            return await accountHandler.ForgotPasswordAsync(request);
        })
        .WithName("ForgotPassword")
        .WithSummary("Request password reset")
        .WithDescription("Sends a password reset email to the user's registered email address.")
        .WithTags("Account Management")
        .AllowAnonymous();

        // Reset password
        group.MapPost("/reset-password", async (
            [FromBody] ExtendedResetPasswordRequest request,
            [FromServices] IUserAccountHandler<TUser> accountHandler) =>
        {
            return await accountHandler.ResetPasswordAsync(request);
        })
        .WithName("ResetPassword")
        .WithSummary("Reset user password")
        .WithDescription("Resets a user's password using the reset token sent via email.")
        .WithTags("Account Management")
        .AllowAnonymous();

        // // Support reset password
        // group.MapPost("/support-reset-password", async (
        //     [FromBody] CustomResetPasswordRequest request,
        //     [FromServices] IUserAccountHandler<TUser> accountHandler) =>
        // {
        //     return await accountHandler.CustomResetPasswordAsync(request);
        // })
        // .WithName("CustomResetPassword")
        // .WithSummary("Custom password reset")
        // .WithDescription("Resets password with optional reset code for flexible password recovery.")
        // .WithTags("Account Management")
        // .AllowAnonymous();

        // Resend confirmation email
        group.MapPost("/resend-confirmation-email", async (
            [FromBody] ExtendedResendConfirmationEmailRequest request,
            [FromServices] IUserAccountHandler<TUser> accountHandler) =>
        {
            return await accountHandler.ResendConfirmationEmailAsync(request);
        })
        .WithName("ResendConfirmationEmail")
        .WithSummary("Resend email confirmation")
        .WithDescription("Resends the email confirmation link to the user's registered email address.")
        .WithTags("Account Management")
        .AllowAnonymous();

        // Account management: profile endpoints for authenticated users
        var manageGroup = group.MapGroup("/manage")
            .RequireAuthorization()
            .WithTags("Account Management");

        // Get user profile (standard)
        manageGroup.MapGet("/profile", async (
            ClaimsPrincipal user,
            [FromServices] IUserAccountHandler<TUser> accountHandler) =>
        {
            return await accountHandler.GetUserInfoAsync(user);
        })
        .WithName("GetUserProfile")
        .WithSummary("Get user profile")
        .WithDescription("Retrieves profile information for the authenticated user.");

        // Get extended user profile with 2FA status, GDPR data, session info, and activity
        manageGroup.MapGet("/profile/extended", async (
            ClaimsPrincipal user,
            [FromServices] IUserAccountHandler<TUser> accountHandler) =>
        {
            return await accountHandler.GetExtendedUserInfoAsync(user);
        })
        .WithName("GetExtendedUserProfile")
        .WithSummary("Get comprehensive user profile with GDPR, session, and activity data")
        .WithDescription("Retrieves comprehensive profile information including Two-Factor Authentication status, GDPR compliance data (Terms of Service, Privacy Policy consents), session information (last access - 'Último Acceso'), activity data, and deletion request status. All information available in a single API call for optimal performance.");

        // Update user profile
        manageGroup.MapPost("/profile", async (
            ClaimsPrincipal user,
            [FromBody] InfoRequest infoRequest,
            [FromServices] IUserAccountHandler<TUser> accountHandler) =>
        {
            return await accountHandler.PostUserInfoAsync(user, infoRequest);
        })
        .WithName("PostUserProfile")
        .WithSummary("Update user profile")
        .WithDescription("Updates user profile information (email, password).");

        // Update extended user profile
        manageGroup.MapPost("/profile/extended", async (
            ClaimsPrincipal user,
            [FromBody] ExtendedInfoRequest infoRequest,
            [FromServices] IUserAccountHandler<TUser> accountHandler) =>
        {
            return await accountHandler.PostExtendedUserInfoAsync(user, infoRequest);
        })
        .WithName("PostExtendedUserProfile")
        .WithSummary("Update extended user profile")
        .WithDescription("Updates extended user profile information with enhanced security features.");

        return group;
    }
}

/// <summary>
/// Configures Two-Factor Authentication management endpoints
/// </summary>
public static class TwoFactorEndpoints
{
    /// <summary>
    /// Maps 2FA management endpoints
    /// </summary>
    public static RouteGroupBuilder MapTwoFactorEndpoints<TUser>(this RouteGroupBuilder group)
        where TUser : class, new()
    {
        var manageGroup = group.MapGroup("/manage/2fa")
            .RequireAuthorization()
            .WithTags("Two-Factor Authentication");

        // Get 2FA status
        manageGroup.MapGet("/status", async (
            HttpContext context,
            [FromServices] ITwoFactorManagementHandler<TUser> twoFactorHandler) =>
        {
            return await twoFactorHandler.GetTwoFactorStatusAsync(context.User);
        })
        .WithName("GetTwoFactorStatus")
        .WithSummary("Get Two-Factor Authentication status")
        .WithDescription("Retrieves the current 2FA status and recovery code count for the authenticated user.");

        // Setup 2FA
        manageGroup.MapGet("/setup", async (
            HttpContext context,
            [FromServices] ITwoFactorManagementHandler<TUser> twoFactorHandler) =>
        {
            return await twoFactorHandler.SetupTwoFactorAsync(context.User);
        })
        .WithName("SetupTwoFactor")
        .WithSummary("Setup Two-Factor Authentication")
        .WithDescription("Generates QR code and manual key for setting up 2FA with an authenticator app.");

        // Verify 2FA setup
        manageGroup.MapPost("/verify", async (
            [FromBody] TwoFactorVerifyRequest request,
            HttpContext context,
            [FromServices] ITwoFactorManagementHandler<TUser> twoFactorHandler) =>
        {
            return await twoFactorHandler.VerifyTwoFactorAsync(context.User, request);
        })
        .WithName("VerifyTwoFactor")
        .WithSummary("Verify and enable Two-Factor Authentication")
        .WithDescription("Verifies the 2FA setup with a code from the authenticator app and enables 2FA.");

        // Disable 2FA
        manageGroup.MapPost("/disable", async (
            [FromBody] TwoFactorDisableRequest request,
            HttpContext context,
            [FromServices] ITwoFactorManagementHandler<TUser> twoFactorHandler) =>
        {
            return await twoFactorHandler.DisableTwoFactorAsync(context.User, request);
        })
        .WithName("DisableTwoFactor")
        .WithSummary("Disable Two-Factor Authentication")
        .WithDescription("Disables 2FA for the user after password verification.");

        // Regenerate recovery codes
        manageGroup.MapPost("/recovery-codes/regenerate", async (
            [FromBody] TwoFactorRegenerateRecoveryCodesRequest request,
            HttpContext context,
            [FromServices] ITwoFactorManagementHandler<TUser> twoFactorHandler) =>
        {
            return await twoFactorHandler.RegenerateRecoveryCodesAsync(context.User, request);
        })
        .WithName("RegenerateRecoveryCodes")
        .WithSummary("Regenerate Two-Factor Authentication recovery codes")
        .WithDescription("Generates new recovery codes for 2FA after password verification.");

        return group;
    }
}

/// <summary>
/// Configures GDPR compliance endpoints for user consent and privacy management
/// Implements GDPR Articles 6, 7, 12-22 requirements for identity systems
/// </summary>
public static class GDPREndpoints
{
    /// <summary>
    /// Maps GDPR compliance endpoints
    /// </summary>
    public static RouteGroupBuilder MapGDPREndpoints(this RouteGroupBuilder group)
    {
        // =============================================================================
        // CONSENT MANAGEMENT ENDPOINTS
        // =============================================================================

        // Grant user consent
        group.MapPost("/consent/grant", async (
            ClaimsPrincipal user,
            [FromBody] GrantConsentRequest request,
            [FromServices] IGDPRComplianceService gdprService,
            [FromServices] ILogger<IGDPRComplianceService> logger,
            HttpContext context) =>
        {
            try
            {
                var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userId))
                {
                    return Results.BadRequest(new ProblemDetails 
                    { 
                        Title = "Authentication Error",
                        Detail = "Unable to identify current user"
                    });
                }

                // Validate request
                if (!Enum.IsDefined(typeof(ConsentType), request.ConsentType))
                {
                    return Results.BadRequest(new ProblemDetails
                    {
                        Title = "Invalid Consent Type",
                        Detail = $"ConsentType '{request.ConsentType}' is not valid"
                    });
                }

                // Get client information for audit trail
                var ipAddress = GetClientIPAddress(context);
                var userAgent = context.Request.Headers.UserAgent.ToString();

                var consentId = await gdprService.RecordConsentAsync(
                    userId, 
                    request.ConsentType, 
                    ipAddress,
                    userAgent,
                    request.PolicyVersion,
                    "api",
                    request.ExpirationDays);

                logger.LogInformation("User {UserId} granted consent {ConsentType}", userId, request.ConsentType);

                return Results.Ok(new ConsentResponse
                {
                    ConsentId = consentId,
                    ConsentType = request.ConsentType,
                    Status = ConsentStatus.Granted,
                    GrantedAt = DateTime.UtcNow,
                    ExpiresAt = request.ExpirationDays.HasValue 
                        ? DateTime.UtcNow.AddDays(request.ExpirationDays.Value) : null,
                    Message = "Consent successfully granted"
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to grant consent for user");
                return Results.Problem(
                    title: "Internal Server Error",
                    detail: "An error occurred while processing the consent request",
                    statusCode: 500);
            }
        })
        .WithName("GrantConsent")
        .WithSummary("Grant user consent with comprehensive audit trail")
        .WithDescription("GDPR Article 7: Grant user consent with comprehensive audit trail")
        .WithTags("GDPR", "Consent Management")
        .RequireAuthorization();

        // Withdraw user consent
        group.MapPost("/consent/withdraw", async (
            ClaimsPrincipal user,
            [FromBody] WithdrawConsentRequest request,
            [FromServices] IGDPRComplianceService gdprService,
            [FromServices] ILogger<IGDPRComplianceService> logger,
            HttpContext context) =>
        {
            try
            {
                var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userId))
                {
                    return Results.BadRequest(new ProblemDetails 
                    { 
                        Title = "Authentication Error",
                        Detail = "Unable to identify current user"
                    });
                }

                var success = await gdprService.WithdrawConsentAsync(
                    userId, 
                    request.ConsentType, 
                    request.WithdrawalReason);

                if (!success)
                {
                    return Results.BadRequest(new ProblemDetails
                    {
                        Title = "Consent Withdrawal Failed",
                        Detail = "Unable to withdraw consent. Consent may not exist or may already be withdrawn."
                    });
                }

                logger.LogInformation("User {UserId} withdrew consent {ConsentType}", userId, request.ConsentType);

                return Results.Ok(new ConsentResponse
                {
                    ConsentType = request.ConsentType,
                    Status = ConsentStatus.Withdrawn,
                    WithdrawnAt = DateTime.UtcNow,
                    WithdrawalReason = request.WithdrawalReason,
                    Message = "Consent successfully withdrawn"
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to withdraw consent for user");
                return Results.Problem(
                    title: "Internal Server Error",
                    detail: "An error occurred while processing the consent withdrawal",
                    statusCode: 500);
            }
        })
        .WithName("WithdrawConsent")
        .WithSummary("Withdraw user consent")
        .WithDescription("GDPR Article 7: Withdraw user consent with audit trail")
        .WithTags("GDPR", "Consent Management")
        .RequireAuthorization();

        // Get consent status
        group.MapGet("/consent/status", async (
            ClaimsPrincipal user,
            [FromQuery] ConsentType? consentType,
            [FromServices] IGDPRComplianceService gdprService,
            [FromServices] ILogger<IGDPRComplianceService> logger) =>
        {
            try
            {
                var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userId))
                {
                    return Results.BadRequest(new ProblemDetails 
                    { 
                        Title = "Authentication Error",
                        Detail = "Unable to identify current user"
                    });
                }

                if (consentType.HasValue)
                {
                    // Check specific consent type
                    var hasConsent = await gdprService.HasValidConsentAsync(userId, consentType.Value);
                    return Results.Ok(new ConsentStatusResponse
                    {
                        ConsentType = consentType.Value,
                        HasValidConsent = hasConsent,
                        CheckedAt = DateTime.UtcNow
                    });
                }
                else
                {
                    // Check all consent types
                    var allStatuses = new List<ConsentTypeStatus>();
                    foreach (ConsentType type in Enum.GetValues<ConsentType>())
                    {
                        var hasConsent = await gdprService.HasValidConsentAsync(userId, type);
                        allStatuses.Add(new ConsentTypeStatus
                        {
                            ConsentType = type,
                            HasValidConsent = hasConsent
                        });
                    }

                    return Results.Ok(new AllConsentStatusResponse
                    {
                        ConsentStatuses = allStatuses,
                        CheckedAt = DateTime.UtcNow
                    });
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to check consent status for user");
                return Results.Problem(
                    title: "Internal Server Error",
                    detail: "An error occurred while checking consent status",
                    statusCode: 500);
            }
        })
        .WithName("GetConsentStatus")
        .WithSummary("Check user consent status")
        .WithDescription("GDPR Article 7: Check current consent status for one or all consent types")
        .WithTags("GDPR", "Consent Management")
        .RequireAuthorization();

        // =============================================================================
        // PRIVACY SETTINGS ENDPOINTS
        // =============================================================================

        // Get privacy settings
        group.MapGet("/privacy/settings", async (
            ClaimsPrincipal user,
            [FromServices] IGDPRComplianceService gdprService,
            [FromServices] ILogger<IGDPRComplianceService> logger) =>
        {
            try
            {
                var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userId))
                {
                    return Results.BadRequest(new ProblemDetails 
                    { 
                        Title = "Authentication Error",
                        Detail = "Unable to identify current user"
                    });
                }

                var settings = await gdprService.InitializePrivacySettingsAsync(userId);
                return Results.Ok(settings);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to get privacy settings for user {UserId}", user.FindFirstValue(ClaimTypes.NameIdentifier));
                return Results.Problem(
                    title: "Internal Server Error",
                    detail: "An error occurred while retrieving privacy settings",
                    statusCode: 500);
            }
        })
        .WithName("GetPrivacySettings")
        .WithSummary("Get user privacy settings")
        .WithDescription("GDPR Article 12: Retrieve current privacy settings and preferences")
        .WithTags("GDPR", "Privacy Management")
        .RequireAuthorization();

        // Update privacy settings
        group.MapPut("/privacy/settings", async (
            ClaimsPrincipal user,
            [FromBody] UpdatePrivacySettingsRequest request,
            [FromServices] IGDPRComplianceService gdprService,
            [FromServices] ILogger<IGDPRComplianceService> logger,
            HttpContext context) =>
        {
            try
            {
                var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userId))
                {
                    return Results.BadRequest(new ProblemDetails 
                    { 
                        Title = "Authentication Error",
                        Detail = "Unable to identify current user"
                    });
                }

                // Create UserPrivacySettings object from request
                var settings = new UserPrivacySettings
                {
                    UserId = userId,
                    AllowAnalytics = request.AllowAnalytics,
                    AllowMarketing = request.AllowMarketing,
                    AllowThirdPartySharing = request.AllowThirdPartySharing,
                    AllowCrossBorderTransfer = request.AllowCrossBorderTransfer,
                    AllowAIProcessing = request.AllowAIProcessing,
                    PreferredDataRetentionDays = request.PreferredDataRetentionDays,
                    PreferredLanguage = request.PreferredLanguage ?? "en-US",
                    NotificationFrequency = request.NotificationFrequency ?? "Immediate"
                };

                var success = await gdprService.UpdatePrivacySettingsAsync(userId, settings);

                if (!success)
                {
                    return Results.BadRequest(new ProblemDetails
                    {
                        Title = "Privacy Settings Update Failed",
                        Detail = "Unable to update privacy settings. Please try again."
                    });
                }

                logger.LogInformation("User {UserId} updated privacy settings", userId);

                return Results.Ok(new { 
                    message = "Privacy settings updated successfully",
                    updatedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to update privacy settings for user");
                return Results.Problem(
                    title: "Internal Server Error",
                    detail: "An error occurred while updating privacy settings",
                    statusCode: 500);
            }
        })
        .WithName("UpdatePrivacySettings")
        .WithSummary("Update user privacy settings")
        .WithDescription("GDPR Article 7: Update privacy preferences with audit trail")
        .WithTags("GDPR", "Privacy Management")
        .RequireAuthorization();

        // =============================================================================
        // DATA RIGHTS ENDPOINTS
        // =============================================================================

        // Request data export
        group.MapPost("/data/export", async (
            ClaimsPrincipal user,
            [FromServices] IGDPRComplianceService gdprService,
            [FromServices] ILogger<IGDPRComplianceService> logger,
            HttpContext context) =>
        {
            try
            {
                var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userId))
                {
                    return Results.BadRequest(new ProblemDetails 
                    { 
                        Title = "Authentication Error",
                        Detail = "Unable to identify current user"
                    });
                }

                var exportData = await gdprService.ExportUserDataAsync(userId);

                logger.LogInformation("User {UserId} requested data export", userId);

                return Results.Ok(new {
                    exportRequestId = Guid.NewGuid(),
                    requestedAt = DateTime.UtcNow,
                    estimatedCompletionTime = DateTime.UtcNow.AddHours(24),
                    data = exportData,
                    message = "Data export completed successfully."
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process data export request for user");
                return Results.Problem(
                    title: "Internal Server Error",
                    detail: "An error occurred while processing the data export request",
                    statusCode: 500);
            }
        })
        .WithName("RequestDataExport")
        .WithSummary("Request user data export")
        .WithDescription("GDPR Article 20: Request complete data export (data portability)")
        .WithTags("GDPR", "Data Rights")
        .RequireAuthorization();

        // Request data deletion
        group.MapPost("/data/delete", async (
            ClaimsPrincipal user,
            [FromBody] DataDeletionRequest request,
            [FromServices] IGDPRComplianceService gdprService,
            [FromServices] ILogger<IGDPRComplianceService> logger,
            HttpContext context) =>
        {
            try
            {
                var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userId))
                {
                    return Results.BadRequest(new ProblemDetails 
                    { 
                        Title = "Authentication Error",
                        Detail = "Unable to identify current user"
                    });
                }

                var deletionDate = await gdprService.RequestDataDeletionAsync(
                    userId, 
                    request.LegalRetentionDays ?? 2555); // Default 7 years legal retention

                logger.LogWarning("User {UserId} requested data deletion", userId);

                return Results.Ok(new DataDeletionResponse
                {
                    RequestedAt = DateTime.UtcNow,
                    ScheduledDeletionDate = deletionDate,
                    LegalRetentionDays = request.LegalRetentionDays ?? 2555,
                    Message = $"Data deletion scheduled for {deletionDate:yyyy-MM-dd}. Some data may be retained for legal compliance purposes."
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process data deletion request for user");
                return Results.Problem(
                    title: "Internal Server Error",
                    detail: "An error occurred while processing the data deletion request",
                    statusCode: 500);
            }
        })
        .WithName("RequestDataDeletion")
        .WithSummary("Request user data deletion")
        .WithDescription("GDPR Article 17: Request data deletion (right to be forgotten)")
        .WithTags("GDPR", "Data Rights")
        .RequireAuthorization();

        return group;
    }

    /// <summary>
    /// Get client IP address for audit trail
    /// </summary>
    /// <param name="context">HTTP context</param>
    /// <returns>Client IP address</returns>
    private static string GetClientIPAddress(HttpContext context)
    {
        // Check for forwarded header first (for load balancers/proxies)
        var forwardedHeader = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedHeader))
        {
            return forwardedHeader.Split(',')[0].Trim();
        }

        // Check for real IP header
        var realIpHeader = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIpHeader))
        {
            return realIpHeader;
        }

        // Fallback to connection remote IP
        return context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
    }
}
