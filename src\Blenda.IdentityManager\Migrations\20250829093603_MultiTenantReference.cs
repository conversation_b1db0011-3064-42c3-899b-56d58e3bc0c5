﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace IdentityManager.Migrations
{
    /// <inheritdoc />
    public partial class MultiTenantReference : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsTenantAdmin",
                table: "AspNetUsers",
                type: "NUMBER(1)",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "TenantId",
                table: "AspNetUsers",
                type: "RAW(16)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "TenantJoinedAt",
                table: "AspNetUsers",
                type: "TIMESTAMP(6)",
                nullable: true,
                defaultValueSql: "SYS_EXTRACT_UTC(SYSTIMESTAMP)");

            migrationBuilder.CreateIndex(
                name: "IX_ApplicationUser_TenantId",
                table: "AspNetUsers",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_ApplicationUser_TenantId_Email",
                table: "AspNetUsers",
                columns: new[] { "TenantId", "Email" });

            migrationBuilder.CreateIndex(
                name: "IX_ApplicationUser_TenantId_Status",
                table: "AspNetUsers",
                columns: new[] { "TenantId", "AccountStatus" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ApplicationUser_TenantId",
                table: "AspNetUsers");

            migrationBuilder.DropIndex(
                name: "IX_ApplicationUser_TenantId_Email",
                table: "AspNetUsers");

            migrationBuilder.DropIndex(
                name: "IX_ApplicationUser_TenantId_Status",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "IsTenantAdmin",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "TenantId",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "TenantJoinedAt",
                table: "AspNetUsers");
        }
    }
}
