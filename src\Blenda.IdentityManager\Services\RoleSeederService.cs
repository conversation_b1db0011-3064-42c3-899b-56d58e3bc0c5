using Microsoft.AspNetCore.Identity;

namespace IdentityManager.Services;

/// <summary>
/// Service responsible for ensuring all required roles exist in the system
/// </summary>
public class RoleSeederService
{
    private readonly RoleManager<IdentityRole> _roleManager;
    private readonly ILogger<RoleSeederService> _logger;

    /// <summary>
    /// All roles defined in the Blenda Identity & Access Management System
    /// Based on docs/ROLES_CLAIMS_POLICIES.md specification and M<PERSON><PERSON><PERSON> DT requirements
    /// </summary>
    private static readonly string[] RequiredRoles = new[]
    {
        // Administrative Roles (Highest Privilege)
        "PlatformAdmin",    // Platform Administrator - System-wide administration
        "BusinessAdmin",    // Business Administrator - Tenant-level administration

        // Management Roles (Medium Privilege)  
        "HRManager",        // Human Resources Manager - HR and personnel management
        "Operations",       // Operations Manager - Shift planning and service compliance
        "Supervisor",       // Supervisor - Team and group management within hierarchy

        // Operational Roles (Standard Privilege)
        "Employee",         // Standard Employee - Personal data access and standard functionality
        "TechnicalSupport", // Technical Support Representative - Technical assistance

        // Specialized Access Roles (Restricted Privilege)
        "ExternalAuditor",  // External Auditor - Read-only audit access for compliance
        "LaborInspector",   // Government Labor Inspector - Regulatory inspection and compliance

        // RTM Critical Roles (Compliance and Regulatory)
        "ComplianceOfficer",        // Compliance Officer - Regulatory compliance monitoring
        "LegalCounsel",            // Legal Counsel - Legal interpretation and regulatory changes
        "DTRelationsSpecialist",   // DT Relations Specialist - Direct liaison with Dirección del Trabajo
        "TechnicalLead",           // Technical Lead - Architectural oversight and technical implementation

        // Legacy Roles (Compatibility)
        "Suscriptor",       // Basic Subscriber - Basic system access (legacy/default)
        "Admin",           // Legacy Admin role for backward compatibility
        "IdentityManager"  // Legacy Identity Manager role for backward compatibility
    };

    public RoleSeederService(
        RoleManager<IdentityRole> roleManager,
        ILogger<RoleSeederService> logger)
    {
        _roleManager = roleManager;
        _logger = logger;
    }

    /// <summary>
    /// Ensures all required roles exist in the system
    /// </summary>
    /// <returns>Task representing the asynchronous operation</returns>
    public async Task EnsureRolesExistAsync()
    {
        _logger.LogInformation("Starting role seeding process. Checking {RoleCount} required roles.", RequiredRoles.Length);

        int createdRoles = 0;
        int existingRoles = 0;

        foreach (var roleName in RequiredRoles)
        {
            try
            {
                if (!await _roleManager.RoleExistsAsync(roleName))
                {
                    var role = new IdentityRole(roleName);
                    var result = await _roleManager.CreateAsync(role);
                    
                    if (result.Succeeded)
                    {
                        _logger.LogInformation("Successfully created role: {RoleName}", roleName);
                        createdRoles++;
                    }
                    else
                    {
                        _logger.LogError("Failed to create role {RoleName}: {Errors}", 
                            roleName, string.Join(", ", result.Errors.Select(e => e.Description)));
                    }
                }
                else
                {
                    _logger.LogDebug("Role already exists: {RoleName}", roleName);
                    existingRoles++;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error while ensuring role {RoleName} exists", roleName);
            }
        }

        _logger.LogInformation("Role seeding completed. Created: {CreatedCount}, Existing: {ExistingCount}, Total Required: {TotalCount}", 
            createdRoles, existingRoles, RequiredRoles.Length);
    }

    /// <summary>
    /// Gets all the role names that should exist in the system
    /// </summary>
    /// <returns>Array of required role names</returns>
    public static string[] GetRequiredRoles() => RequiredRoles;

    /// <summary>
    /// Validates if a role name is one of the documented system roles
    /// </summary>
    /// <param name="roleName">The role name to validate</param>
    /// <returns>True if the role is a documented system role, false otherwise</returns>
    public static bool IsValidSystemRole(string roleName)
    {
        return RequiredRoles.Contains(roleName, StringComparer.OrdinalIgnoreCase);
    }
}
