## KB: Access token permanece válido después de logout — Plan de acción

Este documento describe por qué un access token (JWT) sigue siendo válido después de ejecutar `/api/auth/logout` y contiene un plan de acción detallado y ejecutable para mitigar el problema.

Se proporcionan dos soluciones concretas, pruebas y comandos para verificar el comportamiento: 1) validar `session_id` durante la validación del JWT (recomendado), 2) alternativa escalable con `jti` + blacklist (Redis).

## 1. Resumen / Overview

- Problema: El endpoint `/api/auth/logout` termina la sesión en la base de datos y ejecuta `SignOutAsync()`, pero no invalida los JWT ya emitidos. El middleware de JWT en el proyecto valida firma/issuer/audience/expiración, pero no comprueba si la sesión asociada (claim `session_id`) está marcada como inactiva. Por eso, el access token sigue autorizado para `/api/auth/manage/2fa/status` hasta que expira.
- Objetivo: Asegurar que, tras logout, cualquier access token previamente emitido deje de ser aceptado inmediatamente.

## 2. Implementation Details (Detalle técnico)

Contrato mínimo
- Inputs: petición HTTP con cabecera `Authorization: Bearer <accessToken>`.
- Output: `context.User` debe establecerse sólo si el token es válido y la sesión asociada está activa; de lo contrario la validación debe fallar (401).
- Error modes: si el store de sesiones está inaccesible, por seguridad se recomienda política fail-secure (rechazar), configurable mediante `Auth:FailOpenOnSessionStoreDown`.

Edge cases
- Tokens sin `session_id`: política recomendada — rechazarlos (forzar emisión consistente con sesión).
- Latencia por consulta a BD: mitigar con cache (Redis o IDistributedCache) y TTL.
- Logout en múltiples instancias: usar store centralizado (DB o Redis).

Cambios principales a implementar
- Añadir método de consulta en `ISessionService` (firma propuesta):

	Task<bool> IsSessionActiveAsync(string userId, string sessionId);

- Implementar la lectura en `Services/Core/SessionService.cs` (consulta por userId+sessionId). Si ya existe, añadir método wrapper.
- Modificar la validación del JWT:
	- Si usas `JwtBearer` (recomendado): agregar `options.Events.OnTokenValidated` que consulte `ISessionService`.
	- Si usas middleware personalizado (`JwtAuthenticationMiddleware`): incorporar la comprobación inmediatamente después de `ValidateToken`.
- (Opcional, Opción B) Añadir `jti` en el token en `TokenService` y gestionar blacklist en Redis.

Snippet de ejemplo (OnTokenValidated) — adaptar a tu código:

```csharp
options.Events = new JwtBearerEvents
{
		OnTokenValidated = async context =>
		{
				var http = context.HttpContext;
				var sessionService = http.RequestServices.GetRequiredService<ISessionService>();
				var userManager = http.RequestServices.GetRequiredService<UserManager<ApplicationUser>>();

				var userId = context.Principal?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
				var sessionId = context.Principal?.FindFirst("session_id")?.Value;
				var securityStampClaim = context.Principal?.FindFirst("security_stamp")?.Value;

				if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(sessionId))
				{
						context.Fail("Invalid token: missing session or user");
						return;
				}

				var isActive = await sessionService.IsSessionActiveAsync(userId, sessionId);
				if (!isActive)
				{
						context.Fail("Session terminated");
						return;
				}

				if (!string.IsNullOrEmpty(securityStampClaim))
				{
						var user = await userManager.FindByIdAsync(userId);
						if (user == null || await userManager.GetSecurityStampAsync(user) != securityStampClaim)
						{
								context.Fail("Security stamp mismatch");
								return;
						}
				}
		}
};
```

### Opción B — jti + Redis blacklist (esquema)

- Emisión: añadir claim `jti = Guid.NewGuid().ToString()` en el token.
- Logout: marcar `revoked-jti:{jti}` en Redis con TTL = tiempo restante del token.
- Validación: en `OnTokenValidated`, comprobar en Redis si `revoked-jti:{jti}` existe; si existe -> `context.Fail("Token revoked")`.

Ejemplo Redis (StackExchange.Redis):

```csharp
var mux = http.RequestServices.GetRequiredService<IConnectionMultiplexer>();
var db = mux.GetDatabase();
var isRevoked = await db.KeyExistsAsync($"revoked-jti:{jti}");
if (isRevoked) context.Fail("Token revoked");
```

## 3. Configuration

- Variables de entorno / appsettings sugeridas:
	- `Auth:FailOpenOnSessionStoreDown` (bool, default=false)
	- `Redis:ConnectionString` si se usa Redis

- Dependencias opcionales:
	- `StackExchange.Redis` si adoptas la Opción B.

## 4. Ejemplos y pruebas (How to test)

Prueba manual rápida (PowerShell):

1) Login — obtener access token (ejemplo con `Invoke-RestMethod`) y guardarlo en `$token`.

2) Llamada protegida antes de logout (debe devolver 200):

```powershell
Invoke-RestMethod -Uri "http://localhost:5160/api/auth/manage/2fa/status" -Headers @{ Authorization = "Bearer $token" } -Method Get
```

3) Logout (post con el mismo token):

```powershell
Invoke-RestMethod -Uri "http://localhost:5160/api/auth/logout" -Headers @{ Authorization = "Bearer $token" } -Method Post
```

4) Reintento con el mismo token (debe devolver 401 si la validación se implementó correctamente):

```powershell
Invoke-RestMethod -Uri "http://localhost:5160/api/auth/manage/2fa/status" -Headers @{ Authorization = "Bearer $token" } -Method Get
```

Pruebas automáticas mínimas (xUnit):
- Test_HappyPath_LoginThenCallProtected_Returns200
- Test_Logout_InvalidatesToken_Returns401
- Test_SessionStoreDown_ConfigFailSecure_Returns401

Comandos útiles:

```powershell
# Añadir Redis package (opcional)
dotnet add src\Blenda.IdentityManager\Blenda.IdentityManager.csproj package StackExchange.Redis

# Ejecutar tests
dotnet test
```

## 5. Troubleshooting

- Si la validación falla para tokens legítimos:
	- Verificar que `session_id` almacenado al crear token coincide exactamente con claim en token.
	- Comprobar timezone/expiración y TTL en Redis.
- Latencia alta: introducir cache (Redis o IDistributedCache) con TTL igual al tiempo de expiración de la sesión.
- Multi-instances: asegurarse que logout escribe en store central (DB/Redis), no en memoria local.

## 6. Consideraciones de seguridad

- Fail-secure vs Fail-open: por seguridad configurar por defecto fail-secure (rechazar si no se puede consultar store).
- No loggear tokens completos ni datos sensibles (security_stamp, jti) en texto claro; loggear solamente hashed/partial o identificadores internos.
- Asegurar comunicación TLS entre app y Redis/DB.
- TTL adecuado para entradas de blacklist (jti) para evitar crecimiento infinito.

## 7. Support / Próximos pasos

Opciones para que implemente ahora (elige una):

- Opción A (Recomendada): Implementar `IsSessionActiveAsync` en `ISessionService` / `SessionService` y añadir `OnTokenValidated` (o adaptar `JwtAuthenticationMiddleware`) para comprobar sesión y security_stamp.
	- Entregables: cambios en `ISessionService`, `SessionService`, `Program.cs` (o `JwtAuthenticationMiddleware`), tests unitarios e integración, PR.
	- Estimación: 2–4 horas local.

- Opción B (Escalable): Implementar `jti` + Redis blacklist.
	- Entregables: modificar `TokenService` para emitir `jti`, integración con Redis, validación en `OnTokenValidated`, tests.
	- Estimación: 4–8 horas.

Si quieres que implemente la Opción A ahora, la aplicaré como cambios en el repo, añadiré pruebas automáticas e intentaré ejecutar `dotnet test` localmente. Si prefieres la Opción B, lo implemento en su lugar.

---

Archivo referenciado: `Services/Core/SessionService.cs`, `Services/Core/TokenService.cs`, `Services/JwtAuthenticationMiddleware.cs`, `Services/Handlers/UserAuthenticationHandler.cs`, `Program.cs`.

