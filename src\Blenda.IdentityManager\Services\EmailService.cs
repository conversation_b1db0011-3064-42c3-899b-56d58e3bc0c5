//based from https://z<PERSON>gren.net/sending-e-mails-using-microsoft-graph-using-dotnet/

using Azure.Identity;
using Microsoft.Graph;
using Microsoft.Graph.Models;

public class EmailService
{


    public string BasicExampleSendEmail(string fromAddress, string toAddress, string CcAddress, string subject, string message, string tenanatID, string clientID, string clientSecret)
    {
        try
        {

            var credentials = new ClientSecretCredential(
                                tenanatID, clientID, clientSecret,
                            new TokenCredentialOptions { AuthorityHost = AzureAuthorityHosts.AzurePublicCloud });
            GraphServiceClient graphServiceClient = new GraphServiceClient(credentials);


            string[] toMail = toAddress.Split(',');
            List<Recipient> toRecipients = new List<Recipient>();
            int i = 0;
            for (i = 0; i < toMail.Count(); i++)
            {
                Recipient toRecipient = new Recipient();
                EmailAddress toEmailAddress = new EmailAddress();

                toEmailAddress.Address = toMail[i];
                toRecipient.EmailAddress = toEmailAddress;
                toRecipients.Add(toRecipient);
            }

            List<Recipient> ccRecipients = new List<Recipient>();
            if (!string.IsNullOrEmpty(CcAddress))
            {
                string[] ccMail = CcAddress.Split(',');
                int j = 0;
                for (j = 0; j < ccMail.Count(); j++)
                {
                    Recipient ccRecipient = new Recipient();
                    EmailAddress ccEmailAddress = new EmailAddress();

                    ccEmailAddress.Address = ccMail[j];
                    ccRecipient.EmailAddress = ccEmailAddress;
                    ccRecipients.Add(ccRecipient);
                }
            }
            var mailMessage = new Message
            {
                Subject = subject,

                Body = new ItemBody
                {
                    ContentType = BodyType.Html,
                    Content = message
                },
                ToRecipients = toRecipients,
                CcRecipients = ccRecipients

            };
            // Send mail as the given user. 
            graphServiceClient
               .Users[fromAddress]
               .SendMail
               .PostAsync(new Microsoft.Graph.Users.Item.SendMail.SendMailPostRequestBody
               {
                   Message = mailMessage,
                   SaveToSentItems = true
               }).Wait();

            return "Email successfully sent.";

        }
        catch (Exception ex)
        {

            return "Send Email Failed.\r\n" + ex.Message;
        }
    }
    
    public string SendRegisterEmail(string UserObjectId, string toAddress, string CcAddress, string tenanatID, string clientID, string clientSecret)
    {
        try
        {

            var credentials = new ClientSecretCredential(
                                tenanatID, clientID, clientSecret,
                            new TokenCredentialOptions { AuthorityHost = AzureAuthorityHosts.AzurePublicCloud });
            GraphServiceClient graphServiceClient = new GraphServiceClient(credentials);


            string[] toMail = toAddress.Split(',');
            List<Recipient> toRecipients = new List<Recipient>();
            int i = 0;
            for (i = 0; i < toMail.Count(); i++)
            {
                Recipient toRecipient = new Recipient();
                EmailAddress toEmailAddress = new EmailAddress();

                toEmailAddress.Address = toMail[i];
                toRecipient.EmailAddress = toEmailAddress;
                toRecipients.Add(toRecipient);
            }

            List<Recipient> ccRecipients = new List<Recipient>();
            if (!string.IsNullOrEmpty(CcAddress))
            {
                string[] ccMail = CcAddress.Split(',');
                int j = 0;
                for (j = 0; j < ccMail.Count(); j++)
                {
                    Recipient ccRecipient = new Recipient();
                    EmailAddress ccEmailAddress = new EmailAddress();

                    ccEmailAddress.Address = ccMail[j];
                    ccRecipient.EmailAddress = ccEmailAddress;
                    ccRecipients.Add(ccRecipient);
                }
            }

            var subject = $"Sent from demo code at {DateTime.Now.ToString("s")}";
            var body =
                System.IO.File.ReadAllText("registertemplate.html")
                .Replace("{{HEADER_LINK}}", "https://blenda.lat")
                .Replace("{{HEADER_LINK_TEXT}}", "Hola, Somos del equipo de Blenda.")
                .Replace("{{HEADLINE}}", "Por favor verifique su correo electrónico!")
                .Replace("{{BODY}}", $"Siga este enlace para verificar su correo electrónico o bien copie y pegue en su navegador: <p>https://blenda.lat/verifyemail?userid={UserObjectId}</p>")
                .Replace("{{URI}}", $"https://blenda.lat/verifyemail?userid={UserObjectId}");
                



            var mailMessage = new Message
            {
                Subject = subject,

                Body = new ItemBody
                {
                    ContentType = BodyType.Html,
                    Content = body
                },
                ToRecipients = toRecipients,
                CcRecipients = ccRecipients

            };
            // Send mail as the given user. 
            graphServiceClient
               .Users[UserObjectId]
               .SendMail
               .PostAsync(new Microsoft.Graph.Users.Item.SendMail.SendMailPostRequestBody
               {
                   Message = mailMessage,
                   SaveToSentItems = true
               }).Wait();

            return "Email successfully sent.";

        }
        catch (Exception ex)
        {

            return "Send Email Failed.\r\n" + ex.Message + ex.StackTrace;
        }
    }
}