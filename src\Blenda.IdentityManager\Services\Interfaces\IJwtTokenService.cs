using Microsoft.AspNetCore.Identity;

namespace IdentityManager.Services.Interfaces;

/// <summary>
/// Interface for JWT token generation and management
/// </summary>
public interface IJwtTokenService
{
    /// <summary>
    /// Generates a JWT access token for the specified user
    /// </summary>
    /// <param name="user">The user to generate token for</param>
    /// <param name="userManager">User manager instance</param>
    /// <param name="additionalClaims">Additional claims to include in the token</param>
    /// <returns>JWT access token string</returns>
    Task<string> GenerateAccessTokenAsync<TUser>(TUser user, UserManager<TUser> userManager, IEnumerable<System.Security.Claims.Claim>? additionalClaims = null) 
        where TUser : class;

    /// <summary>
    /// Generates a refresh token for the specified user
    /// </summary>
    /// <param name="user">The user to generate refresh token for</param>
    /// <returns>Refresh token string</returns>
    string GenerateRefreshToken<TUser>(TUser user) where TUser : class;

    /// <summary>
    /// Validates and extracts user information from a JWT token
    /// </summary>
    /// <param name="token">The JWT token to validate</param>
    /// <returns>User ID if valid, null otherwise</returns>
    string? ValidateTokenAsync(string token);

    /// <summary>
    /// Gets the token expiration time
    /// </summary>
    /// <returns>Token expiration time in minutes</returns>
    int GetTokenExpirationMinutes();
}
