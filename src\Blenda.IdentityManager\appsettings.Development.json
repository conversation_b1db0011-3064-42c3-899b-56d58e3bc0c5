{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Warning", "Microsoft.IdentityModel": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"OracleConnectionGRP": "PLACEHOLDER_FOR_ENVIRONMENT_VARIABLE", "OracleConnectionIdentity": "PLACEHOLDER_FOR_ENVIRONMENT_VARIABLE"}, "Jwt": {"Key": "PLACEHOLDER_FOR_ENVIRONMENT_VARIABLE", "Issuer": "https://identity.blenda.lat", "Audience": "https://identity.blenda.lat", "TokenLifetimeInMinutes": 15, "Lifetime": "0.00:15:00", "RefreshTokenLifetime": "7.00:00:00", "RefreshTokenSlidingExpiration": true, "RefreshTokenAbsoluteExpiration": true}, "Email": {"UseGraphApi": true, "From": "<EMAIL>", "FromName": "Blenda Identity & Access Management"}, "AzureAd": {"TenantId": "PLACEHOLDER_FOR_ENVIRONMENT_VARIABLE", "ClientId": "PLACEHOLDER_FOR_ENVIRONMENT_VARIABLE", "ClientSecret": "PLACEHOLDER_FOR_ENVIRONMENT_VARIABLE"}, "ApiSettings": {"PublicFacingService": "https://www.blenda.lat", "InternalService": "https://identity.blenda.lat"}, "Security": {"RequireHttps": true, "CookieSecure": true, "SameSiteMode": "Strict"}}