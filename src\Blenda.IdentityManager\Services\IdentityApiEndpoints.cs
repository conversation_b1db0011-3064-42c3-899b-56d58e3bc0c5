using Microsoft.AspNetCore.Builder;
using IdentityManager.Services.Configuration;

namespace IdentityManager.Services;

/// <summary>
/// Simplified extension methods for configuring identity endpoints using handlers
/// </summary>
public static class IdentityApiEndpoints
{
    /// <summary>
    /// Maps all identity-related endpoints using the handler pattern for better maintainability
    /// </summary>
    /// <typeparam name="TUser">The user type</typeparam>
    /// <param name="endpoints">The endpoint route builder</param>
    /// <returns>The configured route group</returns>
    public static IEndpointConventionBuilder MapIdentityEndpoints<TUser>(this IEndpointRouteBuilder endpoints)
        where TUser : class, new()
    {
        ArgumentNullException.ThrowIfNull(endpoints);

        // Create main route group for all identity endpoints
        var routeGroup = endpoints.MapGroup("");

        // Configure authentication endpoints (login, logout, refresh)
        routeGroup.MapAuthenticationEndpoints<TUser>();

        // Configure registration endpoints (different user types)
        routeGroup.MapRegistrationEndpoints<TUser>();

        // Configure account management endpoints (email confirmation, password reset)
        routeGroup.MapAccountManagementEndpoints<TUser>();

        // Configure Two-Factor Authentication endpoints
        routeGroup.MapTwoFactorEndpoints<TUser>();

        return routeGroup;
    }
}
