using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Identity;

namespace IdentityManager.Models
{
    // =============================================================================
    // SECURITY AUDIT AND SESSION MANAGEMENT MODELS
    // =============================================================================
    
    /// <summary>
    /// Security compliance audit logging for login attempts and security events
    /// Integrates with existing GDPR audit framework
    /// </summary>
    public class SecurityAuditLog
    {
        /// <summary>Primary key for security audit record</summary>
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();
        
        /// <summary>User ID if authentication attempt was for a known user</summary>
        public string? UserId { get; set; }
        
        /// <summary>Navigation property to ApplicationUser</summary>
        public ApplicationUser? User { get; set; }
        
        /// <summary>Type of security event (Login, Logout, TokenRefresh, etc.)</summary>
        [Required]
        [MaxLength(50)]
        public string EventType { get; set; } = string.Empty;
        
        /// <summary>Email address used in authentication attempt</summary>
        [MaxLength(256)]
        public string? Email { get; set; }
        
        /// <summary>Whether the authentication attempt was successful</summary>
        public bool Success { get; set; }
        
        /// <summary>Reason for failure (if applicable)</summary>
        [MaxLength(500)]
        public string? FailureReason { get; set; }
        
        /// <summary>Client IP address (hashed for privacy)</summary>
        [MaxLength(128)]
        public string? IpAddressHash { get; set; }
        
        /// <summary>User agent string (truncated)</summary>
        [MaxLength(500)]
        public string? UserAgent { get; set; }
        
        /// <summary>Tenant ID if applicable (for tenant-scoped audit)</summary>
        public Guid? TenantId { get; set; }
        
        /// <summary>Additional security metadata as JSON</summary>
        [MaxLength(2000)]
        public string? SecurityMetadata { get; set; }
        
        /// <summary>Timestamp of security event</summary>
        [Required]
        public DateTime EventTimestamp { get; set; } = DateTime.UtcNow;
        
        /// <summary>Session ID for tracking user sessions</summary>
        [MaxLength(128)]
        public string? SessionId { get; set; }
        
        /// <summary>Risk score calculated for this event (0-100)</summary>
        public int RiskScore { get; set; } = 0;
    }
    
    /// <summary>
    /// User session management for activity tracking and security monitoring
    /// Supports concurrent sessions and cross-device tracking
    /// </summary>
    public class UserSession
    {
        /// <summary>Session identifier (primary key)</summary>
        [Key]
        [MaxLength(128)]
        public string SessionId { get; set; } = string.Empty;
        
        /// <summary>User who owns this session</summary>
        [Required]
        public string UserId { get; set; } = string.Empty;
        
        /// <summary>Navigation property to ApplicationUser</summary>
        public ApplicationUser User { get; set; } = null!;
        
        /// <summary>Tenant context for this session (if applicable)</summary>
        public Guid? TenantId { get; set; }
        
        /// <summary>When the session was created</summary>
        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>Last activity timestamp for timeout management</summary>
        [Required]
        public DateTime LastActivityAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>When the session expires</summary>
        [Required]
        public DateTime ExpiresAt { get; set; }
        
        /// <summary>Whether session is currently active</summary>
        public bool IsActive { get; set; } = true;
        
        /// <summary>Device/browser information</summary>
        [MaxLength(500)]
        public string? DeviceInfo { get; set; }
        
        /// <summary>IP address when session was created (hashed)</summary>
        [MaxLength(128)]
        public string? IpAddressHash { get; set; }
        
        /// <summary>Location information if available</summary>
        [MaxLength(200)]
        public string? Location { get; set; }
        
        /// <summary>Session termination reason</summary>
        [MaxLength(100)]
        public string? TerminationReason { get; set; }
        
        /// <summary>When session was terminated</summary>
        public DateTime? TerminatedAt { get; set; }
        
        /// <summary>Refresh token reference for this session</summary>
        [MaxLength(128)]
        public string? RefreshTokenHash { get; set; }
    }
    
    /// <summary>
    /// Extends ApplicationUser with activity and session tracking
    /// </summary>
    public partial class ApplicationUser
    {
        // =============================================================================
        // ACTIVITY AND SESSION MANAGEMENT PROPERTIES
        // =============================================================================
        
        /// <summary>
        /// Security: Last activity timestamp for timeout management
        /// </summary>
        public DateTime? LastActivityAt { get; set; }
        
        /// <summary>
        /// Security: Last IP address used (hashed for privacy)
        /// </summary>
        [MaxLength(128)]
        public string? LastIpAddressHash { get; set; }
        
        /// <summary>
        /// Security: Current number of active sessions
        /// </summary>
        public int ActiveSessionCount { get; set; } = 0;
        
        /// <summary>
        /// Security: Maximum allowed concurrent sessions
        /// </summary>
        public int MaxConcurrentSessions { get; set; } = 5;
        
        /// <summary>
        /// Security: Failed login attempts counter (for lockout)
        /// </summary>
        public int ConsecutiveFailedLogins { get; set; } = 0;
        
        /// <summary>
        /// Security: When the failed login counter was last reset
        /// </summary>
        public DateTime? LastFailedLoginResetAt { get; set; }
        
        // =============================================================================
        // NAVIGATION PROPERTIES FOR SECURITY TRACKING
        // =============================================================================
        
        /// <summary>
        /// Security audit logs for this user
        /// </summary>
        public List<SecurityAuditLog> SecurityAuditLogs { get; set; } = new();
        
        /// <summary>
        /// Active sessions for this user
        /// </summary>
        public List<UserSession> Sessions { get; set; } = new();
    }
    
    /// <summary>
    /// Enhanced JWT claims structure with tenant and security context
    /// </summary>
    public static class BlendaClaimTypes
    {
        /// <summary>Tenant ID claim for multi-tenant authorization</summary>
        public const string TenantId = "tenant_id";
        
        /// <summary>Tenant admin status claim</summary>
        public const string TenantAdmin = "tenant_admin";
        
        /// <summary>Session ID claim for session tracking</summary>
        public const string SessionId = "session_id";
        
        /// <summary>Last activity timestamp claim</summary>
        public const string LastActivity = "last_activity";
        
        /// <summary>Security clearance level claim</summary>
        public const string SecurityLevel = "security_level";
        
        /// <summary>Account status claim for authorization policies</summary>
        public const string AccountStatus = "account_status";
    }
}
