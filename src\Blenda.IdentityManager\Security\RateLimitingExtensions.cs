using System.Threading.RateLimiting;
using System.Security.Claims;

namespace IdentityManager.Security
{
    /// <summary>
    /// Extensions for configuring advanced rate limiting policies
    /// </summary>
    public static class RateLimitingExtensions
    {
        /// <summary>
        /// Gets a role-based identifier for rate limiting purposes
        /// Combines user ID and role name for partitioning
        /// </summary>
        private static string GetRoleBasedIdentifier(HttpContext httpContext, string roleName)
        {
            var userId = httpContext.User?.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            var roleClaim = httpContext.User?.FindFirst(System.Security.Claims.ClaimTypes.Role)?.Value;
            var ip = httpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown";
            if (!string.IsNullOrEmpty(userId) && roleClaim == roleName)
            {
                return $"role:{roleName}:user:{userId}";
            }
            else if (!string.IsNullOrEmpty(userId))
            {
                return $"role:{roleName}:user:{userId}";
            }
            else
            {
                return $"role:{roleName}:ip:{ip}";
            }
        }

        /// <summary>
        /// Gets a user-based identifier for rate limiting purposes
        /// Enhanced with role-aware partitioning and session tracking
        /// Uses authenticated user ID when available, falls back to client identifier
        /// </summary>
        private static string GetUserBasedIdentifier(HttpContext httpContext)
        {
            if (httpContext.User?.Identity?.IsAuthenticated == true)
            {
                var userId = httpContext.User.FindFirst("sub")?.Value 
                    ?? httpContext.User.FindFirst("id")?.Value 
                    ?? httpContext.User.Identity.Name;
                
                if (!string.IsNullOrEmpty(userId))
                {
                    // ENHANCEMENT: Role-aware rate limiting for better user experience
                    var userRole = httpContext.User.FindFirst(ClaimTypes.Role)?.Value ?? "User";
                    var sessionId = httpContext.User.FindFirst("session_id")?.Value;
                    
                    // Include session for concurrent session management
                    var identifier = !string.IsNullOrEmpty(sessionId) 
                        ? $"user:{userId}:role:{userRole}:session:{sessionId[..8]}" 
                        : $"user:{userId}:role:{userRole}";
                    
                    return identifier;
                }
            }
            
            // Fall back to client-based identification for unauthenticated requests
            return GetClientIdentifier(httpContext);
        }

        /// <summary>
        /// Gets a client identifier for rate limiting purposes
        /// Uses a combination of IP address and User-Agent for better identification
        /// </summary>
        private static string GetClientIdentifier(HttpContext httpContext)
        {
            var ip = httpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown";
            var userAgent = httpContext.Request.Headers["User-Agent"].ToString();
            var userAgentHash = string.IsNullOrEmpty(userAgent) ? "no-ua" : userAgent.GetHashCode().ToString();
            var userId = httpContext.User?.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            return userId != null ? $"user:{userId}" : $"ip:{ip}:ua:{userAgentHash}";
        }
        /// <summary>
        /// Adds comprehensive rate limiting services with security-focused policies
        /// </summary>
        public static IServiceCollection AddSecurityRateLimiting(this IServiceCollection services, IWebHostEnvironment environment)
        {
            services.AddRateLimiter(options =>
            {
                // SECURITY: Enhanced global rate limiting policy with user-based partitioning
                options.GlobalLimiter = PartitionedRateLimiter.Create<HttpContext, string>(httpContext =>
                    RateLimitPartition.GetTokenBucketLimiter(
                        partitionKey: GetUserBasedIdentifier(httpContext),
                        factory: partition => new TokenBucketRateLimiterOptions
                        {
                            AutoReplenishment = true,
                            TokenLimit = environment.IsDevelopment() ? 1000 : 100,
                            QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                            QueueLimit = 10,
                            ReplenishmentPeriod = TimeSpan.FromMinutes(1),
                            TokensPerPeriod = environment.IsDevelopment() ? 1000 : 100
                        }));

                // SECURITY: Authentication endpoints - enhanced user-based partitioning
                options.AddPolicy("AuthenticationPolicy", httpContext =>
                    RateLimitPartition.GetTokenBucketLimiter(
                        partitionKey: GetUserBasedIdentifier(httpContext),
                        factory: partition => new TokenBucketRateLimiterOptions
                        {
                            AutoReplenishment = true,
                            TokenLimit = environment.IsDevelopment() ? 50 : 10,
                            QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                            QueueLimit = 4,
                            ReplenishmentPeriod = TimeSpan.FromMinutes(1),
                            TokensPerPeriod = 4
                        }));

                // SECURITY: Password reset and sensitive operations - very strict
                options.AddPolicy("SensitiveOperationsPolicy", httpContext =>
                    RateLimitPartition.GetTokenBucketLimiter(
                        partitionKey: GetClientIdentifier(httpContext),
                        factory: partition => new TokenBucketRateLimiterOptions
                        {
                            AutoReplenishment = true,
                            TokenLimit = environment.IsDevelopment() ? 10 : 3,
                            QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                            QueueLimit = 2,
                            ReplenishmentPeriod = TimeSpan.FromMinutes(5),
                            TokensPerPeriod = 1
                        }));

                // SECURITY: Email operations - hourly limits
                options.AddPolicy("EmailOperationsPolicy", httpContext =>
                    RateLimitPartition.GetFixedWindowLimiter(
                        partitionKey: GetClientIdentifier(httpContext),
                        factory: partition => new FixedWindowRateLimiterOptions
                        {
                            AutoReplenishment = true,
                            PermitLimit = environment.IsDevelopment() ? 20 : 5,
                            Window = TimeSpan.FromHours(1),
                            QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                            QueueLimit = 3
                        }));

                // SECURITY: Admin operations - moderate limiting
                options.AddPolicy("AdminOperationsPolicy", httpContext =>
                    RateLimitPartition.GetConcurrencyLimiter(
                        partitionKey: GetClientIdentifier(httpContext),
                        factory: partition => new ConcurrencyLimiterOptions
                        {
                            PermitLimit = environment.IsDevelopment() ? 20 : 10,
                            QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                            QueueLimit = 5
                        }));

                // SECURITY: Public endpoints - basic limiting
                options.AddPolicy("PublicPolicy", httpContext =>
                    RateLimitPartition.GetFixedWindowLimiter(
                        partitionKey: GetClientIdentifier(httpContext),
                        factory: partition => new FixedWindowRateLimiterOptions
                        {
                            AutoReplenishment = true,
                            PermitLimit = environment.IsDevelopment() ? 500 : 50,
                            Window = TimeSpan.FromMinutes(1),
                            QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                            QueueLimit = 20
                        }));

                // SECURITY: GDPR operations - moderate limiting for privacy/consent operations
                options.AddPolicy("GDPRPolicy", httpContext =>
                    RateLimitPartition.GetSlidingWindowLimiter(
                        partitionKey: GetUserBasedIdentifier(httpContext),
                        factory: partition => new SlidingWindowRateLimiterOptions
                        {
                            AutoReplenishment = true,
                            PermitLimit = environment.IsDevelopment() ? 60 : 30,
                            Window = TimeSpan.FromMinutes(5),
                            SegmentsPerWindow = 5,
                            QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                            QueueLimit = 10
                        }));

        // NUEVAS: Políticas específicas por rol
        // SECURITY: PlatformAdmin - Acceso privilegiado alto
        options.AddPolicy("PlatformAdminRateLimit", httpContext =>
            RateLimitPartition.GetTokenBucketLimiter(
                partitionKey: GetRoleBasedIdentifier(httpContext, "PlatformAdmin"),
                factory: partition => new TokenBucketRateLimiterOptions
                {
                    AutoReplenishment = true,
                    TokenLimit = environment.IsDevelopment() ? 1000 : 500,
                    QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                    QueueLimit = 100,
                    ReplenishmentPeriod = TimeSpan.FromMinutes(1),
                    TokensPerPeriod = 100
                }));

        // SECURITY: BusinessAdmin - Gestión empresarial moderada
        options.AddPolicy("BusinessAdminRateLimit", httpContext =>
            RateLimitPartition.GetSlidingWindowLimiter(
                partitionKey: GetRoleBasedIdentifier(httpContext, "BusinessAdmin"),
                factory: partition => new SlidingWindowRateLimiterOptions
                {
                    AutoReplenishment = true,
                    PermitLimit = environment.IsDevelopment() ? 300 : 150,
                    Window = TimeSpan.FromMinutes(5),
                    SegmentsPerWindow = 5,
                    QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                    QueueLimit = 20
                }));

        // SECURITY: HRManager - Gestión de personal
        options.AddPolicy("HRManagerRateLimit", httpContext =>
            RateLimitPartition.GetFixedWindowLimiter(
                partitionKey: GetRoleBasedIdentifier(httpContext, "HRManager"),
                factory: partition => new FixedWindowRateLimiterOptions
                {
                    AutoReplenishment = true,
                    PermitLimit = environment.IsDevelopment() ? 200 : 100,
                    Window = TimeSpan.FromMinutes(5),
                    QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                    QueueLimit = 15
                }));

        // SECURITY: Operations - Gestión de operaciones y turnos (NUEVO para módulo asistencia DT)
        options.AddPolicy("OperationsRateLimit", httpContext =>
            RateLimitPartition.GetSlidingWindowLimiter(
                partitionKey: GetRoleBasedIdentifier(httpContext, "Operations"),
                factory: partition => new SlidingWindowRateLimiterOptions
                {
                    AutoReplenishment = true,
                    PermitLimit = environment.IsDevelopment() ? 250 : 120,
                    Window = TimeSpan.FromMinutes(5),
                    SegmentsPerWindow = 5,
                    QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                    QueueLimit = 20
                }));

        // SECURITY: Supervisor - Supervisión de equipos y empleados (NUEVO para módulo asistencia DT)
        options.AddPolicy("SupervisorRateLimit", httpContext =>
            RateLimitPartition.GetFixedWindowLimiter(
                partitionKey: GetRoleBasedIdentifier(httpContext, "Supervisor"),
                factory: partition => new FixedWindowRateLimiterOptions
                {
                    AutoReplenishment = true,
                    PermitLimit = environment.IsDevelopment() ? 150 : 75,
                    Window = TimeSpan.FromMinutes(5),
                    QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                    QueueLimit = 12
                }));

        // SECURITY: ExternalAuditor - Acceso restringido
        options.AddPolicy("ExternalAuditorRateLimit", httpContext =>
            RateLimitPartition.GetTokenBucketLimiter(
                partitionKey: GetRoleBasedIdentifier(httpContext, "ExternalAuditor"),
                factory: partition => new TokenBucketRateLimiterOptions
                {
                    AutoReplenishment = true,
                    TokenLimit = environment.IsDevelopment() ? 50 : 25,
                    QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                    QueueLimit = 5,
                    ReplenishmentPeriod = TimeSpan.FromMinutes(10),
                    TokensPerPeriod = 10
                }));

        // SECURITY: LaborInspector - Acceso gubernamental controlado
        options.AddPolicy("LaborInspectorRateLimit", httpContext =>
            RateLimitPartition.GetFixedWindowLimiter(
                partitionKey: GetRoleBasedIdentifier(httpContext, "LaborInspector"),
                factory: partition => new FixedWindowRateLimiterOptions
                {
                    AutoReplenishment = true,
                    PermitLimit = environment.IsDevelopment() ? 100 : 30,
                    Window = TimeSpan.FromMinutes(15),
                    QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                    QueueLimit = 10
                }));

        // SECURITY: Employee - Acceso básico para empleados
        options.AddPolicy("EmployeeRateLimit", httpContext =>
            RateLimitPartition.GetSlidingWindowLimiter(
                partitionKey: GetRoleBasedIdentifier(httpContext, "Employee"),
                factory: partition => new SlidingWindowRateLimiterOptions
                {
                    AutoReplenishment = true,
                    PermitLimit = environment.IsDevelopment() ? 100 : 50,
                    Window = TimeSpan.FromMinutes(10),
                    SegmentsPerWindow = 2,
                    QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                    QueueLimit = 10
                }));

        // SECURITY: TechnicalSupport - Soporte técnico moderado
        options.AddPolicy("TechnicalSupportRateLimit", httpContext =>
            RateLimitPartition.GetConcurrencyLimiter(
                partitionKey: GetRoleBasedIdentifier(httpContext, "TechnicalSupport"),
                factory: partition => new ConcurrencyLimiterOptions
                {
                    PermitLimit = environment.IsDevelopment() ? 20 : 15,
                    QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                    QueueLimit = 5
                }));

        // RTM CRITICAL ROLES: Rate limiting para roles críticos del RTM

        // SECURITY: ComplianceOfficer - Gestión de cumplimiento regulatorio
        options.AddPolicy("ComplianceOfficerRateLimit", httpContext =>
            RateLimitPartition.GetFixedWindowLimiter(
                partitionKey: GetRoleBasedIdentifier(httpContext, "ComplianceOfficer"),
                factory: partition => new FixedWindowRateLimiterOptions
                {
                    AutoReplenishment = true,
                    PermitLimit = environment.IsDevelopment() ? 150 : 75,
                    Window = TimeSpan.FromMinutes(5),
                    QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                    QueueLimit = 15
                }));

        // SECURITY: LegalCounsel - Asesoría legal especializada
        options.AddPolicy("LegalCounselRateLimit", httpContext =>
            RateLimitPartition.GetSlidingWindowLimiter(
                partitionKey: GetRoleBasedIdentifier(httpContext, "LegalCounsel"),
                factory: partition => new SlidingWindowRateLimiterOptions
                {
                    AutoReplenishment = true,
                    PermitLimit = environment.IsDevelopment() ? 120 : 60,
                    Window = TimeSpan.FromMinutes(10),
                    SegmentsPerWindow = 2,
                    QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                    QueueLimit = 12
                }));

        // SECURITY: DTRelationsSpecialist - Gestión de relaciones laborales DT
        options.AddPolicy("DTRelationsSpecialistRateLimit", httpContext =>
            RateLimitPartition.GetTokenBucketLimiter(
                partitionKey: GetRoleBasedIdentifier(httpContext, "DTRelationsSpecialist"),
                factory: partition => new TokenBucketRateLimiterOptions
                {
                    AutoReplenishment = true,
                    TokenLimit = environment.IsDevelopment() ? 80 : 40,
                    QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                    QueueLimit = 8,
                    ReplenishmentPeriod = TimeSpan.FromMinutes(15),
                    TokensPerPeriod = 20
                }));

        // SECURITY: TechnicalLead - Liderazgo técnico y arquitectura
        options.AddPolicy("TechnicalLeadRateLimit", httpContext =>
            RateLimitPartition.GetSlidingWindowLimiter(
                partitionKey: GetRoleBasedIdentifier(httpContext, "TechnicalLead"),
                factory: partition => new SlidingWindowRateLimiterOptions
                {
                    AutoReplenishment = true,
                    PermitLimit = environment.IsDevelopment() ? 200 : 100,
                    Window = TimeSpan.FromMinutes(5),
                    SegmentsPerWindow = 5,
                    QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                    QueueLimit = 20
                }));

                // SECURITY: Handle rate limit rejections with detailed logging
                options.OnRejected = async (context, token) =>
                {
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                    var clientId = GetClientIdentifier(context.HttpContext);
                    var userId = GetUserBasedIdentifier(context.HttpContext);
                    var endpoint = context.HttpContext.Request.Path;
                    var method = context.HttpContext.Request.Method;
                    var userAgent = context.HttpContext.Request.Headers["User-Agent"].ToString();
                    var referer = context.HttpContext.Request.Headers["Referer"].ToString();

                    // ENHANCEMENT: Advanced security event logging with metrics
                    var userRole = context.HttpContext.User?.FindFirst(ClaimTypes.Role)?.Value ?? "Anonymous";
                    var isAuthenticated = context.HttpContext.User?.Identity?.IsAuthenticated ?? false;
                    
                    logger.LogWarning("Rate limit exceeded - Client: {ClientId}, User: {UserId}, Role: {UserRole}, Authenticated: {IsAuthenticated}, Endpoint: {Endpoint}, Method: {Method}, UserAgent: {UserAgent}, Referer: {Referer}",
                        clientId, userId, userRole, isAuthenticated, endpoint, method, userAgent, referer);

                    // ENHANCEMENT: Security metrics collection for monitoring
                    try
                    {
                        var metricsLogger = context.HttpContext.RequestServices.GetService<ILogger<Program>>();
                        metricsLogger?.LogInformation("RateLimit.Rejected|Client:{ClientId}|Role:{UserRole}|Endpoint:{Endpoint}|Method:{Method}",
                            clientId, userRole, endpoint, method);
                    }
                    catch
                    {
                        // Fail silently for metrics - don't impact security response
                    }

                    // Set comprehensive response headers
                    context.HttpContext.Response.StatusCode = 429;
                    context.HttpContext.Response.Headers["X-RateLimit-Policy"] = "Enhanced-Security-Policy";
                    context.HttpContext.Response.Headers["X-Content-Type-Options"] = "nosniff";
                    context.HttpContext.Response.Headers["X-Frame-Options"] = "DENY";

                    if (context.Lease.TryGetMetadata(MetadataName.RetryAfter, out var retryAfter))
                    {
                        context.HttpContext.Response.Headers["Retry-After"] = retryAfter.ToString();
                    }

                    // ENHANCEMENT: Return structured error response with security context
                    var response = new
                    {
                        error = "rate_limit_exceeded",
                        message = "Too many requests. Please try again later.",
                        details = new
                        {
                            policy = "Enhanced Security Policy",
                            retryAfter = context.HttpContext.Response.Headers["Retry-After"].ToString(),
                            endpoint = endpoint.ToString(),
                            timestamp = DateTime.UtcNow,
                            requestId = context.HttpContext.TraceIdentifier,
                            userRole = isAuthenticated ? userRole : "Anonymous",
                            rateLimitType = isAuthenticated ? "User-Based" : "Client-Based"
                        }
                    };

                    context.HttpContext.Response.ContentType = "application/json";
                    await context.HttpContext.Response.WriteAsync(
                        System.Text.Json.JsonSerializer.Serialize(response), token);
                };
            });

            return services;
        }

    }
}
