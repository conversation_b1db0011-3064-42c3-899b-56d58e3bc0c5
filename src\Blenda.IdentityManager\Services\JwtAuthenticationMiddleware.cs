using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace IdentityManager
{
    public class JwtAuthenticationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IConfiguration _configuration;
        private readonly ILogger<JwtAuthenticationMiddleware> _logger;

        public JwtAuthenticationMiddleware(RequestDelegate next, IConfiguration configuration, ILogger<JwtAuthenticationMiddleware> logger)
        {
            _next = next;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Lista de endpoints que NO requieren JWT authentication
            var excludedPaths = new[]
            {
                "/login",
                "/register", 
                "/confirm-email",
                "/forgot-password",
                "/reset-password",
                "/refresh-token",
                "/health"
            };

            // Si la ruta está excluida, continuar sin validación JWT
            if (excludedPaths.Any(path => context.Request.Path.StartsWithSegments(path, StringComparison.OrdinalIgnoreCase)))
            {
                _logger.LogInformation("Path {Path} is excluded from JWT validation", context.Request.Path);
                await _next(context);
                return;
            }

            var token = context.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();
            if (string.IsNullOrEmpty(token))
            {
                _logger.LogInformation("No Authorization header found for protected path {Path}", context.Request.Path);
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                await context.Response.WriteAsync("Unauthorized: No token provided");
                return;
            }

            try
            {
                var handler = new JwtSecurityTokenHandler();
                var jwt = handler.ReadJwtToken(token);
                _logger.LogInformation("Token header: {Header}, Payload: {Payload}", 
                    System.Text.Json.JsonSerializer.Serialize(jwt.Header), 
                    System.Text.Json.JsonSerializer.Serialize(jwt.Payload));

                var jwtKey = _configuration["Jwt:Key"];
                if (string.IsNullOrEmpty(jwtKey))
                {
                    _logger.LogError("JWT signing key is missing in configuration.");
                    context.Response.StatusCode = StatusCodes.Status500InternalServerError;
                    await context.Response.WriteAsync("Internal server error: JWT key not configured");
                    return;
                }
                var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtKey));
                var tokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ValidIssuer = _configuration["Jwt:Issuer"],
                    ValidAudience = _configuration["Jwt:Audience"],
                    IssuerSigningKey = key,
                    ClockSkew = TimeSpan.FromMinutes(1)
                };

                var principal = handler.ValidateToken(token, tokenValidationParameters, out var validatedToken);
                context.User = principal;
                _logger.LogInformation("JWT validated: User: {User}, Roles: {Roles}", 
                    principal?.FindFirst(ClaimTypes.Email)?.Value, 
                    string.Join(", ", principal?.FindAll(ClaimTypes.Role).Select(c => c.Value) ?? Array.Empty<string>()));

                await _next(context);
            }
            catch (SecurityTokenException ex)
            {
                _logger.LogError("JWT validation failed: {Exception}, Token: {Token}", ex.Message, token);
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                await context.Response.WriteAsync("Unauthorized: Invalid token");
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error in JWT validation: {Exception}, Token: {Token}", ex.Message, token);
                context.Response.StatusCode = StatusCodes.Status500InternalServerError;
                await context.Response.WriteAsync("Internal server error");
            }
        }
    }

    public static class JwtAuthenticationMiddlewareExtensions
    {
        public static IApplicationBuilder UseJwtAuthentication(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<JwtAuthenticationMiddleware>();
        }
    }
}