using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using Microsoft.AspNetCore.DataProtection;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace IdentityManager.Services
{
    /// <summary>
    /// JWT-based refresh token protector that eliminates Data Protection key sharing issues in Kubernetes pods.
    /// This implementation uses JWT tokens for refresh tokens, making them stateless and pod-independent.
    /// Includes backward compatibility with existing Data Protection tokens during migration.
    /// </summary>
    public class JwtRefreshTokenProtector : ISecureDataFormat<AuthenticationTicket>
    {
        private readonly TicketSerializer _serializer = TicketSerializer.Default;
        private readonly string _jwtKey;
        private readonly string _issuer;
        private readonly string _audience;
        private readonly ILogger<JwtRefreshTokenProtector> _logger;
        private readonly IDataProtector? _fallbackProtector;

        public JwtRefreshTokenProtector(IConfiguration configuration, ILogger<JwtRefreshTokenProtector> logger, IDataProtectionProvider? dataProtectionProvider = null)
        {
            _logger = logger;
            _jwtKey = configuration["Jwt:Key"] ?? throw new InvalidOperationException("JWT Key not configured for refresh tokens");
            _issuer = configuration["Jwt:Issuer"] ?? throw new InvalidOperationException("JWT Issuer not configured");
            _audience = configuration["Jwt:Audience"] ?? throw new InvalidOperationException("JWT Audience not configured");
            
            // Validate key length for security
            if (_jwtKey.Length < 32)
            {
                throw new InvalidOperationException("JWT Key must be at least 32 characters for security");
            }
            
            // BACKWARD COMPATIBILITY: Create fallback Data Protection protector if available
            try
            {
                if (dataProtectionProvider != null)
                {
                    _fallbackProtector = dataProtectionProvider.CreateProtector("RefreshToken");
                    _logger.LogInformation("JWT Refresh Token Protector initialized with Data Protection fallback for migration compatibility");
                }
                else
                {
                    _logger.LogInformation("JWT Refresh Token Protector initialized without Data Protection fallback");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to initialize Data Protection fallback, proceeding with JWT-only mode");
                _fallbackProtector = null;
            }
            
            _logger.LogInformation("JWT Refresh Token Protector initialized with issuer: {Issuer}, audience: {Audience}", _issuer, _audience);
        }

        public string Protect(AuthenticationTicket data) => Protect(data, null);

        public string Protect(AuthenticationTicket data, string? purpose)
        {
            try
            {
                if (data?.Principal?.Identity?.IsAuthenticated != true)
                {
                    _logger.LogWarning("Attempted to protect unauthenticated ticket");
                    throw new ArgumentException("Cannot protect unauthenticated ticket");
                }

                var claims = data.Principal.Claims.ToList();
                
                // Add refresh token specific claims
                claims.Add(new Claim("token_type", "refresh"));
                claims.Add(new Claim("iat", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString()));
                claims.Add(new Claim("nbf", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString()));
                
                // Add expiration from ticket properties or default to 7 days
                var expiresUtc = data.Properties.ExpiresUtc ?? DateTimeOffset.UtcNow.AddDays(7);
                claims.Add(new Claim("exp", expiresUtc.ToUnixTimeSeconds().ToString()));
                
                // Add purpose if provided
                if (!string.IsNullOrEmpty(purpose))
                {
                    claims.Add(new Claim("purpose", purpose));
                }

                var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtKey));
                var token = new JwtSecurityToken(
                    issuer: _issuer,
                    audience: _audience,
                    claims: claims,
                    expires: expiresUtc.DateTime,
                    signingCredentials: new SigningCredentials(key, SecurityAlgorithms.HmacSha256)
                );

                var tokenString = new JwtSecurityTokenHandler().WriteToken(token);
                
                _logger.LogDebug("Protected refresh token for user {UserId} with expiration {Expiration}", 
                    claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value ?? "Unknown",
                    expiresUtc);
                
                return tokenString;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to protect refresh token");
                throw;
            }
        }

        public AuthenticationTicket? Unprotect(string? protectedText) => Unprotect(protectedText, null);

        public AuthenticationTicket? Unprotect(string? protectedText, string? purpose)
        {
            if (string.IsNullOrEmpty(protectedText))
            {
                _logger.LogWarning("Attempted to unprotect null or empty token");
                return null;
            }

            // First try JWT format (new tokens)
            var jwtResult = TryUnprotectJwt(protectedText, purpose);
            if (jwtResult != null)
            {
                _logger.LogDebug("Successfully unprotected JWT refresh token");
                return jwtResult;
            }

            // BACKWARD COMPATIBILITY: Try Data Protection format (old tokens)
            var dataProtectionResult = TryUnprotectDataProtection(protectedText, purpose);
            if (dataProtectionResult != null)
            {
                _logger.LogDebug("Successfully unprotected legacy Data Protection refresh token");
                return dataProtectionResult;
            }

            _logger.LogWarning("Failed to unprotect refresh token in both JWT and Data Protection formats");
            return null;
        }

        private AuthenticationTicket? TryUnprotectJwt(string protectedText, string? purpose)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                
                // Quick check if it looks like a JWT
                if (!tokenHandler.CanReadToken(protectedText))
                {
                    return null;
                }
                
                var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtKey));

                // Validate the JWT token
                var principal = tokenHandler.ValidateToken(protectedText, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = key,
                    ValidateIssuer = true,
                    ValidIssuer = _issuer,
                    ValidateAudience = true,
                    ValidAudience = _audience,
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.FromMinutes(5), // Allow 5 minute clock skew
                    RequireExpirationTime = true,
                    RequireSignedTokens = true
                }, out SecurityToken validatedToken);

                var jwtToken = (JwtSecurityToken)validatedToken;
                
                // Verify it's a refresh token
                var tokenTypeClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == "token_type");
                if (tokenTypeClaim?.Value != "refresh")
                {
                    _logger.LogDebug("Token is not a refresh token. Token type: {TokenType}", tokenTypeClaim?.Value ?? "None");
                    return null;
                }
                
                // Verify purpose if provided
                if (!string.IsNullOrEmpty(purpose))
                {
                    var purposeClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == "purpose");
                    if (purposeClaim?.Value != purpose)
                    {
                        _logger.LogDebug("Token purpose mismatch. Expected: {Expected}, Actual: {Actual}", 
                            purpose, purposeClaim?.Value ?? "None");
                        return null;
                    }
                }

                // Create authentication properties with expiration
                var properties = new AuthenticationProperties();
                if (jwtToken.ValidTo != DateTime.MinValue)
                {
                    properties.ExpiresUtc = jwtToken.ValidTo;
                }
                properties.IsPersistent = true;

                // Filter out JWT-specific claims for the authentication ticket
                var filteredClaims = jwtToken.Claims
                    .Where(c => c.Type != "token_type" && 
                               c.Type != "purpose" && 
                               c.Type != "iat" && 
                               c.Type != "nbf" && 
                               c.Type != "exp" &&
                               c.Type != "iss" &&
                               c.Type != "aud")
                    .ToList();

                var identity = new ClaimsIdentity(filteredClaims, "JWT");
                var ticketPrincipal = new ClaimsPrincipal(identity);

                return new AuthenticationTicket(ticketPrincipal, properties, "Bearer");
            }
            catch (SecurityTokenExpiredException)
            {
                _logger.LogDebug("JWT refresh token has expired");
                return null;
            }
            catch (SecurityTokenInvalidSignatureException)
            {
                _logger.LogDebug("JWT refresh token has invalid signature");
                return null;
            }
            catch (SecurityTokenValidationException)
            {
                _logger.LogDebug("JWT refresh token validation failed");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Error processing as JWT token");
                return null;
            }
        }

        private AuthenticationTicket? TryUnprotectDataProtection(string protectedText, string? purpose)
        {
            if (_fallbackProtector == null)
            {
                return null;
            }

            try
            {
                var protectedBytes = Convert.FromBase64String(protectedText);
                var bytes = _fallbackProtector.Unprotect(protectedBytes);
                return _serializer.Deserialize(bytes);
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Error processing as Data Protection token");
                return null;
            }
        }
    }
}
