using IdentityManager.Models;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace IdentityManager.Services
{
    /// <summary>
    /// Service for validating tenant references with GRP transactional API
    /// Does not manage tenants - only validates and caches tenant information
    /// </summary>
    public interface ITenantReferenceService
    {
        /// <summary>
        /// Validates that a tenant exists in the GRP transactional database
        /// </summary>
        /// <param name="tenantId">Tenant ID to validate</param>
        /// <returns>True if tenant exists and is active</returns>
        Task<bool> ValidateTenantExistsAsync(Guid tenantId);
        
        /// <summary>
        /// Gets basic tenant information from GRP API for caching
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <returns>Tenant information or null if not found</returns>
        Task<TenantInfo?> GetTenantInfoAsync(Guid tenantId);
        
        /// <summary>
        /// Validates that a user has access to a specific tenant
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="tenantId">Tenant ID</param>
        /// <returns>True if user has access to tenant</returns>
        Task<bool> ValidateUserTenantAccessAsync(string userId, Guid tenantId);
    }

    /// <summary>
    /// Basic tenant information for caching purposes
    /// </summary>
    public class TenantInfo
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Subdomain { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime LastValidated { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Implementation of tenant reference validation service
    /// Integrates with GRP transactional API for tenant validation
    /// </summary>
    public class TenantReferenceService : ITenantReferenceService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<TenantReferenceService> _logger;
        private readonly IMemoryCache _cache;

        public TenantReferenceService(
            HttpClient httpClient,
            IConfiguration configuration,
            ILogger<TenantReferenceService> logger,
            IMemoryCache cache)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _logger = logger;
            _cache = cache;
        }

        /// <summary>
        /// Validates tenant existence with GRP API
        /// </summary>
        public async Task<bool> ValidateTenantExistsAsync(Guid tenantId)
        {
            try
            {
                // Check cache first
                var cacheKey = $"tenant_validation_{tenantId}";
                if (_cache.TryGetValue(cacheKey, out bool cachedResult))
                {
                    return cachedResult;
                }

                // Call GRP API to validate tenant
                var grpApiUrl = _configuration["GRPApi:BaseUrl"] ?? "http://localhost:5000";
                var response = await _httpClient.GetAsync($"{grpApiUrl}/api/tenants/{tenantId}/validate");
                
                var isValid = response.IsSuccessStatusCode;
                
                // Cache result for 5 minutes
                _cache.Set(cacheKey, isValid, TimeSpan.FromMinutes(5));
                
                _logger.LogInformation("Tenant validation for {TenantId}: {IsValid}", tenantId, isValid);
                return isValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating tenant {TenantId}", tenantId);
                return false;
            }
        }

        /// <summary>
        /// Gets tenant information from GRP API
        /// </summary>
        public async Task<TenantInfo?> GetTenantInfoAsync(Guid tenantId)
        {
            try
            {
                // Check cache first
                var cacheKey = $"tenant_info_{tenantId}";
                if (_cache.TryGetValue(cacheKey, out TenantInfo? cachedInfo))
                {
                    return cachedInfo;
                }

                // Call GRP API to get tenant info
                var grpApiUrl = _configuration["GRPApi:BaseUrl"] ?? "http://localhost:5000";
                var response = await _httpClient.GetAsync($"{grpApiUrl}/api/tenants/{tenantId}");
                
                if (!response.IsSuccessStatusCode)
                {
                    return null;
                }

                var content = await response.Content.ReadAsStringAsync();
                // Note: In real implementation, deserialize JSON response
                // For now, return basic structure
                var tenantInfo = new TenantInfo
                {
                    Id = tenantId,
                    Name = "Tenant Name", // Would come from API response
                    Subdomain = "subdomain", // Would come from API response
                    IsActive = true,
                    LastValidated = DateTime.UtcNow
                };
                
                // Cache for 15 minutes
                _cache.Set(cacheKey, tenantInfo, TimeSpan.FromMinutes(15));
                
                return tenantInfo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting tenant info for {TenantId}", tenantId);
                return null;
            }
        }

        /// <summary>
        /// Validates user access to tenant
        /// </summary>
        public async Task<bool> ValidateUserTenantAccessAsync(string userId, Guid tenantId)
        {
            try
            {
                // For now, simple validation - can be enhanced with GRP API call
                if (string.IsNullOrEmpty(userId) || tenantId == Guid.Empty)
                {
                    return false;
                }

                // First validate tenant exists
                var tenantExists = await ValidateTenantExistsAsync(tenantId);
                if (!tenantExists)
                {
                    return false;
                }

                // Additional validation logic can be added here
                // For example, calling GRP API to verify user-tenant relationship
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating user {UserId} access to tenant {TenantId}", userId, tenantId);
                return false;
            }
        }
    }
}
