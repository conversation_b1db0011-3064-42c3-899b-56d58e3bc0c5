// Licensed to the .NET Foundation under one or more agreements.
// The .NET Foundation licenses this file to you under the MIT license.

using System.ComponentModel.DataAnnotations;
using IdentityManager.Services.Configuration;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;

namespace IdentityManager.Services;

/// <summary>
/// Custom reset password request model with optional resetCode
/// </summary>
public class CustomResetPasswordRequest
{
    [Required]
    public required string Email { get; init; }
    
    [Required]
    public required string NewPassword { get; init; }
    
    public string? ResetCode { get; init; }
}

/// <summary>
/// Provides extension methods for <see cref="IEndpointRouteBuilder"/> to add identity endpoints.
/// </summary>
public static class CustomIdentityApiEndpointRouteBuilderExtensions
{
    /// <summary>
    /// Add endpoints for registering, logging in, and logging out using ASP.NET Core Identity.
    /// </summary>
    /// <typeparam name="TUser">The type describing the user. This should match the generic parameter in <see cref="UserManager{TUser}"/>.</typeparam>
    /// <param name="endpoints">
    /// The <see cref="IEndpointRouteBuilder"/> to add the identity endpoints to.
    /// Call <see cref="EndpointRouteBuilderExtensions.MapGroup(IEndpointRouteBuilder, string)"/> to add a prefix to all the endpoints.
    /// </param>
    /// <returns>An <see cref="IEndpointConventionBuilder"/> to further customize the added endpoints.</returns>
    public static IEndpointConventionBuilder MapCustomIdentityApi<TUser>(this IEndpointRouteBuilder endpoints)
        where TUser : class, new()
    {
        ArgumentNullException.ThrowIfNull(endpoints);

        var routeGroup = endpoints.MapGroup("");

        // Configure endpoints using our new handler pattern
        routeGroup.MapAuthenticationEndpoints<TUser>();
        routeGroup.MapRegistrationEndpoints<TUser>();
        routeGroup.MapAccountManagementEndpoints<TUser>();
        routeGroup.MapTwoFactorEndpoints<TUser>();

        return routeGroup;
    }
}