using Microsoft.AspNetCore.Identity;
using IdentityManager.Models;
using IdentityManager.Data;
using IdentityManager.Services.Core;
using System.Security.Cryptography;
using System.Text;

namespace IdentityManager.Services.Core;

/// <summary>
/// Service for security auditing and logging authentication events
/// </summary>
public class SecurityAuditService : ISecurityAuditService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<SecurityAuditService> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public SecurityAuditService(
        ApplicationDbContext dbContext,
        ILogger<SecurityAuditService> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _dbContext = dbContext;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    /// <inheritdoc />
    public async Task LogSecurityEventAsync(string? userId, string email, string eventType, bool success, 
        string? failureReason = null, int? riskScore = null)
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            var userAgent = httpContext?.Request.Headers.UserAgent.ToString();
            var ipAddress = GetClientIpAddress(httpContext);

            // Calculate risk score if not provided
            var calculatedRiskScore = riskScore ?? CalculateRiskScore(eventType, success, userAgent, ipAddress);

            var auditEntry = new SecurityAuditLog
            {
                UserId = userId,
                EventType = eventType,
                Success = success,
                FailureReason = failureReason,
                IpAddressHash = HashIpAddress(ipAddress),
                UserAgent = userAgent,
                EventTimestamp = DateTime.UtcNow,
                RiskScore = calculatedRiskScore,
                SecurityMetadata = CreateSecurityMetadata(httpContext, email, eventType)
            };

            _dbContext.SecurityAuditLogs.Add(auditEntry);
            await _dbContext.SaveChangesAsync();

            // Log to application logger as well
            if (success)
            {
                _logger.LogInformation("Security event {EventType} successful for {Email} (Risk: {RiskScore})", 
                    eventType, email, calculatedRiskScore);
            }
            else
            {
                _logger.LogWarning("Security event {EventType} failed for {Email}. Reason: {Reason} (Risk: {RiskScore})", 
                    eventType, email, failureReason ?? "Unknown", calculatedRiskScore);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to log security audit for {Email}", email);
            // Don't throw - audit logging failure shouldn't break authentication
        }
    }

    /// <inheritdoc />
    public int CalculateRiskScore(string eventType, bool success, string? userAgent, string? ipAddress)
    {
        var baseScore = GetEventTypeBaseScore(eventType);
        
        // Increase risk for failed attempts
        if (!success)
        {
            baseScore += 30;
        }

        // Check for suspicious user agent patterns
        if (IsSuspiciousUserAgent(userAgent))
        {
            baseScore += 20;
        }

        // Check for known problematic IP ranges (simplified example)
        if (IsSuspiciousIpAddress(ipAddress))
        {
            baseScore += 25;
        }

        // Additional risk factors based on time of day, frequency, etc. could be added here

        // Ensure score is within valid range
        return Math.Min(Math.Max(baseScore, 1), 100);
    }

    /// <summary>
    /// Gets the base risk score for different event types
    /// </summary>
    private static int GetEventTypeBaseScore(string eventType)
    {
        return eventType switch
        {
            "Login" => 10,
            "LoginEnhanced" => 15,
            "TokenRefresh" => 5,
            "Logout" => 1,
            "PasswordReset" => 20,
            "EmailConfirmation" => 5,
            "TwoFactorAuth" => 15,
            "AdminAction" => 30,
            _ => 10
        };
    }

    /// <summary>
    /// Checks if user agent string appears suspicious
    /// </summary>
    private static bool IsSuspiciousUserAgent(string? userAgent)
    {
        if (string.IsNullOrEmpty(userAgent))
            return true;

        var suspiciousPatterns = new[]
        {
            "bot", "crawler", "spider", "scraper",
            "curl", "wget", "python", "powershell"
        };

        return suspiciousPatterns.Any(pattern => 
            userAgent.Contains(pattern, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Checks if IP address is from a suspicious range
    /// </summary>
    private static bool IsSuspiciousIpAddress(string? ipAddress)
    {
        if (string.IsNullOrEmpty(ipAddress))
            return false;

        // This is a simplified example - in production, you'd check against
        // threat intelligence feeds, known VPN/proxy ranges, etc.
        var suspiciousRanges = new[]
        {
            "127.0.0.1", // Localhost might be suspicious in some contexts
            "169.254.", // Link-local addresses
            "********"  // Common router address
        };

        return suspiciousRanges.Any(range => ipAddress.StartsWith(range));
    }

    /// <summary>
    /// Extracts client IP address from HTTP context
    /// </summary>
    private static string? GetClientIpAddress(HttpContext? httpContext)
    {
        if (httpContext == null)
            return null;

        // Check for IP in X-Forwarded-For header (load balancer scenarios)
        var forwardedFor = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        // Check X-Real-IP header
        var realIp = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        // Fall back to remote IP address
        return httpContext.Connection.RemoteIpAddress?.ToString();
    }

    /// <summary>
    /// Creates a secure hash of an IP address for privacy protection
    /// </summary>
    private static string HashIpAddress(string? ipAddress)
    {
        if (string.IsNullOrEmpty(ipAddress))
            return string.Empty;

        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(ipAddress));
        return Convert.ToBase64String(hashedBytes);
    }

    /// <summary>
    /// Creates security metadata for audit logging
    /// </summary>
    private static string CreateSecurityMetadata(HttpContext? httpContext, string email, string eventType)
    {
        var metadata = new Dictionary<string, object>();

        if (httpContext != null)
        {
            metadata["RequestId"] = httpContext.TraceIdentifier;
            metadata["Protocol"] = httpContext.Request.Protocol;
            metadata["Method"] = httpContext.Request.Method;
            metadata["Host"] = httpContext.Request.Host.ToString();
            
            // Add headers that might be relevant for security analysis
            if (httpContext.Request.Headers.ContainsKey("Accept-Language"))
            {
                metadata["AcceptLanguage"] = httpContext.Request.Headers["Accept-Language"].ToString();
            }
            
            if (httpContext.Request.Headers.ContainsKey("Accept-Encoding"))
            {
                metadata["AcceptEncoding"] = httpContext.Request.Headers["Accept-Encoding"].ToString();
            }
        }

        metadata["EventType"] = eventType;
        metadata["EmailDomain"] = email.Split('@').LastOrDefault() ?? "unknown";
        metadata["Timestamp"] = DateTime.UtcNow.ToString("O");

        return System.Text.Json.JsonSerializer.Serialize(metadata);
    }
}