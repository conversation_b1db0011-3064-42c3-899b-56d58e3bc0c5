using System.ComponentModel.DataAnnotations;

namespace IdentityManager.Models;

// =============================================================================
// ACTIVITY AND SESSION MANAGEMENT DTOs
// =============================================================================

/// <summary>
/// Response model for user activity summary
/// </summary>
public class UserActivityResponse
{
    /// <summary>Current number of active sessions</summary>
    public int ActiveSessionCount { get; set; }
    
    /// <summary>Last login timestamp</summary>
    public DateTime? LastLoginAt { get; set; }
    
    /// <summary>Last activity timestamp</summary>
    public DateTime? LastActivityAt { get; set; }
    
    /// <summary>Total number of login attempts in last 30 days</summary>
    public int RecentLoginAttempts { get; set; }
    
    /// <summary>Number of failed login attempts in last 30 days</summary>
    public int RecentFailedLogins { get; set; }
    
    /// <summary>Account status</summary>
    public string AccountStatus { get; set; } = "Active";
}

/// <summary>
/// Response model for active user sessions
/// </summary>
public class ActiveSessionResponse
{
    /// <summary>Session identifier</summary>
    public string SessionId { get; set; } = string.Empty;
    
    /// <summary>When the session was created</summary>
    public DateTime CreatedAt { get; set; }
    
    /// <summary>Last activity in this session</summary>
    public DateTime LastActivityAt { get; set; }
    
    /// <summary>Device/browser information</summary>
    public string? DeviceInfo { get; set; }
    
    /// <summary>Approximate location (if available)</summary>
    public string? Location { get; set; }
    
    /// <summary>Whether this is the current session</summary>
    public bool IsCurrent { get; set; }
    
    /// <summary>Session expiration date</summary>
    public DateTime ExpiresAt { get; set; }
    
    /// <summary>IP address (masked for security)</summary>
    public string? IpAddressMasked { get; set; }
}

/// <summary>
/// Response model for login history
/// </summary>
public class LoginHistoryResponse
{
    /// <summary>When the login attempt occurred</summary>
    public DateTime Timestamp { get; set; }
    
    /// <summary>Type of login (Login, LoginEnhanced, RecoveryCodeLogin, etc.)</summary>
    public string LoginType { get; set; } = string.Empty;
    
    /// <summary>Whether the login was successful</summary>
    public bool Success { get; set; }
    
    /// <summary>Reason for failure (if applicable)</summary>
    public string? FailureReason { get; set; }
    
    /// <summary>Device/browser information</summary>
    public string? DeviceInfo { get; set; }
    
    /// <summary>Approximate location (if available)</summary>
    public string? Location { get; set; }
    
    /// <summary>IP address (masked for security)</summary>
    public string? IpAddressMasked { get; set; }
    
    /// <summary>Risk score for this attempt (0-100)</summary>
    public int RiskScore { get; set; }
}

/// <summary>
/// Request model for terminating a session
/// </summary>
public class TerminateSessionRequest
{
    /// <summary>Session ID to terminate</summary>
    [Required]
    [MaxLength(128)]
    public string SessionId { get; set; } = string.Empty;
    
    /// <summary>Optional reason for termination</summary>
    [MaxLength(200)]
    public string? Reason { get; set; }
}

/// <summary>
/// Response model for paginated results
/// </summary>
public class PaginatedResponse<T>
{
    /// <summary>List of items</summary>
    public List<T> Items { get; set; } = new();
    
    /// <summary>Total number of items</summary>
    public int TotalCount { get; set; }
    
    /// <summary>Current page number (1-based)</summary>
    public int CurrentPage { get; set; }
    
    /// <summary>Number of items per page</summary>
    public int PageSize { get; set; }
    
    /// <summary>Total number of pages</summary>
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    
    /// <summary>Whether there are more pages</summary>
    public bool HasNextPage => CurrentPage < TotalPages;
    
    /// <summary>Whether there are previous pages</summary>
    public bool HasPreviousPage => CurrentPage > 1;
}

/// <summary>
/// Admin response model for user security overview
/// </summary>
public class UserSecurityOverviewResponse
{
    /// <summary>User ID</summary>
    public string UserId { get; set; } = string.Empty;
    
    /// <summary>User email</summary>
    public string Email { get; set; } = string.Empty;
    
    /// <summary>Current number of active sessions</summary>
    public int ActiveSessionCount { get; set; }
    
    /// <summary>Last login timestamp</summary>
    public DateTime? LastLoginAt { get; set; }
    
    /// <summary>Last activity timestamp</summary>
    public DateTime? LastActivityAt { get; set; }
    
    /// <summary>Number of failed login attempts in last 7 days</summary>
    public int RecentFailedLogins { get; set; }
    
    /// <summary>Highest risk score in last 7 days</summary>
    public int HighestRiskScore { get; set; }
    
    /// <summary>Account status</summary>
    public string AccountStatus { get; set; } = "Active";
    
    /// <summary>Whether account is locked</summary>
    public bool IsLocked { get; set; }
    
    /// <summary>Whether 2FA is enabled</summary>
    public bool TwoFactorEnabled { get; set; }
}
