---
mode: agent
tools: ['codebase', 'editFiles', 'githubRepo', 'runCommands', 'grep_search', 'semantic_search']
description: "Comprehensive security review for ASP.NET Core Identity API"
---

# Security Review Prompt

Perform a comprehensive security review of the Blenda Core Identity & Access Management System focusing on:

## Critical Security Areas

### 1. Connection String Security
- Verify all Oracle connection strings use environment variables
- Check for hardcoded credentials in source code
- Validate secure configuration loading patterns

### 2. Authentication & Authorization
- Review JWT token implementation and validation
- Check multi-factor authentication implementation
- Validate role-based access control (RBAC) enforcement
- Verify secure session management

### 3. Input Validation & Injection Prevention
- Check for SQL injection vulnerabilities using parameterized queries
- Validate email template injection prevention
- Review API input validation and sanitization
- Check for XSS prevention in all user inputs

### 4. Error Handling & Information Disclosure
- Verify error messages don't expose sensitive information
- Check exception handling prevents stack trace exposure
- Validate secure logging without credential exposure

### 5. Data Protection & Encryption
- Review password hashing and storage
- Check sensitive data encryption at rest and in transit
- Validate secure token generation and validation

## Security Framework Compliance

Check compliance with:
- OWASP Top 10 security standards
- ASP.NET Core security best practices
- Enterprise identity management standards
- Oracle database security guidelines

## Output Format

Provide findings in the following format:
1. **High Risk Issues**: Critical security vulnerabilities requiring immediate attention
2. **Medium Risk Issues**: Security improvements needed but not critical
3. **Best Practices**: Recommendations for enhanced security posture
4. **Compliance Status**: Assessment against security frameworks
5. **Remediation Steps**: Specific actions to address identified issues

Focus on practical, actionable recommendations that align with the existing ASP.NET Core Identity architecture.
