---
mode: agent
tools: ['codebase', 'semantic_search', 'grep_search', 'read_file']
description: "Validate enterprise compliance requirements for identity management systems"
---

# Compliance Check Prompt

Perform comprehensive compliance validation for the Blenda Core Identity & Access Management System against enterprise security standards and regulatory requirements.

## Compliance Frameworks to Validate

### 1. GDPR (General Data Protection Regulation)
- **Data Processing**: Verify lawful basis for personal data processing
- **Consent Management**: Check user consent collection and withdrawal mechanisms
- **Data Subject Rights**: Validate right to access, rectification, erasure, and portability
- **Privacy by Design**: Assess data protection measures built into the system
- **Data Breach Notification**: Review incident response and notification procedures
- **Data Retention**: Verify retention policies and automatic deletion

### 2. SOC 2 Type II Controls
- **Security**: Access controls, network security, and system configuration
- **Availability**: System monitoring, incident response, and backup procedures
- **Processing Integrity**: System processing controls and error handling
- **Confidentiality**: Data classification and protection measures
- **Privacy**: Privacy notice requirements and data collection practices

### 3. ISO 27001 Information Security
- **Access Control**: Authentication and authorization mechanisms
- **Cryptography**: Encryption standards and key management
- **System Security**: Secure development and maintenance practices
- **Incident Management**: Security incident handling procedures
- **Business Continuity**: Disaster recovery and continuity planning

### 4. NIST Cybersecurity Framework
- **Identify**: Asset management and risk assessment
- **Protect**: Access control and data security measures
- **Detect**: Security monitoring and anomaly detection
- **Respond**: Incident response procedures and communications
- **Recover**: Recovery planning and system restoration

## Technical Compliance Areas

### Identity Management Standards
- **SAML 2.0**: Security assertion markup language compliance
- **OAuth 2.0 / OpenID Connect**: Authorization and authentication standards
- **FIDO2/WebAuthn**: Strong authentication standards
- **LDAP/Active Directory**: Directory service integration

### Security Implementation Standards
- **Password Policy**: NIST 800-63B compliance for authentication
- **Multi-Factor Authentication**: FIDO Alliance standards
- **Session Management**: OWASP session management guidelines
- **API Security**: OWASP API Security Top 10 compliance

### Database Security Standards
- **Encryption**: Data at rest and in transit encryption
- **Access Controls**: Database-level security controls
- **Audit Logging**: Comprehensive audit trail requirements
- **Backup Security**: Secure backup and recovery procedures

## Compliance Assessment Areas

### 1. Data Protection Assessment
- Personal data inventory and classification
- Data flow mapping and protection measures
- Consent management and user rights implementation
- Cross-border data transfer compliance

### 2. Access Control Assessment
- Role-based access control (RBAC) implementation
- Privileged access management (PAM)
- Multi-factor authentication enforcement
- Session management and timeout controls

### 3. Audit and Monitoring Assessment
- Security event logging and monitoring
- Compliance reporting capabilities
- Incident response procedures
- Regular security assessments

### 4. Infrastructure Security Assessment
- Network security controls
- System hardening compliance
- Vulnerability management
- Patch management procedures

## Output Format

Provide compliance assessment in the following structure:

### Compliance Summary
- Overall compliance score by framework
- Critical non-compliance issues
- Medium and low priority gaps

### Framework-Specific Findings
- Detailed assessment by compliance framework
- Specific control gaps and recommendations
- Evidence of compliance where applicable

### Risk Assessment
- High-risk compliance gaps requiring immediate attention
- Medium-risk areas needing improvement
- Low-risk recommendations for enhanced compliance

### Remediation Plan
- Prioritized action items with timelines
- Resource requirements for compliance
- Implementation roadmap and milestones

Focus on practical, actionable compliance recommendations that align with the existing ASP.NET Core Identity architecture and enterprise security requirements.
