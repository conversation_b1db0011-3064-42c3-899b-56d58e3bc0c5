namespace IdentityManager.Models;

/// <summary>
/// Tenant information model
/// </summary>
public class TenantInfo
{
    /// <summary>
    /// Unique tenant identifier
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Tenant display name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Tenant description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether the tenant is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Tenant creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Additional tenant metadata
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}