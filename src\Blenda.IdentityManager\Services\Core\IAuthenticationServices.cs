using Microsoft.AspNetCore.Identity;
using IdentityManager.Models;
using System.Security.Claims;
using BearerTokenResponse = Microsoft.AspNetCore.Authentication.BearerToken.AccessTokenResponse;

namespace IdentityManager.Services.Core;

/// <summary>
/// Service for JWT token generation, validation, and management
/// </summary>
public interface ITokenService
{
    /// <summary>
    /// Generates access and refresh tokens for a user
    /// </summary>
    /// Generates access and refresh tokens for a user
    /// </summary>
    /// <param name="user">The user to generate tokens for</param>
    /// <returns>Token response containing access token, refresh token, and expiration info</returns>
    Task<BearerTokenResponse> GenerateTokenResponseAsync(ApplicationUser user);

    /// <summary>
    /// Validates and refreshes an access token using a refresh token
    /// </summary>
    /// <param name="refreshToken">The refresh token to validate</param>
    /// <returns>The user associated with the token, or null if invalid</returns>
    Task<ApplicationUser?> ValidateRefreshTokenAsync(string refreshToken);

    /// <summary>
    /// Generates a secure session identifier
    /// </summary>
    /// <returns>A cryptographically secure session ID</returns>
    string GenerateSecureSessionId();

    /// <summary>
    /// Sets refresh token as HTTP-only cookie with appropriate security settings
    /// </summary>
    /// <param name="refreshToken">The refresh token to set as cookie</param>
    void SetRefreshTokenCookie(string refreshToken);
}

/// <summary>
/// Service for security auditing and logging authentication events
/// </summary>
public interface ISecurityAuditService
{
    /// <summary>
    /// Logs authentication-related security events with comprehensive context
    /// </summary>
    /// <param name="userId">The user ID (null for failed login attempts)</param>
    /// <param name="email">The email address</param>
    /// <param name="eventType">Type of security event (Login, TokenRefresh, etc.)</param>
    /// <param name="success">Whether the operation was successful</param>
    /// <param name="failureReason">Reason for failure if unsuccessful</param>
    /// <param name="riskScore">Risk assessment score for the event</param>
    Task LogSecurityEventAsync(string? userId, string email, string eventType, bool success, 
        string? failureReason = null, int? riskScore = null);

    /// <summary>
    /// Calculates risk score for authentication events based on various factors
    /// </summary>
    /// <param name="eventType">Type of security event</param>
    /// <param name="success">Whether the operation was successful</param>
    /// <param name="userAgent">User agent string</param>
    /// <param name="ipAddress">Client IP address</param>
    /// <returns>Risk score from 1-100</returns>
    int CalculateRiskScore(string eventType, bool success, string? userAgent, string? ipAddress);
}

/// <summary>
/// Service for user session management and tracking
/// </summary>
public interface ISessionService
{
    /// <summary>
    /// Creates a new user session record
    /// </summary>
    /// <param name="user">The user to create session for</param>
    /// <param name="sessionId">Unique session identifier</param>
    /// <returns>The created user session</returns>
    Task<UserSession> CreateUserSessionAsync(ApplicationUser user, string sessionId);

    /// <summary>
    /// Terminates a user session and updates user statistics
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="sessionId">The session to terminate</param>
    /// <param name="reason">Reason for termination</param>
    Task TerminateUserSessionAsync(string userId, string sessionId, string reason);

    /// <summary>
    /// Gets the current HTTP context
    /// </summary>
    /// <returns>Current HTTP context or null</returns>
    HttpContext? GetCurrentHttpContext();

    /// <summary>
    /// Extracts client IP address from HTTP context
    /// </summary>
    /// <param name="httpContext">The HTTP context</param>
    /// <returns>Client IP address</returns>
    string? GetClientIpAddress(HttpContext? httpContext);

    /// <summary>
    /// Creates a secure hash of an IP address for privacy protection
    /// </summary>
    /// <param name="ipAddress">The IP address to hash</param>
    /// <returns>Hashed IP address</returns>
    string HashIpAddress(string? ipAddress);

    /// <summary>
    /// Extracts device information from user agent string
    /// </summary>
    /// <param name="userAgent">User agent string</param>
    /// <returns>Device information</returns>
    string GetDeviceInfo(string? userAgent);

    /// <summary>
    /// Returns true if the provided session for the user is still active (not terminated and not expired)
    /// </summary>
    /// <param name="userId">User id to check</param>
    /// <param name="sessionId">Session id from the access token</param>
    /// <returns>True when the session exists and is active</returns>
    Task<bool> IsSessionActiveAsync(string userId, string sessionId);
}

/// <summary>
/// Enhanced user registration service with comprehensive validation and features
/// </summary>
 
public interface IUserRegistrationService<TUser> where TUser : class
{
    /// <summary>
    /// Registers a new user with the specified role and comprehensive validation
    /// </summary>
    /// <param name="registration">Registration request data</param>
    /// <param name="role">Role to assign to the user</param>
    /// <param name="emailValidator">Optional custom email validation function</param>
    /// <param name="requireEmailConfirmation">Whether email confirmation is required</param>
    /// <returns>Registration result</returns>
    Task<RegistrationResult> RegisterUserAsync(
        Microsoft.AspNetCore.Identity.Data.RegisterRequest registration, 
        string role, 
        Func<string, bool>? emailValidator = null,
        bool requireEmailConfirmation = true);

    /// <summary>
    /// Sends confirmation email to a user
    /// </summary>
    /// <param name="user">The user to send email to</param>
    /// <returns>Task representing the async operation</returns>
    Task SendConfirmationEmailAsync(TUser user);

    /// <summary>
    /// Validates user registration data against business rules
    /// </summary>
    /// <param name="registration">Registration data to validate</param>
    /// <returns>Validation result with any errors</returns>
    Task<UserValidationResult> ValidateRegistrationAsync(Microsoft.AspNetCore.Identity.Data.RegisterRequest registration);
}

/// <summary>
/// Result of user registration operation
/// </summary>
public class RegistrationResult
{
    public bool Success { get; set; }
    public string? UserId { get; set; }
    public IList<IdentityError> Errors { get; set; } = new List<IdentityError>();
    public bool EmailConfirmationSent { get; set; }
}

/// <summary>
/// Result of validation operation
/// </summary>
public class UserValidationResult
{
    public bool IsValid { get; set; }
    public Dictionary<string, string> Errors { get; set; } = new();
}