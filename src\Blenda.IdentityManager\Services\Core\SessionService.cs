using Microsoft.AspNetCore.Identity;
using IdentityManager.Models;
using IdentityManager.Data;
using IdentityManager.Services.Core;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;

namespace IdentityManager.Services.Core;

/// <summary>
/// Service for user session management and tracking
/// </summary>
public class SessionService : ISessionService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<SessionService> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public SessionService(
        ApplicationDbContext dbContext,
        UserManager<ApplicationUser> userManager,
        ILogger<SessionService> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _dbContext = dbContext;
        _userManager = userManager;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    /// <inheritdoc />
    public async Task<UserSession> CreateUserSessionAsync(ApplicationUser user, string sessionId)
    {
        try
        {
            var httpContext = GetCurrentHttpContext();
            var ipAddress = GetClientIpAddress(httpContext);
            var userAgent = httpContext?.Request.Headers.UserAgent.ToString();
            var userId = await _userManager.GetUserIdAsync(user);
            var tenantId = await GetUserTenantIdAsync(userId);

            var session = new UserSession
            {
                SessionId = sessionId,
                UserId = userId,
                TenantId = tenantId,
                CreatedAt = DateTime.UtcNow,
                LastActivityAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddDays(30), // Default 30 days
                IsActive = true,
                DeviceInfo = GetDeviceInfo(userAgent),
                IpAddressHash = HashIpAddress(ipAddress)
            };

            _dbContext.UserSessions.Add(session);

            // Update user's active session count and last activity
            user.LastActivityAt = DateTime.UtcNow;
            user.LastIpAddressHash = HashIpAddress(ipAddress);
            user.ActiveSessionCount = await _dbContext.UserSessions
                .CountAsync(s => s.UserId == userId && s.IsActive && s.ExpiresAt > DateTime.UtcNow);

            await _dbContext.SaveChangesAsync();

            _logger.LogDebug("Created session {SessionId} for user {UserId}", sessionId, userId);
            return session;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create user session for user {UserId}", await _userManager.GetUserIdAsync(user));
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<bool> IsSessionActiveAsync(string userId, string sessionId)
    {
        if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(sessionId))
            return false;

        try
        {
            var session = await _dbContext.UserSessions
                .AsNoTracking()
                .FirstOrDefaultAsync(s => s.UserId == userId && s.SessionId == sessionId);

            if (session == null) return false;

            // Session must be marked active and not expired
            var isActive = session.IsActive && session.ExpiresAt > DateTime.UtcNow;
            return isActive;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check session active state for user {UserId} session {SessionId}", userId, sessionId);
            // For safety, when an unexpected error occurs, treat session as inactive
            return false;
        }
    }

    /// <inheritdoc />
    public async Task TerminateUserSessionAsync(string userId, string sessionId, string reason)
    {
        try
        {
            var session = await _dbContext.UserSessions
                .FirstOrDefaultAsync(s => s.UserId == userId && s.SessionId == sessionId && s.IsActive);

            if (session != null)
            {
                session.IsActive = false;
                session.TerminatedAt = DateTime.UtcNow;
                session.TerminationReason = reason;

                // Update user's active session count
                var user = await _userManager.FindByIdAsync(userId);
                if (user != null)
                {
                    user.ActiveSessionCount = await _dbContext.UserSessions
                        .CountAsync(s => s.UserId == userId && s.IsActive && s.ExpiresAt > DateTime.UtcNow);
                }

                await _dbContext.SaveChangesAsync();
                _logger.LogDebug("Terminated session {SessionId} for user {UserId}. Reason: {Reason}", 
                    sessionId, userId, reason);
            }
            else
            {
                _logger.LogDebug("Session {SessionId} not found or already terminated for user {UserId}", 
                    sessionId, userId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to terminate session {SessionId} for user {UserId}", sessionId, userId);
            throw;
        }
    }

    /// <inheritdoc />
    public HttpContext? GetCurrentHttpContext()
    {
        return _httpContextAccessor.HttpContext;
    }

    /// <inheritdoc />
    public string? GetClientIpAddress(HttpContext? httpContext)
    {
        if (httpContext == null)
            return null;

        // Check for IP in X-Forwarded-For header (load balancer scenarios)
        var forwardedFor = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        // Check X-Real-IP header
        var realIp = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        // Fall back to remote IP address
        return httpContext.Connection.RemoteIpAddress?.ToString();
    }

    /// <inheritdoc />
    public string HashIpAddress(string? ipAddress)
    {
        if (string.IsNullOrEmpty(ipAddress))
            return string.Empty;

        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(ipAddress));
        return Convert.ToBase64String(hashedBytes);
    }

    /// <inheritdoc />
    public string GetDeviceInfo(string? userAgent)
    {
        if (string.IsNullOrEmpty(userAgent))
            return "Unknown Device";

        try
        {
            // Simple device detection - in production, consider using a proper user agent parser
            if (userAgent.Contains("Mobile", StringComparison.OrdinalIgnoreCase))
            {
                if (userAgent.Contains("iPhone", StringComparison.OrdinalIgnoreCase))
                    return "iPhone";
                if (userAgent.Contains("Android", StringComparison.OrdinalIgnoreCase))
                    return "Android Phone";
                return "Mobile Device";
            }

            if (userAgent.Contains("Tablet", StringComparison.OrdinalIgnoreCase) || 
                userAgent.Contains("iPad", StringComparison.OrdinalIgnoreCase))
            {
                return "Tablet";
            }

            // Desktop browsers
            if (userAgent.Contains("Chrome", StringComparison.OrdinalIgnoreCase))
                return "Desktop - Chrome";
            if (userAgent.Contains("Firefox", StringComparison.OrdinalIgnoreCase))
                return "Desktop - Firefox";
            if (userAgent.Contains("Safari", StringComparison.OrdinalIgnoreCase))
                return "Desktop - Safari";
            if (userAgent.Contains("Edge", StringComparison.OrdinalIgnoreCase))
                return "Desktop - Edge";

            return "Desktop - Unknown Browser";
        }
        catch
        {
            return "Unknown Device";
        }
    }

    /// <inheritdoc />
    public async Task<Guid?> GetUserTenantIdAsync(string? userId)
    {
        if (string.IsNullOrEmpty(userId)) 
            return null;

        try
        {
            var user = await _dbContext.Users.FindAsync(userId);
            return (user as dynamic)?.TenantId;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Cleans up expired sessions for all users
    /// </summary>
    public async Task CleanupExpiredSessionsAsync()
    {
        try
        {
            var cutoffTime = DateTime.UtcNow;
            var expiredSessions = await _dbContext.UserSessions
                .Where(s => s.IsActive && s.ExpiresAt <= cutoffTime)
                .ToListAsync();

            foreach (var session in expiredSessions)
            {
                session.IsActive = false;
                session.TerminatedAt = DateTime.UtcNow;
                session.TerminationReason = "Expired";
            }

            // Update active session counts for affected users
            var affectedUserIds = expiredSessions.Select(s => s.UserId).Distinct();
            foreach (var userId in affectedUserIds)
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user != null)
                {
                    user.ActiveSessionCount = await _dbContext.UserSessions
                        .CountAsync(s => s.UserId == userId && s.IsActive && s.ExpiresAt > DateTime.UtcNow);
                }
            }

            await _dbContext.SaveChangesAsync();
            
            if (expiredSessions.Any())
            {
                _logger.LogInformation("Cleaned up {Count} expired sessions", expiredSessions.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cleanup expired sessions");
        }
    }

    /// <summary>
    /// Gets active sessions for a user
    /// </summary>
    public async Task<List<UserSession>> GetActiveUserSessionsAsync(string userId)
    {
        return await _dbContext.UserSessions
            .Where(s => s.UserId == userId && s.IsActive && s.ExpiresAt > DateTime.UtcNow)
            .OrderByDescending(s => s.LastActivityAt)
            .ToListAsync();
    }

    /// <summary>
    /// Terminates all sessions for a user except the current one
    /// </summary>
    public async Task TerminateOtherUserSessionsAsync(string userId, string currentSessionId, string reason)
    {
        try
        {
            var otherSessions = await _dbContext.UserSessions
                .Where(s => s.UserId == userId && s.SessionId != currentSessionId && s.IsActive)
                .ToListAsync();

            foreach (var session in otherSessions)
            {
                session.IsActive = false;
                session.TerminatedAt = DateTime.UtcNow;
                session.TerminationReason = reason;
            }

            // Update user's active session count
            var user = await _userManager.FindByIdAsync(userId);
            if (user != null)
            {
                user.ActiveSessionCount = await _dbContext.UserSessions
                    .CountAsync(s => s.UserId == userId && s.IsActive && s.ExpiresAt > DateTime.UtcNow);
            }

            await _dbContext.SaveChangesAsync();
            
            _logger.LogInformation("Terminated {Count} other sessions for user {UserId}", 
                otherSessions.Count, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to terminate other sessions for user {UserId}", userId);
            throw;
        }
    }
}