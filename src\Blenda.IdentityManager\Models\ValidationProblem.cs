using Microsoft.AspNetCore.Http.HttpResults;

namespace IdentityManager.Models;

/// <summary>
/// Represents a validation problem response
/// </summary>
public class ValidationProblem : IResult
{
    private readonly ProblemHttpResult _problemResult;

    public ValidationProblem(ProblemHttpResult problemResult)
    {
        _problemResult = problemResult;
    }

    public Task ExecuteAsync(HttpContext httpContext)
    {
        return _problemResult.ExecuteAsync(httpContext);
    }
}
