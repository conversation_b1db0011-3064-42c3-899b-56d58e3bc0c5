using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Identity.Data;

namespace IdentityManager.Models;

/// <summary>
/// Extended user profile information response with Two-Factor Authentication status
/// Enhances the standard InfoResponse with additional security information for better user experience
/// Now includes GDPR compliance, session, and activity information in a single response
/// </summary>
public class ExtendedInfoResponse
{
    // =============================================================================
    // BASIC PROFILE INFORMATION
    // =============================================================================
    
    /// <summary>
    /// User's email address
    /// </summary>
    [Required]
    [EmailAddress]
    public required string Email { get; set; }

    /// <summary>
    /// Indicates whether the user's email has been confirmed
    /// </summary>
    public bool IsEmailConfirmed { get; set; }

    /// <summary>
    /// User's account creation date
    /// </summary>
    public DateTime CreatedAt { get; set; }

    // =============================================================================
    // SESSION AND ACTIVITY INFORMATION - "Último Acceso"
    // =============================================================================

    /// <summary>
    /// Last login timestamp - "Último Acceso"
    /// </summary>
    public DateTime? LastLoginAt { get; set; }

    /// <summary>
    /// Current session start time (when user logged in this session)
    /// </summary>
    public DateTime? CurrentSessionStart { get; set; }

    /// <summary>
    /// Country/location of last access (for security awareness)
    /// </summary>
    public string? LastAccessCountry { get; set; }

    /// <summary>
    /// Account status for user awareness
    /// </summary>
    public string AccountStatus { get; set; } = "Active";

    // =============================================================================
    // SECURITY STATUS (2FA)
    // =============================================================================

    /// <summary>
    /// Indicates whether Two-Factor Authentication is enabled for the user
    /// Provides enhanced security visibility for user experience
    /// </summary>
    public bool TwoFactorEnabled { get; set; }

    /// <summary>
    /// Number of recovery codes available (only when 2FA is enabled)
    /// Helps users understand their backup authentication options
    /// </summary>
    public int? RecoveryCodesCount { get; set; }

    /// <summary>
    /// Indicates whether the user has set up an authenticator app
    /// Provides visibility into 2FA setup completion status
    /// </summary>
    public bool HasAuthenticatorKey { get; set; }

    // =============================================================================
    // GDPR COMPLIANCE INFORMATION
    // =============================================================================

    /// <summary>
    /// GDPR: Terms of Service acceptance status and date
    /// </summary>
    public ConsentInfo TermsOfServiceConsent { get; set; } = new();

    /// <summary>
    /// GDPR: Privacy Policy acceptance status and date
    /// </summary>
    public ConsentInfo PrivacyPolicyConsent { get; set; } = new();

    /// <summary>
    /// GDPR: Marketing communications consent status
    /// </summary>
    public ConsentInfo? MarketingConsent { get; set; }

    /// <summary>
    /// GDPR: Analytics consent status
    /// </summary>
    public ConsentInfo? AnalyticsConsent { get; set; }

    /// <summary>
    /// GDPR: Data retention preference (days)
    /// </summary>
    public int DataRetentionDays { get; set; } = 2555;

    /// <summary>
    /// GDPR: User's country for jurisdiction awareness
    /// </summary>
    public string? Country { get; set; }

    /// <summary>
    /// GDPR: Whether user is EU citizen (affects rights)
    /// </summary>
    public bool IsEUCitizen { get; set; }

    /// <summary>
    /// GDPR: Last data export request date
    /// </summary>
    public DateTime? LastDataExportRequestAt { get; set; }

    /// <summary>
    /// GDPR: Deletion request status (if any)
    /// </summary>
    public DeletionRequestInfo? DeletionRequest { get; set; }

    /// <summary>
    /// Summary of recent consent activities for user transparency
    /// </summary>
    public List<RecentConsentActivity> RecentConsentActivities { get; set; } = new();
}

/// <summary>
/// Information about a specific consent (GDPR transparency)
/// </summary>
public class ConsentInfo
{
    /// <summary>
    /// Whether consent is currently granted
    /// </summary>
    public bool IsGranted { get; set; }

    /// <summary>
    /// When consent was granted
    /// </summary>
    public DateTime? GrantedAt { get; set; }

    /// <summary>
    /// Policy version that was accepted
    /// </summary>
    public string? PolicyVersion { get; set; }

    /// <summary>
    /// Source of consent (e.g., "registration", "profile_update")
    /// </summary>
    public string? ConsentSource { get; set; }

    /// <summary>
    /// Expiration date (if applicable)
    /// </summary>
    public DateTime? ExpiresAt { get; set; }
}

/// <summary>
/// Information about data deletion request (GDPR Article 17)
/// </summary>
public class DeletionRequestInfo
{
    /// <summary>
    /// Whether account is marked for deletion
    /// </summary>
    public bool IsMarkedForDeletion { get; set; }

    /// <summary>
    /// When deletion was requested
    /// </summary>
    public DateTime? RequestedAt { get; set; }

    /// <summary>
    /// Scheduled deletion date
    /// </summary>
    public DateTime? ScheduledDeletionDate { get; set; }

    /// <summary>
    /// Days remaining until deletion
    /// </summary>
    public int? DaysUntilDeletion { get; set; }
}

/// <summary>
/// Recent consent activity for user transparency
/// </summary>
public class RecentConsentActivity
{
    /// <summary>
    /// Type of consent
    /// </summary>
    public string ConsentType { get; set; } = string.Empty;

    /// <summary>
    /// Action taken (Granted, Withdrawn, Updated)
    /// </summary>
    public string Action { get; set; } = string.Empty;

    /// <summary>
    /// When the action occurred
    /// </summary>
    public DateTime ActionDate { get; set; }

    /// <summary>
    /// Source of the action
    /// </summary>
    public string? Source { get; set; }
}

/// <summary>
/// Request model for updating user profile information
/// Extends the standard InfoRequest functionality
/// </summary>
public class ExtendedInfoRequest
{
    /// <summary>
    /// New email address (optional)
    /// </summary>
    [EmailAddress]
    public string? NewEmail { get; set; }

    /// <summary>
    /// New password (optional)
    /// </summary>
    [DataType(DataType.Password)]
    public string? NewPassword { get; set; }

    /// <summary>
    /// Current password (required when changing password)
    /// </summary>
    [DataType(DataType.Password)]
    public string? OldPassword { get; set; }
}
