namespace IdentityManager.Services.Interfaces;

/// <summary>
/// Interface for email sending operations
/// </summary>
/// <typeparam name="TUser">The type of user entity</typeparam>
public interface IEmailService<TUser> where TUser : class
{
    /// <summary>
    /// Sends an email to the specified user
    /// </summary>
    /// <param name="user">The user to send email to</param>
    /// <param name="subject">Email subject</param>
    /// <param name="htmlBody">HTML email body</param>
    /// <returns>Task representing the send operation</returns>
    Task SendEmailAsync(TUser user, string subject, string htmlBody);

    /// <summary>
    /// Sends a confirmation email to the user
    /// </summary>
    /// <param name="user">The user to send confirmation email to</param>
    /// <param name="confirmationUrl">The confirmation URL</param>
    /// <returns>Task representing the send operation</returns>
    Task SendConfirmationEmailAsync(TUser user, string confirmationUrl);

    /// <summary>
    /// Sends a password reset email to the user
    /// </summary>
    /// <param name="user">The user to send reset email to</param>
    /// <param name="resetUrl">The password reset URL</param>
    /// <returns>Task representing the send operation</returns>
    Task SendPasswordResetEmailAsync(TUser user, string resetUrl);

    /// <summary>
    /// Sends a password reset code email to the user
    /// </summary>
    /// <param name="user">The user to send reset code to</param>
    /// <param name="resetCode">The password reset code</param>
    /// <returns>Task representing the send operation</returns>
    Task SendPasswordResetCodeAsync(TUser user, string resetCode);
}
