using Microsoft.AspNetCore.Identity;
using IdentityManager.Models;
using Microsoft.AspNetCore.Http.HttpResults;
using System.Security.Claims;
using Microsoft.AspNetCore.Identity.Data;
using IdentityManager.Services.Validation;
using Blenda.Shared.Messaging.Services;
using Blenda.Shared.Messaging.Constants;
using Blenda.Shared.Messaging.Models;

using ValidationProblem = Microsoft.AspNetCore.Http.HttpResults.ValidationProblem;

namespace IdentityManager.Services.Handlers;

/// <summary>
/// Interface for Two-Factor Authentication management operations
/// </summary>
public interface ITwoFactorManagementHandler<TUser> where TUser : class
{
    /// <summary>
    /// Gets the current 2FA status for a user
    /// </summary>
    Task<Results<Ok<TwoFactorStatusResponse>, UnauthorizedHttpResult, ProblemHttpResult>> GetTwoFactorStatusAsync(ClaimsPrincipal userPrincipal);

    /// <summary>
    /// Sets up 2FA for a user
    /// </summary>
    Task<Results<Ok<TwoFactorSetupResponse>, UnauthorizedHttpResult, ProblemHttpResult>> SetupTwoFactorAsync(ClaimsPrincipal userPrincipal);

    /// <summary>
    /// Verifies and enables 2FA for a user
    /// </summary>
    Task<Results<Ok<TwoFactorVerifyResponse>, UnauthorizedHttpResult, ValidationProblem>> VerifyTwoFactorAsync(ClaimsPrincipal userPrincipal, TwoFactorVerifyRequest request);

    /// <summary>
    /// Disables 2FA for a user
    /// </summary>
    Task<Results<Ok, UnauthorizedHttpResult, ValidationProblem>> DisableTwoFactorAsync(ClaimsPrincipal userPrincipal, TwoFactorDisableRequest request);

    /// <summary>
    /// Regenerates recovery codes for a user
    /// </summary>
    Task<Results<Ok<string[]>, UnauthorizedHttpResult, ValidationProblem>> RegenerateRecoveryCodesAsync(ClaimsPrincipal userPrincipal, TwoFactorRegenerateRecoveryCodesRequest request);
}

/// <summary>
/// Handles Two-Factor Authentication management operations
/// </summary>
public class TwoFactorManagementHandler<TUser> : ITwoFactorManagementHandler<TUser> where TUser : class
{
    private readonly UserManager<TUser> _userManager;
    private readonly TwoFactorService<TUser> _twoFactorService;
    private readonly ILogger<TwoFactorManagementHandler<TUser>> _logger;
    private readonly IMessageService _messageService;
    private readonly IClientCommunicationService _clientCommunicationService;

    public TwoFactorManagementHandler(
        UserManager<TUser> userManager,
        TwoFactorService<TUser> twoFactorService,
        ILogger<TwoFactorManagementHandler<TUser>> logger,
        IMessageService messageService,
        IClientCommunicationService clientCommunicationService)
    {
        _userManager = userManager;
        _twoFactorService = twoFactorService;
        _logger = logger;
        _messageService = messageService;
        _clientCommunicationService = clientCommunicationService;
    }

    /// <inheritdoc />
    public async Task<Results<Ok<TwoFactorStatusResponse>, UnauthorizedHttpResult, ProblemHttpResult>> GetTwoFactorStatusAsync(ClaimsPrincipal userPrincipal)
    {
        try
        {
            var user = await _userManager.GetUserAsync(userPrincipal);
            if (user == null)
            {
                _logger.LogWarning("2FA status request for unauthorized user");
                return TypedResults.Unauthorized();
            }

            var statusResponse = await _twoFactorService.GetTwoFactorStatusAsync(user);
            
            _logger.LogInformation("2FA status retrieved for user {UserId}", await _userManager.GetUserIdAsync(user));
            return TypedResults.Ok(statusResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting 2FA status");
            return TypedResults.Problem(
                _messageService.GetMessage(MessageKeys.System.InternalError), 
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <inheritdoc />
    public async Task<Results<Ok<TwoFactorSetupResponse>, UnauthorizedHttpResult, ProblemHttpResult>> SetupTwoFactorAsync(ClaimsPrincipal userPrincipal)
    {
        try
        {
            var user = await _userManager.GetUserAsync(userPrincipal);
            if (user == null)
            {
                _logger.LogWarning("2FA setup request for unauthorized user");
                return TypedResults.Unauthorized();
            }

            var setupResponse = await _twoFactorService.SetupTwoFactorAsync(user);
            
            _logger.LogInformation("2FA setup initiated for user {UserId}", await _userManager.GetUserIdAsync(user));
            return TypedResults.Ok(setupResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting up 2FA");
            return TypedResults.Problem(
                _messageService.GetMessage(MessageKeys.System.InternalError), 
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <inheritdoc />
    public async Task<Results<Ok<TwoFactorVerifyResponse>, UnauthorizedHttpResult, ValidationProblem>> VerifyTwoFactorAsync(
        ClaimsPrincipal userPrincipal, 
        TwoFactorVerifyRequest request)
    {
        try
        {
            var user = await _userManager.GetUserAsync(userPrincipal);
            if (user == null)
            {
                _logger.LogWarning("2FA verification request for unauthorized user");
                return TypedResults.Unauthorized();
            }

            var verifyResponse = await _twoFactorService.VerifyTwoFactorSetupAsync(user, request.Code);
            
            if (verifyResponse.Success)
            {
                _logger.LogInformation("2FA verification successful for user {UserId}", await _userManager.GetUserIdAsync(user));
                return TypedResults.Ok(verifyResponse);
            }
            else
            {
                _logger.LogWarning("2FA verification failed for user {UserId}", await _userManager.GetUserIdAsync(user));
                var errorResponse = (ValidationErrorResponse)_clientCommunicationService.CreateValidationErrorResponse(
                    new Dictionary<string, string[]> { ["InvalidCode"] = [MessageKeys.TwoFactor.InvalidCode] }
                );
                return TypedResults.ValidationProblem(errorResponse.ValidationErrors);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying 2FA setup");
            var errorResponse = (ValidationErrorResponse)_clientCommunicationService.CreateValidationErrorResponse(
                new Dictionary<string, string[]> { ["VerificationError"] = [MessageKeys.TwoFactor.VerificationFailed] }
            );
            return TypedResults.ValidationProblem(errorResponse.ValidationErrors);
        }
    }

    /// <inheritdoc />
    public async Task<Results<Ok, UnauthorizedHttpResult, ValidationProblem>> DisableTwoFactorAsync(
        ClaimsPrincipal userPrincipal, 
        TwoFactorDisableRequest request)
    {
        try
        {
            var user = await _userManager.GetUserAsync(userPrincipal);
            if (user == null)
            {
                _logger.LogWarning("2FA disable request for unauthorized user");
                return TypedResults.Unauthorized();
            }

            var isDisabled = await _twoFactorService.DisableTwoFactorAsync(user, request.Password);
            
            if (isDisabled)
            {
                _logger.LogInformation("2FA disabled successfully for user {UserId}", await _userManager.GetUserIdAsync(user));
                return TypedResults.Ok();
            }
            else
            {
                _logger.LogWarning("2FA disable failed for user {UserId}: Invalid password", await _userManager.GetUserIdAsync(user));
                var errorResponse = (ValidationErrorResponse)_clientCommunicationService.CreateValidationErrorResponse(
                    new Dictionary<string, string[]> { ["InvalidPassword"] = [MessageKeys.TwoFactor.InvalidPassword] }
                );
                return TypedResults.ValidationProblem(errorResponse.ValidationErrors);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disabling 2FA");
            var errorResponse = (ValidationErrorResponse)_clientCommunicationService.CreateValidationErrorResponse(
                new Dictionary<string, string[]> { ["DisableError"] = [MessageKeys.TwoFactor.DisableFailed] }
            );
            return TypedResults.ValidationProblem(errorResponse.ValidationErrors);
        }
    }

    /// <inheritdoc />
    public async Task<Results<Ok<string[]>, UnauthorizedHttpResult, ValidationProblem>> RegenerateRecoveryCodesAsync(
        ClaimsPrincipal userPrincipal, 
        TwoFactorRegenerateRecoveryCodesRequest request)
    {
        try
        {
            var user = await _userManager.GetUserAsync(userPrincipal);
            if (user == null)
            {
                _logger.LogWarning("Recovery codes regeneration request for unauthorized user");
                return TypedResults.Unauthorized();
            }

            // Check if 2FA is enabled before attempting to regenerate recovery codes
            var isTwoFactorEnabled = await _userManager.GetTwoFactorEnabledAsync(user);
            if (!isTwoFactorEnabled)
            {
                _logger.LogWarning("Recovery codes regeneration request for user {UserId} who does not have 2FA enabled", 
                    await _userManager.GetUserIdAsync(user));
                var notEnabledError = (ValidationErrorResponse)_clientCommunicationService.CreateValidationErrorResponse(
                    new Dictionary<string, string[]> { ["TwoFactorNotEnabled"] = [MessageKeys.TwoFactor.NotEnabled] }
                );
                return TypedResults.ValidationProblem(notEnabledError.ValidationErrors);
            }

            var recoveryCodes = await _twoFactorService.RegenerateRecoveryCodesAsync(user, request.Password);
            
            if (recoveryCodes != null && recoveryCodes.Length > 0)
            {
                _logger.LogInformation("Recovery codes regenerated for user {UserId}", await _userManager.GetUserIdAsync(user));
                return TypedResults.Ok(recoveryCodes);
            }
            else
            {
                _logger.LogWarning("Recovery codes regeneration failed for user {UserId}: Invalid password", await _userManager.GetUserIdAsync(user));
                var invalidPasswordError = (ValidationErrorResponse)_clientCommunicationService.CreateValidationErrorResponse(
                    new Dictionary<string, string[]> { ["InvalidPassword"] = [MessageKeys.TwoFactor.InvalidPassword] }
                );
                return TypedResults.ValidationProblem(invalidPasswordError.ValidationErrors);
            }
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Invalid password provided for recovery codes regeneration");
            var unauthorizedError = (ValidationErrorResponse)_clientCommunicationService.CreateValidationErrorResponse(
                new Dictionary<string, string[]> { ["InvalidPassword"] = [MessageKeys.TwoFactor.InvalidPassword] }
            );
            return TypedResults.ValidationProblem(unauthorizedError.ValidationErrors);
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("Two-factor authentication is not enabled"))
        {
            _logger.LogWarning(ex, "Attempted to regenerate recovery codes when 2FA is not enabled");
            var notEnabledError = (ValidationErrorResponse)_clientCommunicationService.CreateValidationErrorResponse(
                new Dictionary<string, string[]> { ["TwoFactorNotEnabled"] = [MessageKeys.TwoFactor.NotEnabled] }
            );
            return TypedResults.ValidationProblem(notEnabledError.ValidationErrors);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error regenerating recovery codes");
            var generalError = (ValidationErrorResponse)_clientCommunicationService.CreateValidationErrorResponse(
                new Dictionary<string, string[]> { ["RegenerateError"] = [MessageKeys.System.InternalError] }
            );
            return TypedResults.ValidationProblem(generalError.ValidationErrors);
        }
    }

    /// <summary>
    /// Creates a validation problem with a localized message
    /// </summary>
    private ValidationProblem CreateValidationProblem(string field, string messageKey)
    {
        var errorResponse = (ValidationErrorResponse)_clientCommunicationService.CreateValidationErrorResponse(
            new Dictionary<string, string[]> { [field] = [messageKey] }
        );
        return TypedResults.ValidationProblem(errorResponse.ValidationErrors);
    }

    /// <summary>
    /// Creates a validation problem from a dictionary of errors
    /// </summary>
    private static ValidationProblem CreateValidationProblem(Dictionary<string, string[]> errors)
    {
        return TypedResults.ValidationProblem(errors);
    }
}
