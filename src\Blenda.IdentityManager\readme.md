

dotnet new web -n IdentityManager

cd IdentityManager

dotnet add package Microsoft.AspNetCore.Identity.EntityFrameworkCore --version 9.0.6
dotnet add package Microsoft.EntityFrameworkCore.Design --version 9.0.6
dotnet add package Oracle.EntityFrameworkCore --version 9.23.80
dotnet add package Microsoft.EntityFrameworkCore.Tools --version 9.0.6

dotnet ef migrations add InitialCreate
dotnet ef database update

dotnet add package Swashbuckle.AspNetCore --version 9.0.1
dotnet add package Swashbuckle.AspNetCore.Annotations --version 9.0.1

dotnet add package Microsoft.AspNetCore.Authentication.JwtBearer --version 9.0.6

dotnet build /property:WarningLevel=0
dotnet rundotnet run


<PackageReference Include="Blenda.Core.Shared" Version="0.6.17" />    




dotnet ef migrations add InitialIdentity --context ApplicationDbContext
dotnet ef database update --context ApplicationDbContext

dotnet ef migrations add SetIdentity --context ApplicationDbContext
dotnet ef database update --context ApplicationDbContext

dotnet ef migrations remove --context ApplicationDbContext
dotnet rm -r Migrations

dotnet ef migrations add Deploy20250806 --context ApplicationDbContext
dotnet ef database update --context ApplicationDbContext



CfDJ8OV8MeCAzulOmMb_LPLF3CfxnemrMmeaMzQU3ULqaUREzKSteEOzpm0OA3HEqeCB_Knqr-89y6VL9hfNuLxKJOkRPRXIWDaE8lzf9K1-sAI-bFD5h_whPPEPJnXzgOARLUdDCIvfTbm9-6zKg9nqa1hEhj4BO3TWMCIFbaEFVi9M4Wrd7OwDBsE5LUDYFxOcMmdLHjI2rdhMCBmRPupRnEqjY08tMKnXqZEMo5OXTy00f9f6mc4D15KCj2BAiys4-iLtQ5txu8FFIJ0SKVwCRMZuHyK0R8cHdKk0USR1Rmc3UYnsoIeSXA8pUjn0_F58L7AY18kjPf_D3_YcaUQCOQPxFU2dTdp4xhHIqtxNUlNRTIJOOTUsPPpnKmvt7w6Ee68oz1ys8TVC2lBS02sGgA1miRxU5wp1s0WI7xt36M-mpRkhXeOt6dJUlz_sLXAg4nUd9QF-ZuOo6Cu1RCUMkcyGQ5WbsZRe0-6nxO-v5fpTgaH2BSeDC3v6YhFRtWWnpdPR1xiRAMNdl_DCyRUVbVWkXtmZzT-8-K3CMamlWmmLS6AfgOzk2P-TUS5Mp2a8sq8SV3Dh2QfXXc9fgvyoSwFa2qhezg9E0sr-Pdlw9c_Q5z2aNnpxPlIu0C3_Vhp2JRB_Vd9tdYp0qWTXFg1Gsh8rSaRBr4OC8qTusxm19Ae7yTAddPp78F6mTiRGR8sM6LMs9c6rDViUjKgHD3yCeBiKD5Yd9PtdczxuhUpdfNM6


CfDJ8OV8MeCAzulOmMb_LPLF3CfnREmwNLwRGHcfCi9foqqqH5tsnv90uB1-rTid-rAhHr5FUJkL1lqdRoBBCZOn2eshyymsNrOrJ38fi6-172V9UrP96oLFcZO60HFsTw5OS33doyWu6a5uTsziV_a1myS4eu5mIa2vEyLEnuTKw22FB39MZrhcIKkT5RdiHaU2egB7c-oMRPbx3-O3iuKZj5FDR425v32S3_VvDQop6HjzsSe1UYo6wYTcs--di6ZpqSj_YpPFiwnKXaBtDdmL2sWLwlSuxDWOYaaTEp9M2DQ8QTURtF9GWnu9UA5GHp-qFZTLtc5exeer7IL2CZvFmEPI1j9z2Jrugmljy7aCV-mfNRQPoajOmfy4gXu46Fbj99717d1mM_wjWooW_7He8cywwJGtg64wnsRq8QsDEsC-Bipr3LKS0Uh8iMlYdKK5HEyjMD8o_4u2joQ-DORFMDDTzLjCfHxHs6tpXWszCofLkUYtFoJWEii8EjZnbDUy4sTiaoAH6ypiiZ8fefcadrOQ2g9Uc-aD_TH5E8A8QPn0jdXr5lG2oPIAT2w062E_TNminDv995-JV-l302aRe-C016ir44JlPipVDiTO-7j845UAfHW1MBpBBUh58mb_kB9gqbxGOAYQ9YoJbDnJCHevmy-jpirmyS9-0T28tI3RpgN1TfHud_Wi6DTQneON8pCZ3zb60G3o7Yz0E-C3OHVcIy3Bn423TF2OBb41SX-h

CfDJ8OV8MeCAzulOmMb_LPLF3CdlcdUFmqTkCspW_nKgK8X0lRMFxcZlDvshb4xMdGCXSpBMt7GKXigQ-XDGtNjsmNQBXZX-DU6W8y4FI5qZ1Ns7VHNro_i6xJpf8zsHRZqlqCLASRDvjlVPb5Ggk1ZatNl3ZL03vHGehpMKkmFozpOYyXSlJKFm4V4mYZmpn0ewQVGjx7bsFDHnuMyue0NbOlq0SVbcurNbxCOCMLjq5fBSWCB_TGDDTlF6x-KCKhOsk1Zq9h1xXAZhKL5m2MS_cYFXVGNzWK9gxQPg_Pcf0XWMMRF9r3s58_BHk7hJMEt5uPIb3sLee6UxTyJLZ-_6Ia82L3t7kKSjYZy84_vooj5W1IpmoQ9BgwQY-ykTVKVTtCWfK4DtPbKbZz-LhdM89SmZacJEZSuRWJ9nN0GBp9XDxcgDZ1rrCnAoVzr2mMQN22hYtVot3GnWLgoRJSxA9XLf2fnpp8S27mdtu1xZjxHxYBXOEV909a4h-uHJq7xCO_q-amhp9_403LrB4rhsMsmr1xJLACS0mHUtuB1rPNwH2lQJsoXcerYEUEPRa4TxQGZp8nosL-vU-50vktsKbDfF2aQv0i_D2sj4ZiP_1vsV6la_ttihx8Efo5tq8nQx2KFAHpVv0QE_4sNBGCYKLk9XBZC2b9lBE-EGghgzpKM0cAQXqb-yuAF_C5zYF_7dNleg7wSHGFCWYFRoibsqVn6xULrnnzGAfmbY_dYcCQJUdKc-BX_gTRfZqXDNWOc0ww


{
  "tokenType": "Bearer",
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.7aJY-Dn7IFV73GizxcJBrOxbagrjXEWLwinBlyGzOWI",
  "expiresIn": 1800,
  "refreshToken": "CfDJ8OV8MeCAzulOmMb/LPLF3Cd4HUPca2BRL4CPs60M5Z/uw19Q4c6szNqMLf8b7NyA5Bw/+HC77HOdjuJXkTLmqwikOk5tBVVO2suAsZi1WZcp4jL87GRC9A1Oz0sV6mlgiOoM9A8yjMZ+H03RPPpVHA6sdCqfjtFnABa1+ts9RsYEEiXoJua1l84XNRuA1rGaEfh9c/20Kem09YV4IVaTSV6PlbRRnerTR/ijLJDcVvUWwsflIITZ4td1jV3QeWv7XJZfPHvFl+sUeXbz+k8K7rlFZ3fGPPjqV578QHddgpudL4byAIc2h1MUvXTGLECgfXZ5Bp4vjbiz5JsvUw7jN7RvN+EtbgTiQijqzvul+mZbwqWzp6/I4QgRXtPvQ/bDl3lLXUvZgYSWwvLTZft1YRDpkdQpFFXqYcpCD53REBSMCgUyhRedQBhN8kbukn/ztQxnS4wogC28oQXE8ucykm+OH4ruuyFVleoTVpm9st+mVia89N5mvi4tOoGi/g7i/iN0UMhQ+jixvWZCrq45pJColrWfdXKjKPJaf/+XHzMyedqdvQF+llWGd8LinvLcoEDGMjVhclQv+y6NY/phjH8E8hRFQVsHXMBEpys5H839wal2if6O16AUDyjq8IzrpQGfwEKOSN45Fo3RCxINAv4gtLBiu2b01x5an6PGxcZmR1n8Y+TxasDd8g7Pv0SrJvblTb3MXMpeYnzLhiO7WylY+ypNi1TV7pvzr/Dyd00D3Q5FXU8XO0FFjD7TCF5M1A=="
}


{
  "tokenType": "Bearer",
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.rMf9-qI4ebIs9ATI4xkeXrIsw_Ldo9VUj8BjpIoxKm0",
  "expiresIn": 1800,
  "refreshToken": "CfDJ8OV8MeCAzulOmMb/LPLF3Cdn2wTKpvJNwB7uVt4nYMOmFVrjK/EEtIQL86CnusTEbGye+2XPLsF2pKl2plHdyUhgncKS6p6eET0sJZDlgzDJAmD9IGT/JDHZ6tk5VqjCytiFi7DmyLIXwktkJFPxLrw/1LTT6w2/uXPvTfkzFt0yXNWvfFyh6f4MXq+3tFTo8oFaRo1kb8Ru9Q8mr+6pWfp0STYwXJaYFug4D1iKl+JSrQRV7FyJqYrIm+N9GKvK2q+bGZGeTmBMZA0PhWqefQK5vXRW0hIslgmWmfg76bUK66WruO+tzvl+OapzCIbHIX/FbUFRfB7fU0/t7qbBMVurto48E88thxhc41urzjAiPDnNDBJiOZmtBEj3HVzZJwZEQLrKFrbvyoLDVM954Pk9DE2v+jeZzLTzlFk/dwNf0M4UKzrEBXcvLAZfkkQ28fe79rBc9Js6ZColgwSKlefd8EFIrcyInVl+VMlEsaB6EDQRCP4JpZEiO6LyITi5DRTvNyI8BbfeFeNRMR12YGUfAgMikwnA6fwXujw1MoY3cBZV11qsOZe7kly/ckvqy/CiWAjo1EIkXMJhz2IyzzVoxvpWRdDgbda3iOrasywCWB4oi7eawiNSDEEra0fnwjEavxSQ2Q8SV2JF44UUZatxbhVBg86DXTyWsWLIdPRHmP0qHiL7aw0dSL5ehLEfGj8XNf1TF+g1aVY8B8gwYuLGxqwyqDsNj5eUk0WXjz4+DamywZMLuqMHAF76WYChOsSuC+I89uadRpZ4FMhSei1hEwn2D4tr3BTIUipy/SFu"
}


kubectl delete deployment -n default security-api-deployment
kubectl delete replicaset -n default -l app=security-api
kubectl delete pod -n default -l app=security-api --force --grace-period=0
kubectl delete service security-api-service -n default

kubectl delete deployment -n default public-www-deployment
kubectl delete replicaset -n default -l app=public-www
kubectl delete pod -n default -l app=public-www --force --grace-period=0
kubectl delete service public-www-service -n default




Ensure you have a development certificate installed for HTTPS to work properly. Run these commands in PowerShell:
dotnet dev-certs https --check


dotnet dev-certs https --trust

dotnet run --launch-profile https