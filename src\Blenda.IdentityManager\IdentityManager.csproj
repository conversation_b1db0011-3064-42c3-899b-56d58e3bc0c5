<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <SelfContained>false</SelfContained>
    <Version>*******</Version>
	  <AssemblyVersion>*******</AssemblyVersion>
	  <FileVersion>*******</FileVersion>
	  <Company>Blenda SpA</Company>
	  <Authors><EMAIL></Authors>
	  <Product>Blenda.API.Identity</Product>
	  <Description>API RESTful for Blenda GRP</Description> 
	  <Copyright>Copyright © 2025</Copyright> 
  </PropertyGroup>


  <ItemGroup>
    <ProjectReference Include="..\Blenda.Shared\Blenda.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.12.0" />
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.DataProtection.EntityFrameworkCore" Version="9.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.6">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="9.0.8" />
    <PackageReference Include="Microsoft.Graph" Version="5.56.0" />
    <PackageReference Include="Oracle.EntityFrameworkCore" Version="9.23.80" />
    <PackageReference Include="QRCoder" Version="1.4.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="9.0.1" />
    <PackageReference Include="System.Threading.RateLimiting" Version="7.0.0" />

    <!-- COMMENTED: Private packages not available in public NuGet -->
    <!-- <PackageReference Include="Blenda.Core.DBContext" Version="0.5.4" /> -->
    <!-- <PackageReference Include="Blenda.Core.Shared" Version="0.6.17" /> -->    
  </ItemGroup>

    <ItemGroup>
    <Content Include="*.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>  
  </ItemGroup>



</Project>
