using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using IdentityManager.Services.Interfaces;
using Microsoft.AspNetCore.Identity;
using Microsoft.IdentityModel.Tokens;

namespace IdentityManager.Services;

/// <summary>
/// Service for JWT token generation and management
/// </summary>
public class JwtTokenService : IJwtTokenService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<JwtTokenService> _logger;
    private readonly JwtSecurityTokenHandler _tokenHandler;
    private readonly IServiceProvider _serviceProvider;

    public JwtTokenService(IConfiguration configuration, ILogger<JwtTokenService> logger, IServiceProvider serviceProvider)
    {
        _configuration = configuration;
        _logger = logger;
        _tokenHandler = new JwtSecurityTokenHandler();
        _serviceProvider = serviceProvider;
    }

    /// <inheritdoc />
    public async Task<string> GenerateAccessTokenAsync<TUser>(TUser user, UserManager<TUser> userManager, IEnumerable<Claim>? additionalClaims = null) 
        where TUser : class
    {
        try
        {
            var userId = await userManager.GetUserIdAsync(user);
            var email = await userManager.GetEmailAsync(user);
            var roles = await userManager.GetRolesAsync(user);
            var securityStamp = await userManager.GetSecurityStampAsync(user);

            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, userId),
                new(ClaimTypes.Email, email ?? string.Empty),
                new(ClaimTypes.Name, email ?? string.Empty),
                new("security_stamp", securityStamp ?? string.Empty),
                new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
            };

            // Add role claims
            foreach (var role in roles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role));
            }

            // Get user's individual claims
            var userClaims = await userManager.GetClaimsAsync(user);
            claims.AddRange(userClaims);

            // Get role-based claims for each role (if RoleManager is available)
            var roleManager = _serviceProvider.GetService<RoleManager<IdentityRole>>();
            if (roleManager != null)
            {
                foreach (var roleName in roles)
                {
                    var roleEntity = await roleManager.FindByNameAsync(roleName);
                    if (roleEntity != null)
                    {
                        var roleClaims = await roleManager.GetClaimsAsync(roleEntity);
                        claims.AddRange(roleClaims);
                    }
                }
            }

            // Add additional claims if provided
            if (additionalClaims != null)
            {
                claims.AddRange(additionalClaims);
            }

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(GetJwtSecretKey()));
            var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddMinutes(GetTokenExpirationMinutes()),
                Issuer = GetJwtIssuer(),
                Audience = GetJwtAudience(),
                SigningCredentials = credentials
            };

            var token = _tokenHandler.CreateToken(tokenDescriptor);
            var tokenString = _tokenHandler.WriteToken(token);

            _logger.LogInformation("JWT access token generated successfully for user {UserId}", userId);
            return tokenString;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating JWT access token for user");
            throw;
        }
    }

    /// <inheritdoc />
    public string GenerateRefreshToken<TUser>(TUser user) where TUser : class
    {
        try
        {
            var randomBytes = new byte[32];
            using (var rng = System.Security.Cryptography.RandomNumberGenerator.Create())
            {
                rng.GetBytes(randomBytes);
            }

            var refreshToken = Convert.ToBase64String(randomBytes);
            _logger.LogInformation("Refresh token generated successfully");
            return refreshToken;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating refresh token");
            throw;
        }
    }

    /// <inheritdoc />
    public string? ValidateTokenAsync(string token)
    {
        try
        {
            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(GetJwtSecretKey()));
            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ValidIssuer = GetJwtIssuer(),
                ValidAudience = GetJwtAudience(),
                IssuerSigningKey = key,
                ClockSkew = TimeSpan.Zero
            };

            var principal = _tokenHandler.ValidateToken(token, validationParameters, out var validatedToken);
            var userId = principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            _logger.LogInformation("JWT token validated successfully for user {UserId}", userId);
            return userId;
        }
        catch (SecurityTokenException ex)
        {
            _logger.LogWarning("JWT token validation failed: {Message}", ex.Message);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating JWT token");
            return null;
        }
    }

    /// <inheritdoc />
    public int GetTokenExpirationMinutes()
    {
        return _configuration.GetValue<int>("JwtSettings:ExpirationMinutes", 60);
    }

    private string GetJwtSecretKey()
    {
        return Environment.GetEnvironmentVariable("JWT_SECRET_KEY")
            ?? _configuration["JwtSettings:SecretKey"]
            ?? throw new InvalidOperationException("JWT secret key not found in configuration");
    }

    private string GetJwtIssuer()
    {
        return Environment.GetEnvironmentVariable("JWT_ISSUER")
            ?? _configuration["JwtSettings:Issuer"]
            ?? throw new InvalidOperationException("JWT issuer not found in configuration");
    }

    private string GetJwtAudience()
    {
        return Environment.GetEnvironmentVariable("JWT_AUDIENCE")
            ?? _configuration["Jwt:Audience"]  // FIX: Consistent with WebApplicationBuilderExtensions
            ?? "https://identity.blenda.lat";  // FIX: Consistent fallback value
    }
}
