using System.Diagnostics;
using System.Security.Claims;
using System.Text;
using Blenda.IdentityManager.Models;
using Blenda.IdentityManager.Services;
using Microsoft.Extensions.DependencyInjection;

namespace Blenda.IdentityManager.Middleware;

/// <summary>
/// Middleware for comprehensive API request auditing and security logging
/// </summary>
public class AuditingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<AuditingMiddleware> _logger;
    private readonly IServiceScopeFactory _serviceScopeFactory;

    public AuditingMiddleware(RequestDelegate next, ILogger<AuditingMiddleware> logger, IServiceScopeFactory serviceScopeFactory)
    {
        _next = next;
        _logger = logger;
        _serviceScopeFactory = serviceScopeFactory;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        var auditEntry = await CreateAuditEntryAsync(context);

        // Capture request body for security-sensitive endpoints
        string? requestBody = null;
        if (ShouldCaptureRequestBody(context.Request.Path))
        {
            requestBody = await CaptureRequestBodyAsync(context.Request);
            auditEntry.RequestBody = requestBody;
        }

        try
        {
            await _next(context);
            
            // Request completed successfully
            stopwatch.Stop();
            auditEntry.StatusCode = context.Response.StatusCode;
            auditEntry.Success = context.Response.StatusCode < 400;
            auditEntry.ResponseTimeMs = stopwatch.ElapsedMilliseconds;
            auditEntry.ResponseContentType = context.Response.ContentType;

            // Log slow requests
            if (stopwatch.ElapsedMilliseconds > 5000)
            {
                _logger.LogWarning("Slow API request: {Method} {Path} took {ElapsedMs}ms for user {UserId}",
                    context.Request.Method, context.Request.Path, stopwatch.ElapsedMilliseconds, auditEntry.UserId);
            }
        }
        catch (Exception ex)
        {
            // Request failed with exception
            stopwatch.Stop();
            auditEntry.Success = false;
            auditEntry.ErrorMessage = ex.Message;
            auditEntry.StatusCode = 500;
            auditEntry.ResponseTimeMs = stopwatch.ElapsedMilliseconds;

            _logger.LogError(ex, "Request failed: {Method} {Path} for user {UserId}",
                context.Request.Method, context.Request.Path, auditEntry.UserId);

            throw; // Re-throw to maintain normal exception handling
        }
        finally
        {
            // Always log the audit entry using scoped service
            try
            {
                using var scope = _serviceScopeFactory.CreateScope();
                var auditService = scope.ServiceProvider.GetRequiredService<IAuditService>();
                
                await auditService.LogRequestAsync(auditEntry);

                // Log security-sensitive operations with additional detail
                if (IsSecuritySensitiveEndpoint(context.Request.Path))
                {
                    await LogSecuritySensitiveOperationAsync(context, auditEntry, auditService);
                }
            }
            catch (Exception auditEx)
            {
                _logger.LogError(auditEx, "Failed to log audit entry for {Method} {Path}",
                    context.Request.Method, context.Request.Path);
            }
        }
    }

    private async Task<ApiAuditEntry> CreateAuditEntryAsync(HttpContext context)
    {
        return new ApiAuditEntry
        {
            Method = context.Request.Method,
            Path = context.Request.Path.Value ?? string.Empty,
            IpAddress = GetClientIpAddress(context),
            UserAgent = context.Request.Headers["User-Agent"].FirstOrDefault(),
            UserId = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value,
            Timestamp = DateTime.UtcNow,
            RequestSize = GetRequestSize(context.Request),
            Headers = GetSecurityRelevantHeaders(context.Request)
        };
    }

    private static string GetClientIpAddress(HttpContext context)
    {
        // Check for forwarded IP first (for load balancers/proxies)
        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        return context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
    }

    private static int GetRequestSize(HttpRequest request)
    {
        if (request.ContentLength.HasValue)
        {
            return (int)request.ContentLength.Value;
        }

        return 0;
    }

    private static Dictionary<string, string> GetSecurityRelevantHeaders(HttpRequest request)
    {
        var relevantHeaders = new[] { "Origin", "Referer", "Authorization", "X-Requested-With", "Content-Type" };
        var headers = new Dictionary<string, string>();

        foreach (var headerName in relevantHeaders)
        {
            if (request.Headers.TryGetValue(headerName, out var headerValues))
            {
                var value = string.Join(", ", headerValues.ToArray());
                // Sanitize Authorization header
                if (headerName.Equals("Authorization", StringComparison.OrdinalIgnoreCase))
                {
                    value = value.StartsWith("Bearer ") ? "Bearer [REDACTED]" : "[REDACTED]";
                }
                headers[headerName] = value;
            }
        }

        return headers;
    }

    private static async Task<string?> CaptureRequestBodyAsync(HttpRequest request)
    {
        if (request.ContentLength == 0 || request.ContentLength > 1024 * 10) // Limit to 10KB
        {
            return null;
        }

        try
        {
            request.EnableBuffering();
            request.Body.Position = 0;

            using var reader = new StreamReader(request.Body, Encoding.UTF8, leaveOpen: true);
            var body = await reader.ReadToEndAsync();
            
            request.Body.Position = 0; // Reset position for next middleware

            // Sanitize sensitive data
            return SanitizeRequestBody(body);
        }
        catch
        {
            return null;
        }
    }

    private static string SanitizeRequestBody(string body)
    {
        if (string.IsNullOrEmpty(body))
        {
            return body;
        }

        // Basic sanitization - replace potential passwords and sensitive fields
        var sensitiveFields = new[] { "password", "confirmPassword", "currentPassword", "newPassword", "token", "refreshToken" };
        
        foreach (var field in sensitiveFields)
        {
            // Simple JSON field replacement (this is basic - in production use proper JSON parsing)
            var pattern = $"\"{field}\"\\s*:\\s*\"[^\"]*\"";
            body = System.Text.RegularExpressions.Regex.Replace(body, pattern, $"\"{field}\":\"[REDACTED]\"", 
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        }

        return body;
    }

    private static bool ShouldCaptureRequestBody(PathString path)
    {
        var securityEndpoints = new[]
        {
            "/api/auth/login",
            "/api/auth/register",
            "/api/auth/reset-password",
            "/api/auth/change-password",
            "/api/auth/refresh"
        };

        return securityEndpoints.Any(endpoint => path.StartsWithSegments(endpoint));
    }

    private static bool IsSecuritySensitiveEndpoint(PathString path)
    {
        var securityPaths = new[]
        {
            "/api/auth/",
            "/api/profile/",
            "/api/gdpr/",
            "/api/admin/"
        };

        return securityPaths.Any(securityPath => path.StartsWithSegments(securityPath));
    }

    private async Task LogSecuritySensitiveOperationAsync(HttpContext context, ApiAuditEntry auditEntry, IAuditService auditService)
    {
        var eventType = DetermineSecurityEventType(context.Request.Path, context.Request.Method);
        
        if (!string.IsNullOrEmpty(eventType))
        {
            var securityEntry = new SecurityAuditEntry
            {
                EventType = eventType,
                UserId = auditEntry.UserId ?? "Anonymous",
                Success = auditEntry.Success,
                IpAddress = auditEntry.IpAddress,
                UserAgent = auditEntry.UserAgent,
                Timestamp = auditEntry.Timestamp,
                Resource = auditEntry.Path,
                Action = context.Request.Method
            };

            if (!auditEntry.Success)
            {
                securityEntry.Reason = auditEntry.ErrorMessage ?? $"HTTP {auditEntry.StatusCode}";
            }

            await auditService.LogSecurityEventAsync(securityEntry);
        }
    }

    private static string DetermineSecurityEventType(PathString path, string method)
    {
        return path.Value?.ToLowerInvariant() switch
        {
            var p when p?.Contains("/api/auth/login") == true => "Authentication",
            var p when p?.Contains("/api/auth/logout") == true => "Logout",
            var p when p?.Contains("/api/auth/register") == true => "Registration",
            var p when p?.Contains("/api/auth/reset-password") == true => "PasswordReset",
            var p when p?.Contains("/api/auth/change-password") == true => "PasswordChange",
            var p when p?.Contains("/api/auth/2fa") == true => "TwoFactorAuthentication",
            var p when p?.Contains("/api/profile/") == true && method == "PUT" => "ProfileUpdate",
            var p when p?.Contains("/api/gdpr/") == true => "GDPROperation",
            var p when p?.Contains("/api/admin/") == true => "AdminOperation",
            _ => string.Empty
        };
    }
}
