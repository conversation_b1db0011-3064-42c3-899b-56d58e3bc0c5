using Microsoft.AspNetCore.Identity;
using IdentityManager.Models;
using Microsoft.AspNetCore.Http.HttpResults;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.WebUtilities;
using System.Text;
using Microsoft.AspNetCore.Identity.Data;
using IdentityManager.Services.Validation;
using IdentityManager.Services;
using Blenda.Shared.Messaging.Services;
using Blenda.Shared.Messaging.Constants;
using Blenda.Shared.Messaging.Models;
using Blenda.Shared.Messaging.Extensions;

using ValidationProblem = Microsoft.AspNetCore.Http.HttpResults.ValidationProblem;
using IdentityConsentRecord = IdentityManager.Models.ConsentRecord;
using IdentityConsentType = IdentityManager.Models.ConsentType;
using Microsoft.Extensions.Options;

namespace IdentityManager.Services.Handlers;

/// <summary>
/// Enhanced user registration handler with explicit GDPR consent recording and human-friendly messaging
/// Provides comprehensive user registration with:
/// - Standardized validation error messages
/// - Multi-language support
/// - GDPR compliance with automatic consent tracking
/// - Consistent API response formatting
/// - Security-conscious error handling
/// </summary>
public class EnhancedUserRegistrationHandler
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<EnhancedUserRegistrationHandler> _logger;
    private readonly IGDPRComplianceService _gdprService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IConfiguration _configuration;
    private readonly GrpApiOptions _options;
    private readonly IEmailSender<ApplicationUser> _emailSender;
    private readonly IClientCommunicationService _communicationService;
    private readonly IMessageService _messageService;

    public EnhancedUserRegistrationHandler(
        UserManager<ApplicationUser> userManager,
        ILogger<EnhancedUserRegistrationHandler> logger,
        IGDPRComplianceService gdprService,
        IHttpContextAccessor httpContextAccessor,
        IConfiguration configuration,
        IOptions<GrpApiOptions> options,    
        IEmailSender<ApplicationUser> emailSender,
        IClientCommunicationService communicationService,
        IMessageService messageService)
    {
        _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _gdprService = gdprService ?? throw new ArgumentNullException(nameof(gdprService));
        _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _emailSender = emailSender ?? throw new ArgumentNullException(nameof(emailSender));
        _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
        _messageService = messageService ?? throw new ArgumentNullException(nameof(messageService));
    }

    /// <summary>
    /// Registers a new user with explicit GDPR consent recording and standardized response formatting
    /// This method ensures compliance with "cuando un usuario se registra en la plataforma acepta explicitamente las condiciones de uso y la política de privacidad"
    /// </summary>
    /// <param name="registration">Extended registration request with GDPR consent flags</param>
    /// <param name="role">User role to assign (defaults to "User")</param>
    /// <param name="emailValidator">Optional email domain validator function</param>
    /// <returns>Standardized registration response with human-friendly messages</returns>
    public async Task<Results<Ok<RegistrationResponse>, ValidationProblem>> RegisterUserWithConsentAsync(
        ExtendedRegisterRequest registration, 
        string role = "User", 
        Func<string, bool>? emailValidator = null)
    {
        if (registration == null)
        {
            var nullError = new Dictionary<string, string[]>
            {
                ["Registration"] = [_messageService.GetMessage(MessageKeys.Validation.Required)]
            };
            return TypedResults.ValidationProblem(nullError);
        }

        var clientIpAddress = GetClientIPAddress();
        _logger.LogInformation("Starting user registration with GDPR consent for email: {Email} from IP: {IP}", 
            registration.Email, clientIpAddress);

        // Perform comprehensive input validation
        var validationErrors = await ValidateRegistrationRequestAsync(registration, emailValidator);
        if (validationErrors.Any())
        {
            return TypedResults.ValidationProblem(validationErrors);
        }

        try
        {
            var registrationPrefix = string.IsNullOrWhiteSpace(registration.ClientApp) ? _options.DefaultTenantId.ToString() : registration.ClientApp;

            var registrationUniqueEmail = $"{registrationPrefix}+{registration.Email}";

            // Check if user already exists - use generic message to prevent email enumeration
            if (await _userManager.FindByEmailAsync(registrationUniqueEmail) != null)
            {
                var existsError = new Dictionary<string, string[]>
                {
                    ["Email"] = [_messageService.GetMessage(MessageKeys.Registration.UserAlreadyExists)]
                };
                return TypedResults.ValidationProblem(existsError);
            }

            // Create the user with GDPR compliance properties
            var user = await CreateUserAsync(registration, registrationUniqueEmail);
            var result = await _userManager.CreateAsync(user, registration.Password);

            if (!result.Succeeded)
            {
                var errors = TranslateIdentityErrors(result.Errors);
                return TypedResults.ValidationProblem(errors);
            }

            // Add role to user
            if (!string.IsNullOrEmpty(role))
            {
                await _userManager.AddToRoleAsync(user, role);
                _logger.LogDebug("Assigned role {Role} to user {UserId}", role, user.Id);
            }

            // Record GDPR consents automatically based on registration acceptance
            var recordedConsents = await RecordRegistrationConsentAsync(user.Id, registration, clientIpAddress);

            // Generate and send email confirmation
            await SendEmailConfirmationAsync(user, registration);

            _logger.LogInformation("User registration completed successfully for {Email} with {ConsentCount} GDPR consents recorded", 
                registration.Email, recordedConsents.Count);

            // Create standardized response with human-friendly messaging
            var response = CreateSuccessfulRegistrationResponse(user, registration, recordedConsents);
            return TypedResults.Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during user registration for email: {Email}", registration.Email);
            
            var errorResponse = new Dictionary<string, string[]>
            {
                ["Registration"] = [_messageService.GetMessage(MessageKeys.Registration.InternalError)]
            };
            return TypedResults.ValidationProblem(errorResponse);
        }
    }

    /// <summary>
    /// Validates the registration request with comprehensive input validation
    /// </summary>
    /// <param name="registration">The registration request to validate</param>
    /// <param name="emailValidator">Optional email domain validator function</param>
    /// <returns>Dictionary of validation errors, empty if validation passes</returns>
    private async Task<Dictionary<string, string[]>> ValidateRegistrationRequestAsync(
        ExtendedRegisterRequest registration, 
        Func<string, bool>? emailValidator = null)
    {
        var errors = new Dictionary<string, string[]>();

        // Validate explicit consent requirements
        if (!registration.AcceptTermsOfService)
        {
            errors["AcceptTermsOfService"] = [_messageService.GetMessage(MessageKeys.Consent.TermsOfServiceRequired)];
        }

        if (!registration.AcceptPrivacyPolicy)
        {
            errors["AcceptPrivacyPolicy"] = [_messageService.GetMessage(MessageKeys.Consent.PrivacyPolicyRequired)];
        }

        // Validate email format
        if (string.IsNullOrWhiteSpace(registration.Email))
        {
            errors["Email"] = [_messageService.GetMessage(MessageKeys.Validation.Required)];
        }
        else if (!new EmailAddressAttribute().IsValid(registration.Email))
        {
            errors["Email"] = [_messageService.GetMessage(MessageKeys.Registration.InvalidEmailFormat)];
        }
        else if (emailValidator != null && !emailValidator(registration.Email))
        {
            errors["Email"] = [_messageService.GetMessage(MessageKeys.Registration.EmailDomainNotAllowed)];
        }

        // Validate password is present (complexity validation handled by Identity)
        if (string.IsNullOrWhiteSpace(registration.Password))
        {
            errors["Password"] = [_messageService.GetMessage(MessageKeys.Validation.Required)];
        }

        return errors;
    }

    /// <summary>
    /// Creates a new ApplicationUser instance with GDPR compliance properties
    /// </summary>
    /// <param name="registration">The registration request</param>
    /// <param name="uniqueEmail">The unique email address for the user</param>
    /// <returns>Configured ApplicationUser instance</returns>
    private async Task<ApplicationUser> CreateUserAsync(ExtendedRegisterRequest registration, string uniqueEmail)
    {
        return new ApplicationUser
        {
            UserName = uniqueEmail,
            Email = uniqueEmail,
            EmailConfirmed = false,
            // Set GDPR properties based on explicit consent
            HasAcceptedTermsOfService = registration.AcceptTermsOfService,
            TermsOfServiceAcceptedAt = registration.AcceptTermsOfService ? DateTime.UtcNow : null,
            PrivacyPolicyAcceptedAt = registration.AcceptPrivacyPolicy ? DateTime.UtcNow : null,

            // Assign to default tenant or based on ClientApp if provided
            TenantId = !string.IsNullOrWhiteSpace(registration.ClientApp) && Guid.TryParse(registration.ClientApp, out var tenantId) ? tenantId : _options.DefaultTenantId
        };
    }

    /// <summary>
    /// Translates ASP.NET Core Identity errors to localized user-friendly messages
    /// </summary>
    /// <param name="identityErrors">Collection of identity errors</param>
    /// <returns>Dictionary of field names and localized error messages</returns>
    private Dictionary<string, string[]> TranslateIdentityErrors(IEnumerable<IdentityError> identityErrors)
    {
        var errors = new Dictionary<string, string[]>();
        
        foreach (var error in identityErrors)
        {
            var messageKey = error.Code switch
            {
                "PasswordTooShort" => MessageKeys.Registration.PasswordTooWeak,
                "PasswordRequiresDigit" => MessageKeys.Registration.PasswordTooWeak,
                "PasswordRequiresLower" => MessageKeys.Registration.PasswordTooWeak,
                "PasswordRequiresUpper" => MessageKeys.Registration.PasswordTooWeak,
                "PasswordRequiresNonAlphanumeric" => MessageKeys.Registration.PasswordTooWeak,
                "DuplicateEmail" => MessageKeys.Registration.UserAlreadyExists,
                "InvalidEmail" => MessageKeys.Registration.InvalidEmailFormat,
                _ => MessageKeys.Validation.InvalidValue
            };

            var fieldName = error.Code.Contains("Password") ? "Password" : 
                           error.Code.Contains("Email") ? "Email" : 
                           "General";

            if (!errors.ContainsKey(fieldName))
            {
                errors[fieldName] = [];
            }

            var existingErrors = errors[fieldName].ToList();
            existingErrors.Add(_messageService.GetMessage(messageKey));
            errors[fieldName] = existingErrors.ToArray();
        }

        return errors;
    }

    /// <summary>
    /// Sends email confirmation with proper error handling
    /// </summary>
    /// <param name="user">The user to send confirmation to</param>
    /// <param name="registration">The registration request</param>
    private async Task SendEmailConfirmationAsync(ApplicationUser user, ExtendedRegisterRequest registration)
    {
        try
        {
            var code = await _userManager.GenerateEmailConfirmationTokenAsync(user);
            code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
            
            var userId = await _userManager.GetUserIdAsync(user);
            var frontendUrl = _configuration["ApiSettings:PublicFacingService"] ?? "https://blenda.lat";
            var confirmEmailUrl = $"{frontendUrl}/account/confirm-email?userId={userId}&code={code}";

            if (!string.IsNullOrWhiteSpace(registration.Source))
            {
                confirmEmailUrl += $"&source={registration.Source}";
            }

            if (!string.IsNullOrWhiteSpace(registration.ClientApp))
            {
                confirmEmailUrl += $"&clientApp={registration.ClientApp}";
            }

            // RC: 24-09-2025, Pending review with UX team            
            // TenantAdmin and LaborInspector get a different email template
            // Use different method for role-based confirmation
            await _emailSender.SendConfirmationLinkAsync(user, registration.Email, confirmEmailUrl);
            
            _logger.LogInformation("Email confirmation sent successfully to {Email}", registration.Email);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email confirmation to {Email}", registration.Email);
            // Don't fail registration if email sending fails
        }
    }

    /// <summary>
    /// Creates a standardized successful registration response
    /// </summary>
    /// <param name="user">The created user</param>
    /// <param name="registration">The registration request</param>
    /// <param name="consentRecords">The recorded consent records</param>
    /// <returns>Formatted registration response</returns>
    private RegistrationResponse CreateSuccessfulRegistrationResponse(
        ApplicationUser user, 
        ExtendedRegisterRequest registration, 
        List<IdentityConsentRecord> consentRecords)
    {
        return new RegistrationResponse
        {
            Success = true,
            Message = _messageService.GetMessage(MessageKeys.Registration.Success),
            MessageType = MessageType.Success,
            Timestamp = DateTime.UtcNow,
            UserId = user.Id,
            Email = registration.Email,
            RequiresEmailConfirmation = true,
            // ConsentRecords = consentRecords.Select(c => new Blenda.Shared.Messaging.Models.ConsentRecord
            // {
            //     ConsentId = c.ConsentId.ToString(),
            //     ConsentType = MapConsentType(c.ConsentType),
            //     PolicyVersion = c.PolicyVersion,
            //     ConsentDate = c.ConsentDate,
            //     ExpiryDate = null, // IdentityManager ConsentRecord doesn't have ExpiryDate
            //     Description = GetConsentDescription(c.ConsentType)
            // }).ToList(),
            RegisteredAt = DateTime.UtcNow,
            TenantId = user.TenantId.ToString(),
            NextSteps = new[]
            {
                _messageService.GetMessage(MessageKeys.Email.ConfirmationSent),
                "Click the confirmation link to activate your account",
                "Return to the login page to sign in"
            }
        };
    }

    /// <summary>
    /// Maps internal ConsentType to shared library ConsentType
    /// </summary>
    private Blenda.Shared.Messaging.Models.ConsentType MapConsentType(IdentityConsentType internalType)
    {
        return internalType switch
        {
            IdentityConsentType.Essential => Blenda.Shared.Messaging.Models.ConsentType.Essential,
            IdentityConsentType.Marketing => Blenda.Shared.Messaging.Models.ConsentType.Marketing,
            IdentityConsentType.Analytics => Blenda.Shared.Messaging.Models.ConsentType.Analytics,
            _ => Blenda.Shared.Messaging.Models.ConsentType.Essential
        };
    }

    /// <summary>
    /// Gets human-friendly description for consent types
    /// </summary>
    private string GetConsentDescription(IdentityConsentType consentType)
    {
        return consentType switch
        {
            IdentityConsentType.Essential => "Terms of Service and Privacy Policy acceptance",
            IdentityConsentType.Marketing => "Marketing communications and promotional content",
            IdentityConsentType.Analytics => "Usage analytics and performance monitoring",
            _ => "General consent"
        };
    }

    /// <summary>
    /// Records GDPR consent during registration based on explicit user acceptance
    /// Implements: "cuando un usuario se registra en la plataforma acepta explicitamente las condiciones de uso y la política de privacidad"
    /// </summary>
    private async Task<List<IdentityConsentRecord>> RecordRegistrationConsentAsync(
        string userId, 
        ExtendedRegisterRequest registration, 
        string clientIpAddress)
    {
        var recordedConsents = new List<IdentityConsentRecord>();

        try
        {
            var userAgent = _httpContextAccessor.HttpContext?.Request.Headers.UserAgent.ToString() ?? "Unknown";

            // Record Terms of Service consent (always required)
            if (registration.AcceptTermsOfService)
            {
                var termsConsentId = await _gdprService.RecordConsentAsync(
                    userId, 
                    IdentityConsentType.Essential,
                    clientIpAddress,
                    userAgent,
                    registration.TermsOfServiceVersion ?? "1.0",
                    "registration",
                    null); // Essential consent doesn't expire

                recordedConsents.Add(new IdentityConsentRecord
                {
                    ConsentId = termsConsentId,
                    ConsentType = IdentityConsentType.Essential,
                    PolicyVersion = registration.TermsOfServiceVersion ?? "1.0",
                    ConsentDate = DateTime.UtcNow
                });

                _logger.LogDebug("Recorded Terms of Service consent {ConsentId} for user {UserId}", 
                    termsConsentId, userId);
            }

            // Record Privacy Policy consent (always required)
            if (registration.AcceptPrivacyPolicy)
            {
                var privacyConsentId = await _gdprService.RecordConsentAsync(
                    userId, 
                    IdentityConsentType.Essential,
                    clientIpAddress,
                    userAgent,
                    registration.PrivacyPolicyVersion ?? "1.0",
                    "registration",
                    null); // Essential consent doesn't expire

                recordedConsents.Add(new IdentityConsentRecord
                {
                    ConsentId = privacyConsentId,
                    ConsentType = IdentityConsentType.Essential,
                    PolicyVersion = registration.PrivacyPolicyVersion ?? "1.0",
                    ConsentDate = DateTime.UtcNow
                });

                _logger.LogDebug("Recorded Privacy Policy consent {ConsentId} for user {UserId}", 
                    privacyConsentId, userId);
            }

            // Record Marketing consent (optional)
            if (registration.AcceptMarketingCommunications)
            {
                var marketingConsentId = await _gdprService.RecordConsentAsync(
                    userId, 
                    IdentityConsentType.Marketing,
                    clientIpAddress,
                    userAgent,
                    "1.0",
                    "registration",
                    365); // Marketing consent expires after 1 year

                recordedConsents.Add(new IdentityConsentRecord
                {
                    ConsentId = marketingConsentId,
                    ConsentType = IdentityConsentType.Marketing,
                    ConsentDate = DateTime.UtcNow
                });

                _logger.LogDebug("Recorded Marketing consent {ConsentId} for user {UserId}", 
                    marketingConsentId, userId);
            }

            // Record Analytics consent (optional)
            if (registration.AcceptAnalytics)
            {
                var analyticsConsentId = await _gdprService.RecordConsentAsync(
                    userId, 
                    IdentityConsentType.Analytics,
                    clientIpAddress,
                    userAgent,
                    "1.0",
                    "registration",
                    365); // Analytics consent expires after 1 year

                recordedConsents.Add(new IdentityConsentRecord
                {
                    ConsentId = analyticsConsentId,
                    ConsentType = IdentityConsentType.Analytics,
                    ConsentDate = DateTime.UtcNow
                });

                _logger.LogDebug("Recorded Analytics consent {ConsentId} for user {UserId}", 
                    analyticsConsentId, userId);
            }

            // Initialize privacy settings for the user
            await _gdprService.InitializePrivacySettingsAsync(userId);

            _logger.LogInformation("Successfully recorded {ConsentCount} GDPR consents for user {UserId} during registration", 
                recordedConsents.Count, userId);

            return recordedConsents;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording GDPR consents during registration for user {UserId}", userId);
            // Even if consent recording fails, we don't want to fail the registration
            // The consent can be recorded later through other means
            return recordedConsents;
        }
    }

    /// <summary>
    /// Gets the client IP address for audit purposes with proper proxy and load balancer support
    /// Handles X-Forwarded-For, X-Real-IP headers and direct connections
    /// </summary>
    /// <returns>Client IP address or "Unknown" if unable to determine</returns>
    private string GetClientIPAddress()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null)
            {
                _logger.LogWarning("HttpContext not available when trying to get client IP address");
                return "Unknown";
            }

            // Check for forwarded IP address first (in case of proxy/load balancer)
            var forwardedFor = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                // X-Forwarded-For may contain multiple IPs, take the first one (client IP)
                var clientIp = forwardedFor.Split(',')[0].Trim();
                if (!string.IsNullOrEmpty(clientIp) && clientIp != "unknown")
                {
                    return clientIp;
                }
            }

            // Check for real IP header (used by some proxies)
            var realIP = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(realIP) && realIP != "unknown")
            {
                return realIP;
            }

            // Check for Cloudflare specific header
            var cfConnectingIP = httpContext.Request.Headers["CF-Connecting-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(cfConnectingIP))
            {
                return cfConnectingIP;
            }

            // Fall back to connection remote IP
            var remoteIp = httpContext.Connection.RemoteIpAddress?.ToString();
            if (!string.IsNullOrEmpty(remoteIp))
            {
                return remoteIp;
            }

            _logger.LogWarning("Unable to determine client IP address from any source");
            return "Unknown";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting client IP address");
            return "Unknown";
        }
    }
}
