using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using IdentityManager.Models;
using Blenda.IdentityManager.Models;
using Microsoft.AspNetCore.DataProtection.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace IdentityManager.Data
{
    // public class ApplicationDbContext : IdentityDbContext
    // {
    //     public ApplicationDbContext(DbContextOptions options) : base(options)
    //     {
    //     }
    // }

    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>, IDataProtectionKeyContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        // =============================================================================
        // GDPR COMPLIANCE ENTITY SETS
        // =============================================================================
        
        /// <summary>
        /// GDPR: User consent tracking for comprehensive compliance
        /// </summary>
        public DbSet<UserConsent> UserConsents { get; set; }
        
        /// <summary>
        /// GDPR: User privacy settings and preferences
        /// </summary>
        public DbSet<UserPrivacySettings> UserPrivacySettings { get; set; }
        
        /// <summary>
        /// GDPR: Data processing audit trail
        /// </summary>
        public DbSet<DataProcessingRecord> DataProcessingRecords { get; set; }

        // =============================================================================
        // SECURITY AUDIT AND SESSION MANAGEMENT ENTITY SETS
        // =============================================================================
        
        /// <summary>
        /// Security: Audit logs for authentication and security events
        /// </summary>
        public DbSet<SecurityAuditLog> SecurityAuditLogs { get; set; }
        
        /// <summary>
        /// Security: User session tracking for activity management
        /// </summary>
        public DbSet<UserSession> UserSessions { get; set; }

        // =============================================================================
        // DATA PROTECTION KEYS (EXISTING)
        // =============================================================================
        
        // Data Protection Keys table
        public DbSet<DataProtectionKey> DataProtectionKeys { get; set; } = null!;

        // =============================================================================
        // ORACLE-OPTIMIZED ENTITY CONFIGURATION
        // =============================================================================

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // =============================================================================
            // APPLICATION USER GDPR CONFIGURATION
            // =============================================================================
            
            // Existing Oracle Identity configurations
            modelBuilder.Entity<ApplicationUser>(entity =>
            {
                // Existing Identity property configurations
                entity.Property(u => u.UserName)
                    .HasMaxLength(128);

                // GDPR: String properties with Oracle NVARCHAR2 optimization
                entity.Property(e => e.TermsOfServiceVersion)
                    .HasMaxLength(50)
                    .HasColumnType("NVARCHAR2(50)")
                    .IsUnicode(true);
                    
                entity.Property(e => e.PrivacyPolicyVersion)
                    .HasMaxLength(50)
                    .HasColumnType("NVARCHAR2(50)")
                    .IsUnicode(true);
                    
                entity.Property(e => e.Country)
                    .HasMaxLength(100)
                    .HasColumnType("NVARCHAR2(100)")
                    .IsUnicode(true);
                    
                entity.Property(e => e.LastLoginIPHash)
                    .HasMaxLength(128)
                    .HasColumnType("NVARCHAR2(128)")
                    .IsUnicode(false); // IP hashes don't need Unicode
                    
                entity.Property(e => e.LastLoginUserAgent)
                    .HasMaxLength(1000)
                    .HasColumnType("NVARCHAR2(1000)")
                    .IsUnicode(true);
                    
                entity.Property(e => e.SuspensionReason)
                    .HasMaxLength(500)
                    .HasColumnType("NVARCHAR2(500)")
                    .IsUnicode(true);

                // GDPR: DateTime properties with UTC precision
                entity.Property(e => e.CreatedAt)
                    .HasColumnType("TIMESTAMP(6)")
                    .HasDefaultValueSql("SYS_EXTRACT_UTC(SYSTIMESTAMP)")
                    .IsRequired();
                    
                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("TIMESTAMP(6)");
                    
                entity.Property(e => e.LastLoginAt)
                    .HasColumnType("TIMESTAMP(6)");
                    
                entity.Property(e => e.TermsOfServiceAcceptedAt)
                    .HasColumnType("TIMESTAMP(6)");
                    
                entity.Property(e => e.PrivacyPolicyAcceptedAt)
                    .HasColumnType("TIMESTAMP(6)");
                    
                entity.Property(e => e.DeletionRequestedAt)
                    .HasColumnType("TIMESTAMP(6)");
                    
                entity.Property(e => e.ScheduledDeletionDate)
                    .HasColumnType("TIMESTAMP(6)");
                    
                entity.Property(e => e.LastDataExportRequestAt)
                    .HasColumnType("TIMESTAMP(6)");
                    
                entity.Property(e => e.SuspendedAt)
                    .HasColumnType("TIMESTAMP(6)");

                // GDPR: Enum properties with proper Oracle configuration and explicit sentinel values
                entity.Property(e => e.AccountStatus)
                    .HasConversion<int>()
                    .HasDefaultValue(UserAccountStatus.Active)
                    .HasSentinel(UserAccountStatus.Active);
                    
                entity.Property(e => e.LawfulBasisForProcessing)
                    .HasConversion<int>()
                    .HasDefaultValue(ProcessingLawfulBasis.Consent)
                    .HasSentinel(ProcessingLawfulBasis.Consent);

                // Multi-Tenant: Tenant reference properties with Oracle optimization
                entity.Property(e => e.TenantId)
                    .HasColumnType("RAW(16)");
                    
                entity.Property(e => e.TenantJoinedAt)
                    .HasColumnType("TIMESTAMP(6)")
                    .HasDefaultValueSql("SYS_EXTRACT_UTC(SYSTIMESTAMP)");

                // Performance: Indexes for GDPR compliance queries
                entity.HasIndex(e => e.IsEUCitizen)
                    .HasDatabaseName("IX_ApplicationUser_IsEUCitizen");
                    
                entity.HasIndex(e => e.IsMarkedForDeletion)
                    .HasDatabaseName("IX_ApplicationUser_IsMarkedForDeletion");
                    
                entity.HasIndex(e => e.ScheduledDeletionDate)
                    .HasDatabaseName("IX_ApplicationUser_ScheduledDeletionDate")
                    .HasFilter("ScheduledDeletionDate IS NOT NULL");
                    
                entity.HasIndex(e => e.AccountStatus)
                    .HasDatabaseName("IX_ApplicationUser_AccountStatus");
                    
                entity.HasIndex(e => new { e.Country, e.IsEUCitizen })
                    .HasDatabaseName("IX_ApplicationUser_Country_IsEUCitizen");
                    
                // Multi-Tenant: Performance indexes for tenant-based queries
                entity.HasIndex(e => e.TenantId)
                    .HasDatabaseName("IX_ApplicationUser_TenantId");
                    
                entity.HasIndex(e => new { e.TenantId, e.AccountStatus })
                    .HasDatabaseName("IX_ApplicationUser_TenantId_Status");
                    
                entity.HasIndex(e => new { e.TenantId, e.Email })
                    .HasDatabaseName("IX_ApplicationUser_TenantId_Email");

                // GDPR: Relationships configuration
                entity.HasMany(e => e.UserConsents)
                    .WithOne(e => e.User)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);
                    
                entity.HasOne(e => e.PrivacySettings)
                    .WithOne(e => e.User)
                    .HasForeignKey<UserPrivacySettings>(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);
                    
                entity.HasMany(e => e.DataProcessingRecords)
                    .WithOne(e => e.User)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Existing Identity Role configuration
            modelBuilder.Entity<IdentityRole>()
                .Property(r => r.Name)
                .HasMaxLength(128);

            // =============================================================================
            // USER CONSENT ENTITY CONFIGURATION
            // =============================================================================
            
            modelBuilder.Entity<UserConsent>(entity =>
            {
                // Primary key configuration
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("SYS_GUID()");

                // Oracle-optimized string properties
                entity.Property(e => e.UserId)
                    .IsRequired()
                    .HasMaxLength(450)
                    .HasColumnType("NVARCHAR2(450)");
                    
                entity.Property(e => e.IpAddressHash)
                    .HasMaxLength(128)
                    .HasColumnType("NVARCHAR2(128)")
                    .IsUnicode(false);
                    
                entity.Property(e => e.UserAgent)
                    .HasMaxLength(500)
                    .HasColumnType("NVARCHAR2(500)")
                    .IsUnicode(true);
                    
                entity.Property(e => e.PolicyVersion)
                    .HasMaxLength(50)
                    .HasColumnType("NVARCHAR2(50)")
                    .IsUnicode(false);
                    
                entity.Property(e => e.Metadata)
                    .HasMaxLength(1000)
                    .HasColumnType("NCLOB")
                    .IsUnicode(true);
                    
                entity.Property(e => e.ConsentSource)
                    .HasMaxLength(100)
                    .HasColumnType("NVARCHAR2(100)")
                    .IsUnicode(true);
                    
                entity.Property(e => e.WithdrawalReason)
                    .HasMaxLength(500)
                    .HasColumnType("NVARCHAR2(500)")
                    .IsUnicode(true);

                // DateTime properties with Oracle precision
                entity.Property(e => e.ConsentDate)
                    .HasColumnType("TIMESTAMP(6)")
                    .HasDefaultValueSql("SYS_EXTRACT_UTC(SYSTIMESTAMP)")
                    .IsRequired();
                    
                entity.Property(e => e.ExpirationDate)
                    .HasColumnType("TIMESTAMP(6)");

                // Enum properties with Oracle optimization and explicit sentinel values
                entity.Property(e => e.ConsentType)
                    .HasConversion<int>()
                    .HasSentinel(ConsentType.Essential)
                    .IsRequired();
                    
                entity.Property(e => e.Status)
                    .HasConversion<int>()
                    .HasSentinel(ConsentStatus.Granted)
                    .IsRequired();

                // Performance indexes for consent queries
                entity.HasIndex(e => new { e.UserId, e.ConsentType })
                    .HasDatabaseName("IX_UserConsent_UserId_ConsentType")
                    .IsUnique();
                    
                entity.HasIndex(e => e.ConsentDate)
                    .HasDatabaseName("IX_UserConsent_ConsentDate");
                    
                entity.HasIndex(e => e.ExpirationDate)
                    .HasDatabaseName("IX_UserConsent_ExpirationDate")
                    .HasFilter("ExpirationDate IS NOT NULL");
                    
                entity.HasIndex(e => e.Status)
                    .HasDatabaseName("IX_UserConsent_Status");

                // Table name for Oracle
                entity.ToTable("UserConsents");
            });

            // =============================================================================
            // USER PRIVACY SETTINGS CONFIGURATION
            // =============================================================================
            
            modelBuilder.Entity<UserPrivacySettings>(entity =>
            {
                // Primary key configuration
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("SYS_GUID()");

                // Oracle-optimized properties
                entity.Property(e => e.UserId)
                    .IsRequired()
                    .HasMaxLength(450)
                    .HasColumnType("NVARCHAR2(450)");
                    
                entity.Property(e => e.PreferredLanguage)
                    .HasMaxLength(10)
                    .HasColumnType("NVARCHAR2(10)")
                    .HasDefaultValue("en-US")
                    .IsUnicode(false);
                    
                entity.Property(e => e.NotificationFrequency)
                    .HasMaxLength(50)
                    .HasColumnType("NVARCHAR2(50)")
                    .HasDefaultValue("Immediate")
                    .IsUnicode(false);
                    
                entity.Property(e => e.SettingsVersion)
                    .HasMaxLength(50)
                    .HasColumnType("NVARCHAR2(50)")
                    .HasDefaultValue("1.0")
                    .IsUnicode(false);

                // DateTime with Oracle precision
                entity.Property(e => e.LastUpdated)
                    .HasColumnType("TIMESTAMP(6)")
                    .HasDefaultValueSql("SYS_EXTRACT_UTC(SYSTIMESTAMP)")
                    .IsRequired();

                // Default values for privacy settings
                entity.Property(e => e.AllowAnalytics)
                    .HasDefaultValue(false);
                entity.Property(e => e.AllowMarketing)
                    .HasDefaultValue(false);
                entity.Property(e => e.AllowThirdPartySharing)
                    .HasDefaultValue(false);
                entity.Property(e => e.AllowCrossBorderTransfer)
                    .HasDefaultValue(false);
                entity.Property(e => e.AllowAIProcessing)
                    .HasDefaultValue(false);
                entity.Property(e => e.PreferredDataRetentionDays)
                    .HasDefaultValue(2555); // 7 years

                // Unique constraint on UserId (one privacy setting per user)
                entity.HasIndex(e => e.UserId)
                    .HasDatabaseName("IX_UserPrivacySettings_UserId")
                    .IsUnique();

                // Table name for Oracle
                entity.ToTable("UserPrivacySettings");
            });

            // =============================================================================
            // DATA PROCESSING RECORD CONFIGURATION
            // =============================================================================
            
            modelBuilder.Entity<DataProcessingRecord>(entity =>
            {
                // Primary key configuration
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("SYS_GUID()");

                // Oracle-optimized string properties
                entity.Property(e => e.UserId)
                    .IsRequired()
                    .HasMaxLength(450)
                    .HasColumnType("NVARCHAR2(450)");
                    
                entity.Property(e => e.DataDescription)
                    .IsRequired()
                    .HasMaxLength(1000)
                    .HasColumnType("NCLOB")
                    .IsUnicode(true);
                    
                entity.Property(e => e.ProcessingPurpose)
                    .IsRequired()
                    .HasMaxLength(500)
                    .HasColumnType("NVARCHAR2(500)")
                    .IsUnicode(true);
                    
                entity.Property(e => e.DataCategories)
                    .HasMaxLength(500)
                    .HasColumnType("NVARCHAR2(500)")
                    .IsUnicode(true);
                    
                entity.Property(e => e.ThirdPartiesInvolved)
                    .HasMaxLength(500)
                    .HasColumnType("NVARCHAR2(500)")
                    .IsUnicode(true);
                    
                entity.Property(e => e.ProcessingSystem)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasColumnType("NVARCHAR2(200)")
                    .IsUnicode(true);
                    
                entity.Property(e => e.ProcessingMetadata)
                    .HasMaxLength(2000)
                    .HasColumnType("NCLOB")
                    .IsUnicode(true);
                    
                entity.Property(e => e.DataHash)
                    .HasMaxLength(128)
                    .HasColumnType("NVARCHAR2(128)")
                    .IsUnicode(false);

                // DateTime properties with Oracle precision
                entity.Property(e => e.ProcessingDate)
                    .HasColumnType("TIMESTAMP(6)")
                    .HasDefaultValueSql("SYS_EXTRACT_UTC(SYSTIMESTAMP)")
                    .IsRequired();

                // Enum properties with Oracle optimization and explicit sentinel values
                entity.Property(e => e.Activity)
                    .HasConversion<int>()
                    .HasSentinel(DataProcessingActivity.AccountCreation)
                    .IsRequired();
                    
                entity.Property(e => e.LawfulBasis)
                    .HasConversion<int>()
                    .HasSentinel(ProcessingLawfulBasis.Consent)
                    .IsRequired();

                // Performance indexes for audit queries
                entity.HasIndex(e => e.UserId)
                    .HasDatabaseName("IX_DataProcessingRecord_UserId");
                    
                entity.HasIndex(e => e.ProcessingDate)
                    .HasDatabaseName("IX_DataProcessingRecord_ProcessingDate");
                    
                entity.HasIndex(e => e.Activity)
                    .HasDatabaseName("IX_DataProcessingRecord_Activity");
                    
                entity.HasIndex(e => e.LawfulBasis)
                    .HasDatabaseName("IX_DataProcessingRecord_LawfulBasis");
                    
                entity.HasIndex(e => new { e.UserId, e.Activity, e.ProcessingDate })
                    .HasDatabaseName("IX_DataProcessingRecord_UserId_Activity_Date");

                // Foreign key to UserConsent
                entity.HasOne(e => e.RelatedConsent)
                    .WithMany()
                    .HasForeignKey(e => e.RelatedConsentId)
                    .OnDelete(DeleteBehavior.SetNull);

                // Table name for Oracle
                entity.ToTable("DataProcessingRecords");
            });

            // =============================================================================
            // EXISTING DATA PROTECTION KEYS CONFIGURATION
            // =============================================================================
            
            // Data Protection Keys configuration for Oracle
            modelBuilder.Entity<DataProtectionKey>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FriendlyName)
                    .HasMaxLength(255)
                    .HasColumnType("NVARCHAR2(255)");
                entity.Property(e => e.Xml)
                    .HasColumnType("CLOB"); // Use CLOB for large XML data in Oracle
            });

            // =============================================================================
            // SECURITY AUDIT LOG CONFIGURATION
            // =============================================================================
            
            modelBuilder.Entity<SecurityAuditLog>(entity =>
            {
                // Primary key configuration
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("SYS_GUID()");

                // Oracle-optimized string properties
                entity.Property(e => e.UserId)
                    .HasMaxLength(450)
                    .HasColumnType("NVARCHAR2(450)")
                    .IsRequired(false); // Allow null for system events
                    
                entity.Property(e => e.EventType)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnType("NVARCHAR2(100)")
                    .IsUnicode(false);
                    
                entity.Property(e => e.IpAddressHash)
                    .HasMaxLength(128)
                    .HasColumnType("NVARCHAR2(128)")
                    .IsUnicode(false);
                    
                entity.Property(e => e.UserAgent)
                    .HasMaxLength(1000)
                    .HasColumnType("NVARCHAR2(1000)")
                    .IsUnicode(true);
                    
                entity.Property(e => e.SessionId)
                    .HasMaxLength(128)
                    .HasColumnType("NVARCHAR2(128)")
                    .IsUnicode(false);
                    
                entity.Property(e => e.FailureReason)
                    .HasMaxLength(500)
                    .HasColumnType("NVARCHAR2(500)")
                    .IsUnicode(true);
                    
                entity.Property(e => e.SecurityMetadata)
                    .HasMaxLength(2000)
                    .HasColumnType("NCLOB")
                    .IsUnicode(true);

                // DateTime properties with Oracle precision
                entity.Property(e => e.EventTimestamp)
                    .HasColumnType("TIMESTAMP(6)")
                    .HasDefaultValueSql("SYS_EXTRACT_UTC(SYSTIMESTAMP)")
                    .IsRequired();

                // GUID properties for tenant context
                entity.Property(e => e.TenantId)
                    .HasColumnType("RAW(16)");

                // Performance indexes for security queries
                entity.HasIndex(e => e.UserId)
                    .HasDatabaseName("IX_SecurityAuditLog_UserId");
                    
                entity.HasIndex(e => e.EventTimestamp)
                    .HasDatabaseName("IX_SecurityAuditLog_EventTimestamp");
                    
                entity.HasIndex(e => e.EventType)
                    .HasDatabaseName("IX_SecurityAuditLog_EventType");
                    
                entity.HasIndex(e => new { e.EventType, e.Success })
                    .HasDatabaseName("IX_SecurityAuditLog_EventType_Success");
                    
                entity.HasIndex(e => e.TenantId)
                    .HasDatabaseName("IX_SecurityAuditLog_TenantId");
                    
                entity.HasIndex(e => new { e.TenantId, e.EventType })
                    .HasDatabaseName("IX_SecurityAuditLog_TenantId_EventType");

                // Relationship to ApplicationUser
                entity.HasOne(e => e.User)
                    .WithMany(u => u.SecurityAuditLogs)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.SetNull); // Allow orphaned logs for system events

                // Table name for Oracle
                entity.ToTable("SecurityAuditLogs");
            });

            // =============================================================================
            // USER SESSION CONFIGURATION
            // =============================================================================
            
            modelBuilder.Entity<UserSession>(entity =>
            {
                // Primary key configuration (custom string-based session ID)
                entity.HasKey(e => e.SessionId);
                entity.Property(e => e.SessionId)
                    .HasMaxLength(128)
                    .HasColumnType("NVARCHAR2(128)")
                    .IsUnicode(false);

                // Oracle-optimized string properties
                entity.Property(e => e.UserId)
                    .IsRequired()
                    .HasMaxLength(450)
                    .HasColumnType("NVARCHAR2(450)");
                    
                entity.Property(e => e.IpAddressHash)
                    .HasMaxLength(128)
                    .HasColumnType("NVARCHAR2(128)")
                    .IsUnicode(false);
                    
                entity.Property(e => e.DeviceInfo)
                    .HasMaxLength(500)
                    .HasColumnType("NVARCHAR2(500)")
                    .IsUnicode(true);
                    
                entity.Property(e => e.Location)
                    .HasMaxLength(200)
                    .HasColumnType("NVARCHAR2(200)")
                    .IsUnicode(true);
                    
                entity.Property(e => e.RefreshTokenHash)
                    .HasMaxLength(128)
                    .HasColumnType("NVARCHAR2(128)")
                    .IsUnicode(false);

                // DateTime properties with Oracle precision
                entity.Property(e => e.CreatedAt)
                    .HasColumnType("TIMESTAMP(6)")
                    .HasDefaultValueSql("SYS_EXTRACT_UTC(SYSTIMESTAMP)")
                    .IsRequired();
                    
                entity.Property(e => e.LastActivityAt)
                    .HasColumnType("TIMESTAMP(6)")
                    .IsRequired();
                    
                entity.Property(e => e.ExpiresAt)
                    .HasColumnType("TIMESTAMP(6)");

                // GUID properties for tenant context
                entity.Property(e => e.TenantId)
                    .HasColumnType("RAW(16)");

                // Default values
                entity.Property(e => e.IsActive)
                    .HasDefaultValue(true);

                // Performance indexes for session queries
                entity.HasIndex(e => e.UserId)
                    .HasDatabaseName("IX_UserSession_UserId");
                    
                entity.HasIndex(e => e.LastActivityAt)
                    .HasDatabaseName("IX_UserSession_LastActivityAt");
                    
                entity.HasIndex(e => e.ExpiresAt)
                    .HasDatabaseName("IX_UserSession_ExpiresAt")
                    .HasFilter("ExpiresAt IS NOT NULL");
                    
                entity.HasIndex(e => new { e.UserId, e.IsActive })
                    .HasDatabaseName("IX_UserSession_UserId_IsActive");
                    
                entity.HasIndex(e => e.TenantId)
                    .HasDatabaseName("IX_UserSession_TenantId");
                    
                entity.HasIndex(e => new { e.TenantId, e.IsActive })
                    .HasDatabaseName("IX_UserSession_TenantId_IsActive");

                // Relationship to ApplicationUser
                entity.HasOne(e => e.User)
                    .WithMany(u => u.Sessions)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Table name for Oracle
                entity.ToTable("UserSessions");
            });
        }
    }
}