﻿// <auto-generated />
using System;
using IdentityManager.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Oracle.EntityFrameworkCore.Metadata;

#nullable disable

namespace IdentityManager.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250830203152_SecurityEnhancement_ConsolidatedAuditAndSessions")]
    partial class SecurityEnhancement_ConsolidatedAuditAndSessions
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            OracleModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("IdentityManager.Models.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("NVARCHAR2(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("NUMBER(10)");

                    b.Property<int>("AccountStatus")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(10)")
                        .HasDefaultValue(1);

                    b.Property<int>("ActiveSessionCount")
                        .HasColumnType("NUMBER(10)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("NVARCHAR2(2000)");

                    b.Property<int>("ConsecutiveFailedLogins")
                        .HasColumnType("NUMBER(10)");

                    b.Property<string>("Country")
                        .HasMaxLength(100)
                        .IsUnicode(true)
                        .HasColumnType("NVARCHAR2(100)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TIMESTAMP(6)")
                        .HasDefaultValueSql("SYS_EXTRACT_UTC(SYSTIMESTAMP)");

                    b.Property<int>("DataRetentionDays")
                        .HasColumnType("NUMBER(10)");

                    b.Property<DateTime?>("DeletionRequestedAt")
                        .HasColumnType("TIMESTAMP(6)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("NUMBER(1)");

                    b.Property<int>("FailedLoginAttempts")
                        .HasColumnType("NUMBER(10)");

                    b.Property<bool>("HasAcceptedPrivacyPolicy")
                        .HasColumnType("NUMBER(1)");

                    b.Property<bool>("HasAcceptedTermsOfService")
                        .HasColumnType("NUMBER(1)");

                    b.Property<bool>("IsEUCitizen")
                        .HasColumnType("NUMBER(1)");

                    b.Property<bool>("IsMarkedForDeletion")
                        .HasColumnType("NUMBER(1)");

                    b.Property<bool>("IsTenantAdmin")
                        .HasColumnType("NUMBER(1)");

                    b.Property<DateTime?>("LastActivityAt")
                        .HasColumnType("TIMESTAMP(7)");

                    b.Property<DateTime?>("LastDataExportRequestAt")
                        .HasColumnType("TIMESTAMP(6)");

                    b.Property<DateTime?>("LastFailedLoginResetAt")
                        .HasColumnType("TIMESTAMP(7)");

                    b.Property<string>("LastIpAddressHash")
                        .HasMaxLength(128)
                        .HasColumnType("NVARCHAR2(128)");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("TIMESTAMP(6)");

                    b.Property<string>("LastLoginIPHash")
                        .HasMaxLength(128)
                        .IsUnicode(false)
                        .HasColumnType("NVARCHAR2(128)");

                    b.Property<string>("LastLoginUserAgent")
                        .HasMaxLength(1000)
                        .IsUnicode(true)
                        .HasColumnType("NVARCHAR2(1000)");

                    b.Property<int>("LawfulBasisForProcessing")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(10)")
                        .HasDefaultValue(1);

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("NUMBER(1)");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("TIMESTAMP(7) WITH TIME ZONE");

                    b.Property<int>("MaxConcurrentSessions")
                        .HasColumnType("NUMBER(10)");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("NVARCHAR2(2000)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("NVARCHAR2(2000)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("NUMBER(1)");

                    b.Property<DateTime?>("PrivacyPolicyAcceptedAt")
                        .HasColumnType("TIMESTAMP(6)");

                    b.Property<string>("PrivacyPolicyVersion")
                        .HasMaxLength(50)
                        .IsUnicode(true)
                        .HasColumnType("NVARCHAR2(50)");

                    b.Property<DateTime?>("ScheduledDeletionDate")
                        .HasColumnType("TIMESTAMP(6)");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("NVARCHAR2(2000)");

                    b.Property<DateTime?>("SuspendedAt")
                        .HasColumnType("TIMESTAMP(6)");

                    b.Property<string>("SuspensionReason")
                        .HasMaxLength(500)
                        .IsUnicode(true)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("RAW(16)");

                    b.Property<DateTime?>("TenantJoinedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TIMESTAMP(6)")
                        .HasDefaultValueSql("SYS_EXTRACT_UTC(SYSTIMESTAMP)");

                    b.Property<DateTime?>("TermsOfServiceAcceptedAt")
                        .HasColumnType("TIMESTAMP(6)");

                    b.Property<string>("TermsOfServiceVersion")
                        .HasMaxLength(50)
                        .IsUnicode(true)
                        .HasColumnType("NVARCHAR2(50)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("NUMBER(1)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TIMESTAMP(6)");

                    b.Property<string>("UserName")
                        .HasMaxLength(128)
                        .HasColumnType("NVARCHAR2(128)");

                    b.HasKey("Id");

                    b.HasIndex("AccountStatus")
                        .HasDatabaseName("IX_ApplicationUser_AccountStatus");

                    b.HasIndex("IsEUCitizen")
                        .HasDatabaseName("IX_ApplicationUser_IsEUCitizen");

                    b.HasIndex("IsMarkedForDeletion")
                        .HasDatabaseName("IX_ApplicationUser_IsMarkedForDeletion");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("\"NormalizedUserName\" IS NOT NULL");

                    b.HasIndex("ScheduledDeletionDate")
                        .HasDatabaseName("IX_ApplicationUser_ScheduledDeletionDate")
                        .HasFilter("ScheduledDeletionDate IS NOT NULL");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("IX_ApplicationUser_TenantId");

                    b.HasIndex("Country", "IsEUCitizen")
                        .HasDatabaseName("IX_ApplicationUser_Country_IsEUCitizen");

                    b.HasIndex("TenantId", "AccountStatus")
                        .HasDatabaseName("IX_ApplicationUser_TenantId_Status");

                    b.HasIndex("TenantId", "Email")
                        .HasDatabaseName("IX_ApplicationUser_TenantId_Email");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("IdentityManager.Models.DataProcessingRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)")
                        .HasDefaultValueSql("SYS_GUID()");

                    b.Property<int>("Activity")
                        .HasColumnType("NUMBER(10)");

                    b.Property<string>("DataCategories")
                        .HasMaxLength(500)
                        .IsUnicode(true)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<string>("DataDescription")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .IsUnicode(true)
                        .HasColumnType("NCLOB");

                    b.Property<string>("DataHash")
                        .HasMaxLength(128)
                        .IsUnicode(false)
                        .HasColumnType("NVARCHAR2(128)");

                    b.Property<int>("LawfulBasis")
                        .HasColumnType("NUMBER(10)");

                    b.Property<DateTime>("ProcessingDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TIMESTAMP(6)")
                        .HasDefaultValueSql("SYS_EXTRACT_UTC(SYSTIMESTAMP)");

                    b.Property<string>("ProcessingMetadata")
                        .HasMaxLength(2000)
                        .IsUnicode(true)
                        .HasColumnType("NCLOB");

                    b.Property<string>("ProcessingPurpose")
                        .IsRequired()
                        .HasMaxLength(500)
                        .IsUnicode(true)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<string>("ProcessingSystem")
                        .IsRequired()
                        .HasMaxLength(200)
                        .IsUnicode(true)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<Guid?>("RelatedConsentId")
                        .HasColumnType("RAW(16)");

                    b.Property<int>("RetentionDays")
                        .HasColumnType("NUMBER(10)");

                    b.Property<string>("ThirdPartiesInvolved")
                        .HasMaxLength(500)
                        .IsUnicode(true)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("NVARCHAR2(450)");

                    b.HasKey("Id");

                    b.HasIndex("Activity")
                        .HasDatabaseName("IX_DataProcessingRecord_Activity");

                    b.HasIndex("LawfulBasis")
                        .HasDatabaseName("IX_DataProcessingRecord_LawfulBasis");

                    b.HasIndex("ProcessingDate")
                        .HasDatabaseName("IX_DataProcessingRecord_ProcessingDate");

                    b.HasIndex("RelatedConsentId");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_DataProcessingRecord_UserId");

                    b.HasIndex("UserId", "Activity", "ProcessingDate")
                        .HasDatabaseName("IX_DataProcessingRecord_UserId_Activity_Date");

                    b.ToTable("DataProcessingRecords", (string)null);
                });

            modelBuilder.Entity("IdentityManager.Models.SecurityAuditLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)")
                        .HasDefaultValueSql("SYS_GUID()");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)");

                    b.Property<DateTime>("EventTimestamp")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TIMESTAMP(6)")
                        .HasDefaultValueSql("SYS_EXTRACT_UTC(SYSTIMESTAMP)");

                    b.Property<string>("EventType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .IsUnicode(false)
                        .HasColumnType("NVARCHAR2(100)");

                    b.Property<string>("FailureReason")
                        .HasMaxLength(500)
                        .IsUnicode(true)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<string>("IpAddressHash")
                        .HasMaxLength(128)
                        .IsUnicode(false)
                        .HasColumnType("NVARCHAR2(128)");

                    b.Property<int>("RiskScore")
                        .HasColumnType("NUMBER(10)");

                    b.Property<string>("SecurityMetadata")
                        .HasMaxLength(2000)
                        .IsUnicode(true)
                        .HasColumnType("NCLOB");

                    b.Property<string>("SessionId")
                        .HasMaxLength(128)
                        .IsUnicode(false)
                        .HasColumnType("NVARCHAR2(128)");

                    b.Property<bool>("Success")
                        .HasColumnType("NUMBER(1)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(1000)
                        .IsUnicode(true)
                        .HasColumnType("NVARCHAR2(1000)");

                    b.Property<string>("UserId")
                        .HasMaxLength(450)
                        .HasColumnType("NVARCHAR2(450)");

                    b.HasKey("Id");

                    b.HasIndex("EventTimestamp")
                        .HasDatabaseName("IX_SecurityAuditLog_EventTimestamp");

                    b.HasIndex("EventType")
                        .HasDatabaseName("IX_SecurityAuditLog_EventType");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("IX_SecurityAuditLog_TenantId");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_SecurityAuditLog_UserId");

                    b.HasIndex("EventType", "Success")
                        .HasDatabaseName("IX_SecurityAuditLog_EventType_Success");

                    b.HasIndex("TenantId", "EventType")
                        .HasDatabaseName("IX_SecurityAuditLog_TenantId_EventType");

                    b.ToTable("SecurityAuditLogs", (string)null);
                });

            modelBuilder.Entity("IdentityManager.Models.UserConsent", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)")
                        .HasDefaultValueSql("SYS_GUID()");

                    b.Property<DateTime>("ConsentDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TIMESTAMP(6)")
                        .HasDefaultValueSql("SYS_EXTRACT_UTC(SYSTIMESTAMP)");

                    b.Property<string>("ConsentSource")
                        .HasMaxLength(100)
                        .IsUnicode(true)
                        .HasColumnType("NVARCHAR2(100)");

                    b.Property<int>("ConsentType")
                        .HasColumnType("NUMBER(10)");

                    b.Property<DateTime?>("ExpirationDate")
                        .HasColumnType("TIMESTAMP(6)");

                    b.Property<string>("IpAddressHash")
                        .HasMaxLength(128)
                        .IsUnicode(false)
                        .HasColumnType("NVARCHAR2(128)");

                    b.Property<string>("Metadata")
                        .HasMaxLength(1000)
                        .IsUnicode(true)
                        .HasColumnType("NCLOB");

                    b.Property<string>("PolicyVersion")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("NVARCHAR2(50)");

                    b.Property<int>("Status")
                        .HasColumnType("NUMBER(10)");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .IsUnicode(true)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("NVARCHAR2(450)");

                    b.Property<string>("WithdrawalReason")
                        .HasMaxLength(500)
                        .IsUnicode(true)
                        .HasColumnType("NVARCHAR2(500)");

                    b.HasKey("Id");

                    b.HasIndex("ConsentDate")
                        .HasDatabaseName("IX_UserConsent_ConsentDate");

                    b.HasIndex("ExpirationDate")
                        .HasDatabaseName("IX_UserConsent_ExpirationDate")
                        .HasFilter("ExpirationDate IS NOT NULL");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_UserConsent_Status");

                    b.HasIndex("UserId", "ConsentType")
                        .IsUnique()
                        .HasDatabaseName("IX_UserConsent_UserId_ConsentType");

                    b.ToTable("UserConsents", (string)null);
                });

            modelBuilder.Entity("IdentityManager.Models.UserPrivacySettings", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)")
                        .HasDefaultValueSql("SYS_GUID()");

                    b.Property<bool>("AllowAIProcessing")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false);

                    b.Property<bool>("AllowAnalytics")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false);

                    b.Property<bool>("AllowCrossBorderTransfer")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false);

                    b.Property<bool>("AllowMarketing")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false);

                    b.Property<bool>("AllowThirdPartySharing")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false);

                    b.Property<DateTime>("LastUpdated")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TIMESTAMP(6)")
                        .HasDefaultValueSql("SYS_EXTRACT_UTC(SYSTIMESTAMP)");

                    b.Property<string>("NotificationFrequency")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("NVARCHAR2(50)")
                        .HasDefaultValue("Immediate");

                    b.Property<int>("PreferredDataRetentionDays")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(10)")
                        .HasDefaultValue(2555);

                    b.Property<string>("PreferredLanguage")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("NVARCHAR2(10)")
                        .HasDefaultValue("en-US");

                    b.Property<string>("SettingsVersion")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("NVARCHAR2(50)")
                        .HasDefaultValue("1.0");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("NVARCHAR2(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .IsUnique()
                        .HasDatabaseName("IX_UserPrivacySettings_UserId");

                    b.ToTable("UserPrivacySettings", (string)null);
                });

            modelBuilder.Entity("IdentityManager.Models.UserSession", b =>
                {
                    b.Property<string>("SessionId")
                        .HasMaxLength(128)
                        .IsUnicode(false)
                        .HasColumnType("NVARCHAR2(128)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TIMESTAMP(6)")
                        .HasDefaultValueSql("SYS_EXTRACT_UTC(SYSTIMESTAMP)");

                    b.Property<string>("DeviceInfo")
                        .HasMaxLength(500)
                        .IsUnicode(true)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("TIMESTAMP(6)");

                    b.Property<string>("IpAddressHash")
                        .HasMaxLength(128)
                        .IsUnicode(false)
                        .HasColumnType("NVARCHAR2(128)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(true);

                    b.Property<DateTime>("LastActivityAt")
                        .HasColumnType("TIMESTAMP(6)");

                    b.Property<string>("Location")
                        .HasMaxLength(200)
                        .IsUnicode(true)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<string>("RefreshTokenHash")
                        .HasMaxLength(128)
                        .IsUnicode(false)
                        .HasColumnType("NVARCHAR2(128)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("RAW(16)");

                    b.Property<DateTime?>("TerminatedAt")
                        .HasColumnType("TIMESTAMP(7)");

                    b.Property<string>("TerminationReason")
                        .HasMaxLength(100)
                        .HasColumnType("NVARCHAR2(100)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("NVARCHAR2(450)");

                    b.HasKey("SessionId");

                    b.HasIndex("ExpiresAt")
                        .HasDatabaseName("IX_UserSession_ExpiresAt")
                        .HasFilter("ExpiresAt IS NOT NULL");

                    b.HasIndex("LastActivityAt")
                        .HasDatabaseName("IX_UserSession_LastActivityAt");

                    b.HasIndex("TenantId")
                        .HasDatabaseName("IX_UserSession_TenantId");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_UserSession_UserId");

                    b.HasIndex("TenantId", "IsActive")
                        .HasDatabaseName("IX_UserSession_TenantId_IsActive");

                    b.HasIndex("UserId", "IsActive")
                        .HasDatabaseName("IX_UserSession_UserId_IsActive");

                    b.ToTable("UserSessions", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.DataProtection.EntityFrameworkCore.DataProtectionKey", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(10)");

                    OraclePropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("FriendlyName")
                        .HasMaxLength(255)
                        .HasColumnType("NVARCHAR2(255)");

                    b.Property<string>("Xml")
                        .HasColumnType("CLOB");

                    b.HasKey("Id");

                    b.ToTable("DataProtectionKeys");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("NVARCHAR2(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("NVARCHAR2(2000)");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("NVARCHAR2(128)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("\"NormalizedName\" IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(10)");

                    OraclePropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("NVARCHAR2(2000)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("NVARCHAR2(2000)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("NVARCHAR2(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(10)");

                    OraclePropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("NVARCHAR2(2000)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("NVARCHAR2(2000)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("NVARCHAR2(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("NVARCHAR2(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("NVARCHAR2(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("NVARCHAR2(2000)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("NVARCHAR2(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("NVARCHAR2(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("NVARCHAR2(450)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("NVARCHAR2(450)");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("NVARCHAR2(450)");

                    b.Property<string>("Name")
                        .HasColumnType("NVARCHAR2(450)");

                    b.Property<string>("Value")
                        .HasColumnType("NVARCHAR2(2000)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("IdentityManager.Models.DataProcessingRecord", b =>
                {
                    b.HasOne("IdentityManager.Models.UserConsent", "RelatedConsent")
                        .WithMany()
                        .HasForeignKey("RelatedConsentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("IdentityManager.Models.ApplicationUser", "User")
                        .WithMany("DataProcessingRecords")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RelatedConsent");

                    b.Navigation("User");
                });

            modelBuilder.Entity("IdentityManager.Models.SecurityAuditLog", b =>
                {
                    b.HasOne("IdentityManager.Models.ApplicationUser", "User")
                        .WithMany("SecurityAuditLogs")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("User");
                });

            modelBuilder.Entity("IdentityManager.Models.UserConsent", b =>
                {
                    b.HasOne("IdentityManager.Models.ApplicationUser", "User")
                        .WithMany("UserConsents")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("IdentityManager.Models.UserPrivacySettings", b =>
                {
                    b.HasOne("IdentityManager.Models.ApplicationUser", "User")
                        .WithOne("PrivacySettings")
                        .HasForeignKey("IdentityManager.Models.UserPrivacySettings", "UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("IdentityManager.Models.UserSession", b =>
                {
                    b.HasOne("IdentityManager.Models.ApplicationUser", "User")
                        .WithMany("Sessions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("IdentityManager.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("IdentityManager.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("IdentityManager.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("IdentityManager.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("IdentityManager.Models.ApplicationUser", b =>
                {
                    b.Navigation("DataProcessingRecords");

                    b.Navigation("PrivacySettings");

                    b.Navigation("SecurityAuditLogs");

                    b.Navigation("Sessions");

                    b.Navigation("UserConsents");
                });
#pragma warning restore 612, 618
        }
    }
}
