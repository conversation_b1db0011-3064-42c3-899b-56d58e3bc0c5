using IdentityManager.Services.Interfaces;
using IdentityManager.Models;

namespace IdentityManager.Services.Interfaces;

/// <summary>
/// Interface for tenant management operations
/// Provides methods for tenant identification and management in multi-tenant environment
/// </summary>
public interface ITenantService
{
    /// <summary>
    /// Gets the default tenant ID for new resource allocations
    /// </summary>
    /// <returns>The default tenant ID configured for the system</returns>
    Task<string> GetDefaultTenantIdAsync();

    /// <summary>
    /// Gets the tenant ID for a specific user
    /// </summary>
    /// <param name="userId">The user ID to get tenant for</param>
    /// <returns>The tenant ID associated with the user, or default if none specified</returns>
    Task<string> GetUserTenantIdAsync(string userId);

    /// <summary>
    /// Validates if a tenant ID exists and is active
    /// </summary>
    /// <param name="tenantId">The tenant ID to validate</param>
    /// <returns>True if tenant is valid and active, false otherwise</returns>
    Task<bool> ValidateTenantAsync(string tenantId);

    /// <summary>
    /// Gets tenant information by ID
    /// </summary>
    /// <param name="tenantId">The tenant ID to get information for</param>
    /// <returns>Tenant information or null if not found</returns>
    Task<IdentityManager.Models.TenantInfo?> GetTenantInfoAsync(string tenantId);
}