using System.ComponentModel.DataAnnotations;

namespace IdentityManager.Models;

/// <summary>
/// Request model for 2FA verification
/// </summary>
public class TwoFactorVerifyRequest
{
    [Required]
    [StringLength(6, MinimumLength = 6)]
    [RegularExpression(@"^\d{6}$", ErrorMessage = "Code must be 6 digits")]
    public required string Code { get; set; }
}

/// <summary>
/// Request model for disabling 2FA
/// </summary>
public class TwoFactorDisableRequest
{
    [Required]
    [DataType(DataType.Password)]
    public required string Password { get; set; }
}

/// <summary>
/// Request model for regenerating recovery codes
/// </summary>
public class TwoFactorRegenerateRecoveryCodesRequest
{
    [Required]
    [DataType(DataType.Password)]
    public required string Password { get; set; }
}

/// <summary>
/// Request model for recovery code login
/// </summary>
public class RecoveryCodeLoginRequest
{
    /// <summary>
    /// Email address associated with the user account (required)
    /// </summary>
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email address format")]
    public required string Email { get; set; }

    [Required]
    public required string Password { get; set; }

    [Required]
    public required string RecoveryCode { get; set; }


    /// <summary>
    /// Optional: Client application name (e.g. "BlendaApp", "BlendaWeb", "BlendaDT")
    /// </summary>
    public string? ClientApp { get; set; }

    /// <summary>
    /// Optional: Source of the request (e.g. "BlendaTechnology", "DT", "B2B")
    /// </summary>
    public string? Source { get; set; }    
}

/// <summary>
/// Request model for custom password reset
/// </summary>
public class CustomResetPasswordRequest
{
    [Required]
    public required string UserId { get; set; }

    [Required]
    public required string ResetCode { get; set; }

    [Required]
    public required string NewPassword { get; set; }
}

/// <summary>
/// Enhanced login request with 2FA support
/// </summary>
public class EnhancedLoginRequest
{
    [Required]
    [EmailAddress]
    public required string Email { get; set; }

    [Required]
    [DataType(DataType.Password)]
    public required string Password { get; set; }

    [StringLength(6, MinimumLength = 6)]
    [RegularExpression(@"^\d{6}$", ErrorMessage = "Two-factor code must be 6 digits")]
    public string? TwoFactorCode { get; set; }

    public bool RememberMe { get; set; } = false;

     /// <summary>
    /// Optional: Source of the registration (e.g. "BlendaTechnology", "DT", "B2B")
    /// </summary>
    public string? Source { get; set; }

    /// <summary>
    /// Optional: Client application name (e.g. "BlendaApp", "BlendaWeb", "BlendaDT")
    /// </summary>
    public string? ClientApp { get; set; }


}

/// <summary>
/// Response model for 2FA setup
/// </summary>
public class TwoFactorSetupResponse
{
    public required string AuthenticatorKey { get; set; }
    public required string QrCodeBase64 { get; set; }
    public required string QrCodeUri { get; set; }
    public string[]? BackupCodes { get; set; }
}

/// <summary>
/// Response model for 2FA verification
/// </summary>
public class TwoFactorVerifyResponse
{
    public bool Success { get; set; }
    public required string[] RecoveryCodes { get; set; }
    public required string Message { get; set; }
}

/// <summary>
/// Response model for 2FA status
/// </summary>
public class TwoFactorStatusResponse
{
    public bool IsEnabled { get; set; }
    public int RecoveryCodesCount { get; set; }
    public DateTime? LastEnabledDate { get; set; }
    public bool HasAuthenticatorKey { get; set; }
}

/// <summary>
/// Response model for recovery codes
/// </summary>
public class RecoveryCodesResponse
{
    public required string[] RecoveryCodes { get; set; }
    public required string Message { get; set; }
}

/// <summary>
/// Response model for two-factor requirement during login
/// </summary>
public class TwoFactorRequiredResponse
{
    public bool RequiresTwoFactor { get; set; } = true;
    public required string Message { get; set; }
}

/// <summary>
/// Enhanced login response with comprehensive user information
/// </summary>
public class EnhancedLoginResponse
{
    public required string AccessToken { get; set; }
    public required string RefreshToken { get; set; }
    public int ExpiresIn { get; set; }
    public required UserInfo User { get; set; }
}

/// <summary>
/// User information included in login response
/// </summary>
public class UserInfo
{
    public required string Id { get; set; }
    public required string Email { get; set; }
    public required string[] Roles { get; set; }
    public bool TwoFactorEnabled { get; set; }
}

/// <summary>
/// Generic success response
/// </summary>
public class TwoFactorSuccessResponse
{
    public bool Success { get; set; } = true;
    public required string Message { get; set; }
}
