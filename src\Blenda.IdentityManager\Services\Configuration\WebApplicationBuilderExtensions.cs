using IdentityManager.Data;
using IdentityManager.Models;
using IdentityManager.Services;
using IdentityManager.Services.Handlers;
using IdentityManager.Services.Core;
using IdentityManager.Services.Interfaces;
using IdentityManager.Security;
using Blenda.IdentityManager.Services;
using Blenda.Shared.Messaging.Extensions;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.DataProtection.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.IdentityModel.Logging;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Threading.RateLimiting;
using DotNetEnv;
using Microsoft.AspNetCore.DataProtection;

namespace IdentityManager.Services.Configuration;

/// <summary>
/// Extension methods for WebApplicationBuilder to organize configuration
/// </summary>
public static class WebApplicationBuilderExtensions
{
    /// <summary>
    /// Configure environment-specific settings and load environment variables
    /// </summary>
    public static WebApplicationBuilder ConfigureEnvironment(this WebApplicationBuilder builder)
    {
        // SECURITY: Load .env file in development for local configuration
        if (builder.Environment.IsDevelopment())
        {
            var envFile = Path.Combine(builder.Environment.ContentRootPath, ".env");
            if (File.Exists(envFile))
            {
                Env.Load(envFile);
                Console.WriteLine("Successfully loaded .env file for development");
            }
            else
            {
                Console.WriteLine("Warning: .env file not found in development environment");
            }
        }

        // SECURITY: Configure URLs for development - use environment variable for production
        if (builder.Environment.IsDevelopment())
        {
            // SECURITY: Configure both HTTP and HTTPS for development
            builder.WebHost.UseUrls("http://localhost:5160", "https://localhost:7160");
        }

        // SECURITY: Disable PII logging in production
        if (builder.Environment.IsDevelopment())
        {
            IdentityModelEventSource.ShowPII = true;
            IdentityModelEventSource.Logger.LogLevel = System.Diagnostics.Tracing.EventLevel.Informational;
        }

        // SECURITY: Set base path and load environment variables FIRST
        builder.Configuration.SetBasePath(AppContext.BaseDirectory);
        builder.Configuration.AddEnvironmentVariables(); // CRITICAL: Load environment variables

        return builder;
    }

    /// <summary>
    /// Configure Kestrel server with security limits
    /// </summary>
    public static WebApplicationBuilder ConfigureKestrel(this WebApplicationBuilder builder)
    {
        // SECURITY: Configure Kestrel with security limits
        builder.WebHost.ConfigureKestrel(opt => 
        {
            opt.Limits.MaxRequestBodySize = 10 * 1024 * 1024; // 10MB limit for security
            opt.Limits.RequestHeadersTimeout = TimeSpan.FromMinutes(1);
            opt.Limits.KeepAliveTimeout = TimeSpan.FromMinutes(2);
        });

        return builder;
    }

    /// <summary>
    /// Configure comprehensive logging with security filters
    /// </summary>
    public static WebApplicationBuilder ConfigureLogging(this WebApplicationBuilder builder)
    {
        builder.Services.AddLogging(logging =>
        {
            logging.AddConsole();
            logging.AddDebug();
            
            // SECURITY: Set appropriate log levels for production
            if (builder.Environment.IsProduction())
            {
                logging.SetMinimumLevel(LogLevel.Warning);
            }
            else
            {
                logging.SetMinimumLevel(LogLevel.Information);
            }
            
            // SECURITY: Filter sensitive Microsoft logging
            logging.AddFilter("Microsoft.EntityFrameworkCore.Database.Command", LogLevel.Warning);
            logging.AddFilter("Microsoft.IdentityModel", LogLevel.Warning);
            logging.AddFilter("Microsoft.AspNetCore.Authentication", LogLevel.Information);
        });

        return builder;
    }

    /// <summary>
    /// Configure database contexts with secure connection strings
    /// </summary>
    public static WebApplicationBuilder ConfigureDatabase(this WebApplicationBuilder builder)
    {
        var configuration = builder.Configuration;

        // SECURITY: Secure connection string loading with comprehensive error handling
        string connectionStringIdentity;
        string connectionStringGRP;

        try 
        {
            // CRITICAL FIX: Load from environment variables first, fallback to configuration
            connectionStringIdentity = Environment.GetEnvironmentVariable("ORACLE_CONNECTION_IDENTITY")
                ?? configuration.GetConnectionString("OracleConnectionIdentity")
                ?? throw new InvalidOperationException("Connection string 'OracleConnectionIdentity' not found in environment variables or configuration.");

            connectionStringGRP = Environment.GetEnvironmentVariable("ORACLE_CONNECTION_GRP")
                ?? configuration.GetConnectionString("OracleConnectionGRP")
                ?? throw new InvalidOperationException("Connection string 'OracleConnectionGRP' not found in environment variables or configuration.");
            
            // SECURITY: Validate connection strings are not empty
            if (string.IsNullOrWhiteSpace(connectionStringIdentity) || string.IsNullOrWhiteSpace(connectionStringGRP))
            {
                throw new InvalidOperationException("Connection strings cannot be empty or whitespace.");
            }
        }
        catch (Exception ex)
        {
            // SECURITY: Log error without exposing connection string details
            var logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<Program>();
            logger.LogCritical(ex, "Failed to load database connection strings. Application cannot start.");
            throw new InvalidOperationException("Database configuration error. Check environment variables and configuration.");
        }

        // SECURITY: Database context configuration with comprehensive error handling
        try
        {
            builder.Services.AddDbContext<ApplicationDbContext>(options => 
            {
                options.UseOracle(connectionStringIdentity, oracleOptions =>
                {
                    oracleOptions.UseOracleSQLCompatibility(OracleSQLCompatibility.DatabaseVersion19);
                    oracleOptions.CommandTimeout(300); // 5 minute timeout for long operations
                });
                
                // ORACLE: Configure execution strategy for connection resilience
                options.ReplaceService<IExecutionStrategy, OracleRetryingExecutionStrategy>();
                
                // SECURITY: Enable sensitive data logging only in development
                if (builder.Environment.IsDevelopment())
                {
                    options.EnableSensitiveDataLogging();
                }
                
                options.EnableServiceProviderCaching();
                options.EnableDetailedErrors(builder.Environment.IsDevelopment());
            });
        }
        catch (Exception ex)
        {
            var logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<Program>();
            logger.LogCritical(ex, "Failed to configure database contexts");
            throw new InvalidOperationException("Database context configuration failed. Check connection strings and Oracle client configuration.");
        }

        return builder;
    }

    /// <summary>
    /// Configure Swagger documentation with security definitions
    /// </summary>
    public static WebApplicationBuilder ConfigureSwagger(this WebApplicationBuilder builder)
    {
        builder.Services.AddEndpointsApiExplorer();
        builder.Services.AddSwaggerGen(opt =>
        {
            opt.SwaggerDoc("v1", new OpenApiInfo 
            { 
                Title = "Blenda Core Identity & Access Management API", 
                Version = "v1",
                Description = "Enterprise Identity Management API with OAuth 2.0 and JWT support",
                Contact = new OpenApiContact 
                {
                    Name = "Blenda Technology Security Team",
                    Email = "<EMAIL>"
                }
            });
            
            // SECURITY: Comprehensive JWT Bearer authentication scheme
            opt.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Type = SecuritySchemeType.Http,
                Scheme = "bearer",
                BearerFormat = "JWT",
                In = ParameterLocation.Header,
                Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                Name = "Authorization"
            });

            // SECURITY: Global security requirement for all endpoints
            opt.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    Array.Empty<string>()
                }
            });
            
            // SECURITY: Custom schema ID generator to resolve conflicts between duplicate type names
            opt.CustomSchemaIds(type => 
            {
                // Handle ConsentType enum conflicts by including namespace
                if (type.Name == "ConsentType")
                {
                    return type.FullName?.Replace(".", "_") ?? type.Name;
                }
                
                // Handle other potential conflicts by including full namespace
                var conflictTypes = new[] { "RegistrationResponse", "ApiResponse", "ApiErrorResponse" };
                if (conflictTypes.Contains(type.Name))
                {
                    return type.FullName?.Replace(".", "_") ?? type.Name;
                }
                
                return type.Name;
            });
            
            // SECURITY: Hide sensitive endpoints in production
            if (builder.Environment.IsProduction())
            {
                opt.DocumentFilter<ProductionEndpointFilter>();
            }
        });

        return builder;
    }

    /// <summary>
    /// Configure ASP.NET Core Identity with enterprise security policies
    /// </summary>
    public static WebApplicationBuilder ConfigureIdentity(this WebApplicationBuilder builder)
    {
        // SECURITY: Enterprise-grade Identity configuration with comprehensive security policies
        builder.Services.AddIdentity<ApplicationUser, IdentityRole>(options =>
        {
            // SECURITY: Enhanced password policy configuration
            options.Password.RequireDigit = true;
            options.Password.RequireLowercase = true;
            options.Password.RequireNonAlphanumeric = true;
            options.Password.RequireUppercase = true;
            options.Password.RequiredLength = 12; // Increased from 8 for better security
            options.Password.RequiredUniqueChars = 4; // Increased from 1

            // SECURITY: Enhanced lockout protection against brute force attacks
            options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(15); // Increased from 5
            options.Lockout.MaxFailedAccessAttempts = 3; // Decreased from 5 for better security
            options.Lockout.AllowedForNewUsers = true;

            // SECURITY: Strict user validation settings
            options.User.AllowedUserNameCharacters =
                "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
            options.User.RequireUniqueEmail = true;

            // SECURITY: Require email confirmation for enhanced security
            options.SignIn.RequireConfirmedEmail = true;
            options.SignIn.RequireConfirmedAccount = true;

            // SECURITY: Enhanced token lifespan settings
            options.Tokens.EmailConfirmationTokenProvider = TokenOptions.DefaultEmailProvider;
            options.Tokens.PasswordResetTokenProvider = TokenOptions.DefaultEmailProvider;
        })
        .AddDefaultTokenProviders()
        .AddEntityFrameworkStores<ApplicationDbContext>()
        .AddRoles<IdentityRole>();

        // GDPR COMPLIANCE: Register GDPR service for consent and privacy management
        builder.Services.AddScoped<IGDPRComplianceService, GDPRComplianceService>();

        // MULTI-TENANT: Register tenant reference service for GRP integration
        builder.Services.AddScoped<ITenantReferenceService, TenantReferenceService>();
        builder.Services.AddHttpClient<TenantReferenceService>();
        
        return builder;
    }    /// <summary>
    /// Configure JWT authentication with comprehensive security validation
    /// </summary>
    public static WebApplicationBuilder ConfigureAuthentication(this WebApplicationBuilder builder)
    {
        var configuration = builder.Configuration;

        // SECURITY: Configure Bearer Token authentication with secure defaults
        builder.Services.AddAuthentication()
            .AddBearerToken(IdentityConstants.BearerScheme, options =>
            {
                options.BearerTokenExpiration = TimeSpan.FromMinutes(15); // Short-lived tokens
                options.RefreshTokenExpiration = TimeSpan.FromDays(7); // 7-day refresh token
            });

        // SECURITY: Enhanced JWT Authentication with comprehensive validation
        string jwtKey, jwtIssuer, jwtAudience;

        try
            {
                // CRITICAL FIX: Load JWT settings from environment variables first
                jwtKey = Environment.GetEnvironmentVariable("JWT_SECRET_KEY")
                    ?? configuration["Jwt:Key"]
                    ?? throw new InvalidOperationException("JWT signing key not found in environment variables or configuration.");
                
                jwtIssuer = Environment.GetEnvironmentVariable("JWT_ISSUER")
                    ?? configuration["Jwt:Issuer"]
                    ?? "https://identity.blenda.lat";
                
                jwtAudience = Environment.GetEnvironmentVariable("JWT_AUDIENCE")
                    ?? configuration["Jwt:Audience"]
                    ?? "https://identity.blenda.lat";
                
                // SECURITY: Validate JWT key strength
                if (jwtKey.Length < 32)
                {
                    throw new InvalidOperationException("JWT signing key must be at least 32 characters long for security.");
                }
            }
            catch (Exception ex)
            {
                var logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<Program>();
                logger.LogCritical(ex, "Failed to load JWT configuration");
                throw new InvalidOperationException("JWT configuration error. Check environment variables and configuration.");
            }

        builder.Services.AddAuthentication(options =>
        {
            options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
        })
        .AddJwtBearer(options =>
        {
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ValidIssuer = jwtIssuer,
                ValidAudience = jwtAudience,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtKey)),
                ClockSkew = TimeSpan.FromMinutes(1), // Reduced clock skew for enhanced security
                RequireExpirationTime = true,
                RequireSignedTokens = true,
                SaveSigninToken = false // Security: Don't cache tokens
            };
            
            // SECURITY: Enhanced event handling for JWT authentication
            options.Events = new JwtBearerEvents
            {
                OnAuthenticationFailed = context =>
                {
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                    logger.LogWarning("JWT authentication failed: {Error}", context.Exception.Message);
                    return Task.CompletedTask;
                },
                    OnTokenValidated = async context =>
                    {
                        var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                        var userId = context.Principal?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                        var sessionId = context.Principal?.FindFirst("session_id")?.Value;

                        logger.LogInformation("JWT token validated for user: {UserId}, session: {SessionId}", userId, sessionId);

                        if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(sessionId))
                        {
                            logger.LogWarning("Token missing required session or user claims");
                            context.Fail("Invalid token: missing session or user");
                            return;
                        }

                        try
                        {
                            var sessionService = context.HttpContext.RequestServices.GetRequiredService<ISessionService>();
                            var isActive = await sessionService.IsSessionActiveAsync(userId, sessionId);
                            if (!isActive)
                            {
                                logger.LogInformation("Token's session is not active: {SessionId} for user {UserId}", sessionId, userId);
                                context.Fail("Session terminated");
                                return;
                            }
                        }
                        catch (Exception ex)
                        {
                            var config = context.HttpContext.RequestServices.GetRequiredService<IConfiguration>();
                            var failOpen = config.GetValue<bool>("Auth:FailOpenOnSessionStoreDown", false);
                            logger.LogError(ex, "Error while validating session for token");
                            if (!failOpen)
                            {
                                context.Fail("Session validation failed");
                                return;
                            }
                            logger.LogWarning("Fail-open is enabled for session store errors; allowing token");
                        }
                    }
            };
        });

        return builder;
    }

    /// <summary>
    /// Configure authorization policies with role-based and claim-based access
    /// </summary>
    public static WebApplicationBuilder ConfigureAuthorization(this WebApplicationBuilder builder)
    {
        builder.Services.AddAuthorization(options =>
        {
            // MANTENER: Políticas existentes para compatibilidad
            options.AddPolicy("AdminPolicy", policy => 
            {
                policy.RequireRole("Admin", "PlatformAdmin");
                policy.RequireAuthenticatedUser();
                policy.RequireClaim(ClaimTypes.Email);
            });
            
            options.AddPolicy("SuscriptorPolicy", policy => 
            {
                // EVOLUCIÓN: Incluir nuevos roles equivalentes
                policy.RequireRole("Suscriptor", "BusinessAdmin", "PlatformAdmin", "Admin");
                policy.RequireAuthenticatedUser();
            });
            
            options.AddPolicy("IdentityManagementPolicy", policy =>
            {
                policy.RequireRole("Admin", "IdentityManager", "PlatformAdmin");
                policy.RequireClaim("scope", "identity.manage");
            });
            
            options.AddPolicy("ApiAccessPolicy", policy =>
            {
                policy.RequireAuthenticatedUser();
                policy.RequireClaim("scope", "api.access");
            });

            // NUEVAS: Políticas específicas por rol

            // SECURITY: Administrador de Plataforma - Máximo nivel
            options.AddPolicy("PlatformAdminPolicy", policy => 
            {
                policy.RequireRole("PlatformAdmin");
                policy.RequireClaim("scope", "platform.manage");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: Administrador Empresarial - Gestión de tenant
            options.AddPolicy("BusinessAdminPolicy", policy => 
            {
                policy.RequireRole("BusinessAdmin", "PlatformAdmin");
                policy.RequireClaim("tenant", "required");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: Gestor de RRHH - Gestión de personal
            options.AddPolicy("HRManagerPolicy", policy => 
            {
                policy.RequireRole("HRManager", "BusinessAdmin", "PlatformAdmin");
                policy.RequireClaim("scope", "hr.manage");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: Auditor Externo - Solo lectura restringida
            options.AddPolicy("ExternalAuditorPolicy", policy => 
            {
                policy.RequireRole("ExternalAuditor");
                policy.RequireClaim("audit.access", "readonly");
                policy.RequireClaim("tenant", "required");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: Inspector Laboral - Acceso gubernamental
            options.AddPolicy("LaborInspectorPolicy", policy => 
            {
                policy.RequireRole("LaborInspector");
                policy.RequireClaim("government.authority", "labor_directorate");
                policy.RequireClaim("scope", "government.audit");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: Operations - Gestión de operaciones y turnos
            options.AddPolicy("OperationsPolicy", policy => 
            {
                policy.RequireRole("Operations", "HRManager", "BusinessAdmin", "PlatformAdmin");
                policy.RequireClaim("tenant", "required");
                policy.RequireClaim("scope", "operations.manage");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: Supervisor - Supervisión de equipos y empleados
            options.AddPolicy("SupervisorPolicy", policy => 
            {
                policy.RequireRole("Supervisor", "Operations", "HRManager", "BusinessAdmin", "PlatformAdmin");
                policy.RequireClaim("tenant", "required");
                policy.RequireClaim("scope", "employee.supervise");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: Empleado - Acceso personal limitado
            options.AddPolicy("EmployeePolicy", policy => 
            {
                policy.RequireRole("Employee", "Supervisor", "Operations", "HRManager", "BusinessAdmin", "PlatformAdmin");
                policy.RequireClaim("tenant", "required");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: Soporte Técnico - Asistencia limitada
            options.AddPolicy("TechnicalSupportPolicy", policy => 
            {
                policy.RequireRole("TechnicalSupport", "PlatformAdmin");
                policy.RequireClaim("scope", "support.access");
                policy.RequireAuthenticatedUser();
            });

            // NUEVAS: Políticas funcionales combinadas

            // SECURITY: Gestión de usuarios por roles jerárquicos
            options.AddPolicy("UserManagementPolicy", policy => 
            {
                policy.RequireRole("BusinessAdmin", "HRManager", "PlatformAdmin");
                policy.RequireClaim("tenant", "required");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: Acceso a reportes según nivel
            options.AddPolicy("ReportsAccessPolicy", policy => 
            {
                policy.RequireRole("HRManager", "BusinessAdmin", "ExternalAuditor", "LaborInspector", "PlatformAdmin");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: Datos de asistencia con restricciones
            options.AddPolicy("AttendanceDataPolicy", policy => 
            {
                policy.RequireRole("Employee", "Supervisor", "Operations", "HRManager", "BusinessAdmin", "ExternalAuditor", "LaborInspector", "PlatformAdmin");
                policy.RequireClaim("tenant", "required");
                policy.RequireAuthenticatedUser();
            });

            // NUEVAS: Políticas funcionales específicas para módulo de asistencia DT

            // SECURITY: Gestión de turnos - Operations y superiores
            options.AddPolicy("ShiftManagementPolicy", policy => 
            {
                policy.RequireRole("Operations", "HRManager", "BusinessAdmin", "PlatformAdmin");
                policy.RequireClaim("scope", "shifts.manage");
                policy.RequireClaim("tenant", "required");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: Supervisión de equipos - Supervisor y superiores
            options.AddPolicy("TeamSupervisionPolicy", policy => 
            {
                policy.RequireRole("Supervisor", "Operations", "HRManager", "BusinessAdmin", "PlatformAdmin");
                policy.RequireClaim("scope", "team.manage");
                policy.RequireClaim("tenant", "required");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: Planificación operacional - Operations específico
            options.AddPolicy("OperationalPlanningPolicy", policy => 
            {
                policy.RequireRole("Operations", "HRManager", "BusinessAdmin", "PlatformAdmin");
                policy.RequireClaim("scope", "operations.manage");
                policy.RequireClaim("tenant", "required");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: Supervisión de empleados - Supervisor con alcance limitado
            options.AddPolicy("EmployeeSupervisionPolicy", policy => 
            {
                policy.RequireRole("Supervisor", "Operations", "HRManager", "BusinessAdmin", "PlatformAdmin");
                policy.RequireClaim("scope", "employee.supervise");
                policy.RequireClaim("tenant", "required");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: Operaciones críticas del sistema
            options.AddPolicy("SystemOperationsPolicy", policy => 
            {
                policy.RequireRole("PlatformAdmin");
                policy.RequireClaim("scope", "platform.manage");
                policy.RequireAuthenticatedUser();
            });

            // RTM CRITICAL ROLES: Nuevas políticas para roles críticos del RTM

            // SECURITY: Compliance Officer - Gestión de cumplimiento regulatorio
            options.AddPolicy("ComplianceOfficerPolicy", policy =>
            {
                policy.RequireRole("ComplianceOfficer", "PlatformAdmin");
                policy.RequireClaim("scope", "compliance.manage");
                policy.RequireClaim("tenant", "required");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: Legal Counsel - Asesoría legal especializada
            options.AddPolicy("LegalCounselPolicy", policy =>
            {
                policy.RequireRole("LegalCounsel", "ComplianceOfficer", "PlatformAdmin");
                policy.RequireClaim("scope", "legal.interpret");
                policy.RequireClaim("legal.access", "privileged");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: DT Relations Specialist - Gestión de relaciones laborales DT
            options.AddPolicy("DTRelationsSpecialistPolicy", policy =>
            {
                policy.RequireRole("DTRelationsSpecialist", "LegalCounsel", "ComplianceOfficer", "PlatformAdmin");
                policy.RequireClaim("scope", "dt.relations.manage");
                policy.RequireClaim("government.authority", "labor_directorate");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: Technical Lead - Liderazgo técnico y arquitectura
            options.AddPolicy("TechnicalLeadPolicy", policy =>
            {
                policy.RequireRole("TechnicalLead", "PlatformAdmin");
                policy.RequireClaim("scope", "technical.architecture");
                policy.RequireClaim("scope", "team.technical.lead");
                policy.RequireAuthenticatedUser();
            });

            // RTM COMPLIANCE: Políticas funcionales para módulo de asistencia DT

            // SECURITY: Gestión de cumplimiento normativo - Compliance y superiores
            options.AddPolicy("ComplianceManagementPolicy", policy =>
            {
                policy.RequireRole("ComplianceOfficer", "LegalCounsel", "PlatformAdmin");
                policy.RequireClaim("scope", "compliance.manage");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: Gestión de asuntos legales - Legal y Compliance
            options.AddPolicy("LegalAffairsPolicy", policy =>
            {
                policy.RequireRole("LegalCounsel", "ComplianceOfficer", "PlatformAdmin");
                policy.RequireClaim("scope", "legal.interpret");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: Gestión de relaciones DT - Especialista DT y superiores
            options.AddPolicy("DTRelationsManagementPolicy", policy =>
            {
                policy.RequireRole("DTRelationsSpecialist", "LegalCounsel", "ComplianceOfficer", "LaborInspector", "PlatformAdmin");
                policy.RequireClaim("scope", "dt.relations.manage");
                policy.RequireAuthenticatedUser();
            });

            // SECURITY: Liderazgo técnico - Technical Lead y PlatformAdmin
            options.AddPolicy("TechnicalLeadershipPolicy", policy =>
            {
                policy.RequireRole("TechnicalLead", "PlatformAdmin");
                policy.RequireClaim("scope", "technical.architecture");
                policy.RequireAuthenticatedUser();
            });
        });

        return builder;
    }

    /// <summary>
    /// Configure CORS with strict origin validation
    /// </summary>
    public static WebApplicationBuilder ConfigureCors(this WebApplicationBuilder builder)
    {
        var allowedOrigins = new[]
        {
            "http://localhost:5000",            
            "http://localhost:5045",
            "http://localhost:5176",
            "https://localhost:5001",
            "https://localhost:7171",
            "https://localhost:7249",
            "https://blenda.lat",
            "https://api.blenda.lat",
            "https://admin.blenda.lat",
            "https://blog.blenda.lat",
            "https://docs.blenda.lat",
            "https://grp.blenda.lat",
            "https://identity.blenda.lat",
            "https://my.blenda.lat",
            "https://portal.blenda.lat",
            "https://www.blenda.lat"
        };

        builder.Services.AddCors(options =>
        {
            options.AddDefaultPolicy(policy =>
            {
                if (builder.Environment.IsDevelopment())
                {
                    // SECURITY: More permissive CORS for development
                    policy.WithOrigins(allowedOrigins)
                          .AllowAnyMethod()
                          .AllowAnyHeader()
                          .AllowCredentials()
                          .SetPreflightMaxAge(TimeSpan.FromMinutes(10));
                }
                else
                {
                    // SECURITY: Enhanced CORS for production with explicit headers for API endpoints
                    policy.WithOrigins(allowedOrigins)
                          .WithMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH")
                          .WithHeaders("Content-Type", "Authorization", "X-Requested-With", "Accept", "Origin", "Access-Control-Request-Method", "Access-Control-Request-Headers")
                          .AllowCredentials()
                          .SetPreflightMaxAge(TimeSpan.FromHours(1))
                          .WithExposedHeaders("Content-Disposition"); // For file downloads
                }
            });
            
            // SECURITY: Add a restrictive policy for sensitive endpoints
            options.AddPolicy("RestrictivePolicy", policy =>
            {
                policy.WithOrigins("https://identity.blenda.lat", "https://admin.blenda.lat")
                      .WithMethods("GET", "POST")
                      .WithHeaders("Content-Type", "Authorization")
                      .AllowCredentials();
            });
        });

        return builder;
    }

    /// <summary>
    /// Configure all application services including handlers and utilities
    /// </summary>
    public static WebApplicationBuilder ConfigureApplicationServices(this WebApplicationBuilder builder)
    {
        var configuration = builder.Configuration;


        // SECURITY: Configure comprehensive rate limiting with security-focused policies
        builder.Services.AddSecurityRateLimiting(builder.Environment);

        // SECURITY: Configure Data Protection to use database storage for multiple replica deployment
        try
        {
            builder.Services.AddDataProtection()
                .PersistKeysToDbContext<ApplicationDbContext>() // Store keys in Oracle database
                .SetApplicationName("BlendaIdentityManager")
                .SetDefaultKeyLifetime(TimeSpan.FromDays(90)); // 90-day key rotation for security

            Console.WriteLine("Data Protection configured with database storage for multiple replicas");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Data Protection not available (this is expected in Kubernetes): {ex.Message}");
        }

        // SECURITY: JWT-based refresh token protection (eliminates Data Protection key sharing issues)
        builder.Services.AddSingleton<ISecureDataFormat<AuthenticationTicket>, JwtRefreshTokenProtector>();

        // Configure API Settings with secure binding
        builder.Services.Configure<ApiSettings>(options =>
        {
            configuration.GetSection("ApiSettings").Bind(options);
            
            // SECURITY: Validate API settings
            if (string.IsNullOrWhiteSpace(options.PublicFacingService))
            {
                options.PublicFacingService = "https://www.blenda.lat";
            }
            if (string.IsNullOrWhiteSpace(options.InternalService))
            {
                options.InternalService = "https://identity.blenda.lat";
            }
        });

        // Dependency injection for configuration
        builder.Services.AddSingleton<IConfiguration>(configuration);

        // SECURITY: Essential services for identity management
        builder.Services.AddMemoryCache();
        builder.Services.AddHttpContextAccessor();

        // SECURITY: Service implementations with proper DI
        builder.Services.AddScoped<IEmailSender<ApplicationUser>, EmailSender>();
        builder.Services.AddScoped<TwoFactorService<ApplicationUser>>();
        builder.Services.AddScoped<ITwoFactorService<ApplicationUser>>(provider => 
            provider.GetRequiredService<TwoFactorService<ApplicationUser>>());
        builder.Services.AddScoped<CustomSignInManager<ApplicationUser>>();
        
        // SECURITY: Role management and seeding service
        builder.Services.AddScoped<RoleSeederService>();
        
        // SECURITY: Role claims seeding service for Módulo Asistencia DT (Operations and Supervisor roles)
        builder.Services.AddScoped<RoleClaimsSeederService>();

        // SECURITY: Handler services for better separation of concerns
        // Register core authentication services
        builder.Services.AddScoped<ITokenService, IdentityManager.Services.Core.TokenService>();
        builder.Services.AddScoped<ISecurityAuditService, IdentityManager.Services.Core.SecurityAuditService>();
        builder.Services.AddScoped<ISessionService, IdentityManager.Services.Core.SessionService>();
        builder.Services.AddScoped<IUserRegistrationService<ApplicationUser>, UserRegistrationService<ApplicationUser>>();
        
        // Register refactored handlers
        builder.Services.AddScoped<IUserRegistrationHandler<ApplicationUser>, UserRegistrationHandler<ApplicationUser>>();
        builder.Services.AddScoped<EnhancedUserRegistrationHandler>();
        builder.Services.AddScoped<IUserAuthenticationHandler, UserAuthenticationHandler>();
        builder.Services.AddScoped<IUserAccountHandler<ApplicationUser>, UserAccountHandler<ApplicationUser>>();
        builder.Services.AddScoped<ITwoFactorManagementHandler<ApplicationUser>, TwoFactorManagementHandler<ApplicationUser>>();

        // SECURITY: Add security logging services
        builder.Services.AddSecurityLogging();

        // SECURITY: Add messaging services for standardized communication
        builder.Services.AddMessagingServices(options =>
        {
            options.DefaultLocale = Blenda.Shared.Messaging.Constants.SupportedLocale.English;
            options.UseFallbackMessages = true;
            options.LogMissingKeys = builder.Environment.IsDevelopment(); // Log only in development
        });

        // SECURITY: Add comprehensive audit logging service for compliance
        builder.Services.AddScoped<IAuditService, AuditService>();

        // SECURITY: GRP Resource Integration Services
        builder.Services.Configure<GrpApiOptions>(builder.Configuration.GetSection(GrpApiOptions.SectionName));
        builder.Services.AddScoped<ITenantService, TenantService>();
        builder.Services.AddScoped<IGrpResourceService, GrpResourceService>();
        
        // HTTP client for GRP API integration
        builder.Services.AddHttpClient<GrpResourceService>(client =>
        {
            var grpConfig = builder.Configuration.GetSection(GrpApiOptions.SectionName);
            var baseUrl = grpConfig["BaseUrl"] ?? "https://api.blenda.lat";
            var timeoutSeconds = grpConfig.GetValue<int>("TimeoutSeconds", 30);
            
            client.BaseAddress = new Uri(baseUrl);
            client.Timeout = TimeSpan.FromSeconds(timeoutSeconds);
            client.DefaultRequestHeaders.Add("User-Agent", "BlendaIdentityManager/1.0");
            client.DefaultRequestHeaders.Add("Accept", "application/json");
        });

        // SECURITY: Health checks for monitoring with system metrics
        builder.Services.AddHealthChecks()
            .AddDbContextCheck<ApplicationDbContext>("identity-db")
            .AddCheck<ApplicationHealthCheck>("application");

        return builder;
    }
}
