# API Debug - Comprehensive Endpoint Testing and Troubleshooting

## Overview
Systematic debugging approach for ASP.NET Core Identity API endpoints with focus on authentication, GDPR compliance, and audit logging.

## Scope
- Authentication flow validation
- GDPR endpoint functionality
- Rate limiting verification
- Health check monitoring
- Error response analysis

## Instructions

### 1. Environment Validation
```powershell
# Verify application is running
Get-Process -Name dotnet -ErrorAction SilentlyContinue
netstat -an | Select-String ":5160"

# Test basic connectivity
Test-NetConnection -ComputerName localhost -Port 5160
```

### 2. Health Check Validation
```powershell
# Basic health check
$healthResponse = Invoke-WebRequest -Uri "http://localhost:5160/health" -UseBasicParsing
$healthData = $healthResponse.Content | ConvertFrom-Json
Write-Host "API Status: $($healthData.status)"
Write-Host "Memory Usage: $($healthData.checks[1].data.memory_usage_percent)%"
Write-Host "CPU Usage: $($healthData.checks[1].data.cpu_usage_percent)%"
```

### 3. Authentication Testing
```powershell
# Login endpoint test
$loginData = @{
    email = "<EMAIL>"
    password = "YourPassword123!"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-WebRequest -Uri "http://localhost:5160/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"
    Write-Host "Login Status: $($loginResponse.StatusCode)"
    $tokenData = $loginResponse.Content | ConvertFrom-Json
    $token = $tokenData.token
    Write-Host "Token received: $($token.Substring(0,20))..."
} catch {
    Write-Host "Login Error: $($_.Exception.Message)"
}
```

### 4. GDPR Endpoint Testing
```powershell
# Test GDPR endpoints with authentication
$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# Privacy settings
try {
    $privacyResponse = Invoke-WebRequest -Uri "http://localhost:5160/api/gdpr/privacy/settings" -Method GET -Headers $headers
    Write-Host "Privacy Settings Status: $($privacyResponse.StatusCode)"
} catch {
    Write-Host "Privacy Settings Error: $($_.Exception.Message)"
}

# Data export request
try {
    $exportResponse = Invoke-WebRequest -Uri "http://localhost:5160/api/gdpr/data/export" -Method GET -Headers $headers
    Write-Host "Data Export Status: $($exportResponse.StatusCode)"
} catch {
    Write-Host "Data Export Error: $($_.Exception.Message)"
}
```

### 5. Rate Limiting Validation
```powershell
# Test GDPR rate limiting (30 requests per 5 minutes)
$rateLimitTest = @()
for ($i = 1; $i -le 35; $i++) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:5160/api/gdpr/privacy/settings" -Method GET -Headers $headers
        $rateLimitTest += @{
            Request = $i
            Status = $response.StatusCode
            RetryAfter = $response.Headers["Retry-After"]
        }
        Write-Host "Request $i : Status $($response.StatusCode)"
        
        if ($response.StatusCode -eq 429) {
            Write-Host "Rate limit reached at request $i"
            break
        }
    } catch {
        Write-Host "Request $i failed: $($_.Exception.Message)"
    }
    Start-Sleep -Milliseconds 100
}
```

### 6. Error Pattern Analysis
```powershell
# Common error patterns to check
$errorPatterns = @(
    @{ Endpoint = "/health"; ExpectedStatus = 200; Description = "Health check availability" }
    @{ Endpoint = "/api/auth/login"; ExpectedStatus = 200; Description = "Authentication endpoint" }
    @{ Endpoint = "/api/gdpr/privacy/settings"; ExpectedStatus = 200; Description = "GDPR privacy settings" }
    @{ Endpoint = "/nonexistent"; ExpectedStatus = 404; Description = "404 handling" }
)

foreach ($pattern in $errorPatterns) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:5160$($pattern.Endpoint)" -Method GET -Headers $headers -ErrorAction SilentlyContinue
        $status = $response.StatusCode
    } catch {
        $status = $_.Exception.Response.StatusCode.Value__
    }
    
    $result = if ($status -eq $pattern.ExpectedStatus) { "✅ PASS" } else { "❌ FAIL" }
    Write-Host "$result - $($pattern.Description): Expected $($pattern.ExpectedStatus), Got $status"
}
```

## Database Verification
```sql
-- Verify audit logging is working
SELECT TOP 10 
    EventType,
    Success,
    FailureReason,
    SecurityMetadata,
    Timestamp
FROM USER_IDENTITY.SecurityAuditLogs 
WHERE EventType = 'API_REQUEST'
ORDER BY Timestamp DESC;

-- Check authentication events
SELECT TOP 10 
    UserId,
    EventType,
    Success,
    IpAddressHash,
    Timestamp
FROM USER_IDENTITY.SecurityAuditLogs 
WHERE EventType = 'LoginEnhanced'
ORDER BY Timestamp DESC;
```

## Common Issues and Solutions

### Issue: API Not Responding
**Solution**: 
1. Check if process is running: `Get-Process -Name dotnet`
2. Verify port availability: `netstat -an | Select-String ":5160"`
3. Check firewall settings
4. Review application logs for startup errors

### Issue: 401 Unauthorized
**Solution**:
1. Verify JWT token is valid and not expired
2. Check Authorization header format: "Bearer <token>"
3. Validate JWT configuration in appsettings.json/.env
4. Review SecurityAuditLogs for authentication failures

### Issue: 429 Rate Limited
**Solution**:
1. Verify rate limiting policies in RateLimitingExtensions.cs
2. Check if GDPRPolicy is properly configured
3. Wait for rate limit window to reset
4. Review rate limiting middleware registration

### Issue: 500 Internal Server Error
**Solution**:
1. Check application logs for exceptions
2. Verify database connectivity
3. Review SecurityAuditLogs for error patterns
4. Check middleware configuration order

## Performance Monitoring
```powershell
# Monitor API performance
$performanceTest = @()
for ($i = 1; $i -le 10; $i++) {
    $start = Get-Date
    $response = Invoke-WebRequest -Uri "http://localhost:5160/health" -UseBasicParsing
    $end = Get-Date
    $duration = ($end - $start).TotalMilliseconds
    
    $performanceTest += @{
        Request = $i
        Duration = $duration
        Status = $response.StatusCode
    }
}

$avgDuration = ($performanceTest | Measure-Object -Property Duration -Average).Average
Write-Host "Average Response Time: $([math]::Round($avgDuration, 2))ms"
```
