namespace IdentityManager.Models;

/// <summary>
/// Response model for access token authentication
/// </summary>
public class AccessTokenResponse
{
    /// <summary>
    /// The JWT access token
    /// </summary>
    public required string AccessToken { get; set; }

    /// <summary>
    /// The refresh token for obtaining new access tokens
    /// </summary>
    public required string RefreshToken { get; set; }

    /// <summary>
    /// Token expiration time in seconds
    /// </summary>
    public required int ExpiresIn { get; set; }
}
