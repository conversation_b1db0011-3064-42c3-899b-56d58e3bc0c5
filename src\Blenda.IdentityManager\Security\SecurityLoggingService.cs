using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace IdentityManager.Security
{
    /// <summary>
    /// Security logging service for audit trails and compliance
    /// </summary>
    public class SecurityLoggingService
    {
        private readonly ILogger<SecurityLoggingService> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public SecurityLoggingService(
            ILogger<SecurityLoggingService> logger,
            IHttpContextAccessor httpContextAccessor)
        {
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// Log authentication attempts for security monitoring
        /// </summary>
        public void LogAuthenticationAttempt(string email, bool success, string? reason = null)
        {
            var context = _httpContextAccessor.HttpContext;
            var clientIp = GetClientIpAddress();
            var userAgent = context?.Request.Headers["User-Agent"].ToString() ?? "Unknown";

            var logData = new
            {
                EventType = "Authentication",
                Email = email,
                Success = success,
                Reason = reason,
                ClientIP = clientIp,
                UserAgent = userAgent,
                Timestamp = DateTime.UtcNow,
                RequestId = context?.TraceIdentifier
            };

            if (success)
            {
                _logger.LogInformation("Authentication successful: {@AuthData}", logData);
            }
            else
            {
                _logger.LogWarning("Authentication failed: {@AuthData}", logData);
            }
        }

        /// <summary>
        /// Log email confirmation attempts
        /// </summary>
        public void LogEmailConfirmation(string userId, bool success, string? errorDetails = null)
        {
            var logData = new
            {
                EventType = "EmailConfirmation",
                UserId = userId,
                Success = success,
                ErrorDetails = errorDetails,
                ClientIP = GetClientIpAddress(),
                Timestamp = DateTime.UtcNow
            };

            _logger.LogInformation("Email confirmation attempt: {@EmailConfirmData}", logData);
        }

        /// <summary>
        /// Log password reset attempts
        /// </summary>
        public void LogPasswordReset(string email, bool success, string? reason = null)
        {
            var logData = new
            {
                EventType = "PasswordReset",
                Email = email,
                Success = success,
                Reason = reason,
                ClientIP = GetClientIpAddress(),
                Timestamp = DateTime.UtcNow
            };

            _logger.LogWarning("Password reset attempt: {@PasswordResetData}", logData);
        }

        /// <summary>
        /// Log privileged operations for compliance
        /// </summary>
        public void LogPrivilegedOperation(string operation, ClaimsPrincipal user, object? additionalData = null)
        {
            var userEmail = user.FindFirst(ClaimTypes.Email)?.Value ?? "Unknown";
            var userRoles = string.Join(", ", user.FindAll(ClaimTypes.Role).Select(c => c.Value));

            var logData = new
            {
                EventType = "PrivilegedOperation",
                Operation = operation,
                UserEmail = userEmail,
                UserRoles = userRoles,
                AdditionalData = additionalData,
                ClientIP = GetClientIpAddress(),
                Timestamp = DateTime.UtcNow
            };

            _logger.LogWarning("Privileged operation executed: {@PrivilegedOpData}", logData);
        }

        /// <summary>
        /// Log security violations and suspicious activities
        /// </summary>
        public void LogSecurityViolation(string violationType, string description, object? context = null)
        {
            var logData = new
            {
                EventType = "SecurityViolation",
                ViolationType = violationType,
                Description = description,
                Context = context,
                ClientIP = GetClientIpAddress(),
                Timestamp = DateTime.UtcNow
            };

            _logger.LogError("Security violation detected: {@SecurityViolationData}", logData);
        }

        /// <summary>
        /// Get client IP address with proxy support
        /// </summary>
        private string GetClientIpAddress()
        {
            var context = _httpContextAccessor.HttpContext;
            if (context == null) return "Unknown";

            // Check for forwarded IP (behind proxy/load balancer)
            var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                return forwardedFor.Split(',')[0].Trim();
            }

            // Check for real IP header
            var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(realIp))
            {
                return realIp;
            }

            // Fall back to remote IP
            return context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        }
    }

    /// <summary>
    /// Extension methods for registering security services
    /// </summary>
    public static class SecurityServiceExtensions
    {
        /// <summary>
        /// Add security logging services to DI container
        /// </summary>
        public static IServiceCollection AddSecurityLogging(this IServiceCollection services)
        {
            services.AddScoped<SecurityLoggingService>();
            return services;
        }
    }
}
