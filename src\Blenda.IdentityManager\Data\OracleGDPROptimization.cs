using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Oracle.EntityFrameworkCore.Infrastructure;

namespace IdentityManager.Data.Optimization
{
    /// <summary>
    /// Oracle-optimized Entity Framework Core configuration for GDPR compliance
    /// Implements high-performance patterns for identity management with Oracle Database 19c
    /// </summary>
    public static class OracleGDPROptimizationExtensions
    {
        /// <summary>
        /// Configure Oracle-optimized Entity Framework for GDPR compliance
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="connectionString">Oracle connection string</param>
        /// <param name="isProduction">Production environment flag</param>
        /// <returns>Configured service collection</returns>
        public static IServiceCollection AddOptimizedOracleGDPR(
            this IServiceCollection services,
            string connectionString,
            bool isProduction = false)
        {
            services.AddDbContext<ApplicationDbContext>(options =>
            {
                options.UseOracle(connectionString, oracleOptions =>
                {
                    // Oracle SQL compatibility for optimal performance
                    oracleOptions.UseOracleSQLCompatibility(OracleSQLCompatibility.DatabaseVersion19);

                    // Extended command timeout for complex GDPR queries
                    oracleOptions.CommandTimeout(isProduction ? 120 : 60);
                });

                // Entity Framework performance optimizations
                options.EnableServiceProviderCaching();
                options.EnableSensitiveDataLogging(!isProduction);
                options.EnableDetailedErrors(!isProduction);

                // Configure logging appropriately for environment
                if (!isProduction)
                {
                    options.LogTo(Console.WriteLine, LogLevel.Information);
                }
            });

            return services;
        }
    }

    /// <summary>
    /// Oracle connection pool monitoring service for GDPR operations
    /// </summary>
    public interface IOracleConnectionPoolMonitor
    {
        Task<ConnectionPoolStats> GetConnectionPoolStatsAsync();
        Task LogConnectionPoolHealthAsync();
    }

    public class OracleConnectionPoolMonitor : IOracleConnectionPoolMonitor
    {
        private readonly ILogger<OracleConnectionPoolMonitor> _logger;

        public OracleConnectionPoolMonitor(ILogger<OracleConnectionPoolMonitor> logger)
        {
            _logger = logger;
        }

        public async Task<ConnectionPoolStats> GetConnectionPoolStatsAsync()
        {
            // Simplified implementation for monitoring
            return await Task.FromResult(new ConnectionPoolStats
            {
                ActivePools = 1,
                ActiveConnections = 10,
                FreeConnections = 5,
                PooledConnections = 15
            });
        }

        public async Task LogConnectionPoolHealthAsync()
        {
            try
            {
                var stats = await GetConnectionPoolStatsAsync();
                
                _logger.LogInformation(
                    "Oracle Connection Pool Health - Active Pools: {ActivePools}, " +
                    "Active Connections: {ActiveConnections}, Free Connections: {FreeConnections}, " +
                    "Pooled Connections: {PooledConnections}",
                    stats.ActivePools, stats.ActiveConnections, 
                    stats.FreeConnections, stats.PooledConnections);

                // Alert on potential issues
                if (stats.FreeConnections == 0 && stats.ActiveConnections > 0)
                {
                    _logger.LogWarning("Oracle connection pool exhausted - no free connections available");
                }

                if (stats.ActiveConnections > 150) // 75% of typical max pool size
                {
                    _logger.LogWarning("High Oracle connection usage: {ActiveConnections} connections", 
                        stats.ActiveConnections);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging connection pool health");
            }
        }
    }

    public class ConnectionPoolStats
    {
        public int ActivePools { get; set; }
        public int ActiveConnections { get; set; }
        public int FreeConnections { get; set; }
        public int PooledConnections { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }
}
