using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace IdentityManager.Security
{
    /// <summary>
    /// Swagger filter to hide sensitive endpoints in production environment
    /// </summary>
    public class ProductionEndpointFilter : IDocumentFilter
    {
        public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
        {
            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production";
            
            if (environment == "Production")
            {
                // Remove sensitive endpoints from production Swagger documentation
                var sensitiveEndpoints = new[]
                {
                    "/api/SendRegisterEmail",
                    "/swagger",
                    "/swagger/index.html"
                };

                foreach (var endpoint in sensitiveEndpoints)
                {
                    if (swaggerDoc.Paths.ContainsKey(endpoint))
                    {
                        swaggerDoc.Paths.Remove(endpoint);
                    }
                }

                // Add security warning to API documentation
                swaggerDoc.Info.Description += "\n\n⚠️ **SECURITY NOTICE**: This is a production API. Unauthorized access is monitored and logged.";
            }
        }
    }
}
