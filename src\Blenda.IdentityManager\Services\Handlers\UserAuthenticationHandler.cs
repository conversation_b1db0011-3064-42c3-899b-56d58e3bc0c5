using Microsoft.AspNetCore.Identity;
using IdentityManager.Models;
using IdentityManager.Services.Core;
using IdentityManager.Services.Interfaces;
using Microsoft.AspNetCore.Http.HttpResults;
using System.Security.Claims;

using LoginRequest = Microsoft.AspNetCore.Identity.Data.LoginRequest;
using RefreshRequest = Microsoft.AspNetCore.Identity.Data.RefreshRequest;
using AccessTokenResponse = Microsoft.AspNetCore.Authentication.BearerToken.AccessTokenResponse;
using Microsoft.Extensions.Options;

namespace IdentityManager.Services.Handlers;

/// <summary>
/// Interface for user authentication operations
/// </summary>
public interface IUserAuthenticationHandler
{

    /// <summary>
    /// Enhanced login with Two-Factor Authentication support
    /// </summary>
    Task<Results<Ok<object>, ProblemHttpResult>> LoginEnhancedAsync(EnhancedLoginRequest request);

    /// <summary>
    /// Authenticates a user using a recovery code
    /// </summary>
    Task<Results<Ok<AccessTokenResponse>, ProblemHttpResult>> RecoveryCodeLoginAsync(RecoveryCodeLoginRequest request);

    /// <summary>
    /// Refreshes an expired access token using a refresh token
    /// </summary>
    Task<Results<Ok<AccessTokenResponse>, UnauthorizedHttpResult>> RefreshTokenAsync(RefreshRequest request);

    /// <summary>
    /// Signs out the current user
    /// </summary>
    Task<Results<Ok, UnauthorizedHttpResult>> LogoutAsync();
}

/// <summary>
/// Handles user authentication operations with modern, service-oriented architecture
/// </summary>
public class UserAuthenticationHandler : IUserAuthenticationHandler
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly SignInManager<ApplicationUser> _signInManager;
    private readonly ITokenService _tokenService;
    private readonly ISecurityAuditService _securityAuditService;
    private readonly ISessionService _sessionService;
    private readonly ITwoFactorService<ApplicationUser> _twoFactorService;
    private readonly ILogger<UserAuthenticationHandler> _logger;
    private readonly GrpApiOptions _options;

    public UserAuthenticationHandler(
        UserManager<ApplicationUser> userManager,
        SignInManager<ApplicationUser> signInManager,
        ITokenService tokenService,
        ISecurityAuditService securityAuditService,
        ISessionService sessionService,
        ITwoFactorService<ApplicationUser> twoFactorService,
        ILogger<UserAuthenticationHandler> logger,
        IOptions<GrpApiOptions> options)
    {
        _userManager = userManager;
        _signInManager = signInManager;
        _tokenService = tokenService;
        _securityAuditService = securityAuditService;
        _sessionService = sessionService;
        _twoFactorService = twoFactorService;
        _logger = logger;
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
    }

    

    /// <inheritdoc />
    public async Task<Results<Ok<object>, ProblemHttpResult>> LoginEnhancedAsync(EnhancedLoginRequest request)
    {
        try
        {
            _logger.LogInformation("Enhanced login attempt for {Email}", request.Email);

            var registrationPrefix = string.IsNullOrWhiteSpace(request.ClientApp) ? _options.DefaultTenantId.ToString() : request.ClientApp;

            var registrationUniqueEmail = $"{registrationPrefix}+{request.Email}";

            var user = await _userManager.FindByEmailAsync(registrationUniqueEmail);
            if (user == null)
            {
                _logger.LogWarning("Enhanced login failed - user not found: {Email}", request.Email);
                await _securityAuditService.LogSecurityEventAsync(null, request.Email, "LoginEnhanced", false, "User not found");
                return TypedResults.Problem("Invalid login attempt", statusCode: StatusCodes.Status401Unauthorized);
            }

            if (!await _userManager.IsEmailConfirmedAsync(user))
            {
                _logger.LogWarning("Enhanced login failed - email not confirmed: {Email}", request.Email);
                await _securityAuditService.LogSecurityEventAsync(await _userManager.GetUserIdAsync(user), request.Email, "LoginEnhanced", false, "Email not confirmed");
                return TypedResults.Problem("Email not confirmed", statusCode: StatusCodes.Status401Unauthorized);
            }

            var passwordResult = await _signInManager.CheckPasswordSignInAsync(user, request.Password, lockoutOnFailure: false);
            if (!passwordResult.Succeeded)
            {
                _logger.LogWarning("Enhanced login failed for {Email}: {Result}", request.Email, passwordResult);
                
                string failureReason = passwordResult.IsLockedOut ? "Account locked" : 
                                     passwordResult.IsNotAllowed ? "Account not allowed" : 
                                     passwordResult.RequiresTwoFactor ? "Requires 2FA" : "Invalid credentials";
                
                await _securityAuditService.LogSecurityEventAsync(await _userManager.GetUserIdAsync(user), request.Email, "LoginEnhanced", false, failureReason);
                return TypedResults.Problem("Invalid login attempt", statusCode: StatusCodes.Status401Unauthorized);
            }

            var has2FA = await _userManager.GetTwoFactorEnabledAsync(user);
            
            if (has2FA && string.IsNullOrEmpty(request.TwoFactorCode))
            {
                _logger.LogInformation("2FA required for user {Email}", request.Email);
                return TypedResults.Ok((object)new { requiresTwoFactor = true });
            }

            if (has2FA && !string.IsNullOrEmpty(request.TwoFactorCode))
            {
                var isValidToken = await _twoFactorService.ValidateTwoFactorCodeAsync(user, request.TwoFactorCode);
                if (!isValidToken)
                {
                    _logger.LogWarning("Invalid 2FA code for {Email}", request.Email);
                    await _securityAuditService.LogSecurityEventAsync(await _userManager.GetUserIdAsync(user), request.Email, "LoginEnhanced", false, "Invalid 2FA code");
                    return TypedResults.Problem("Invalid two-factor code", statusCode: StatusCodes.Status401Unauthorized);
                }
            }

            var accessTokenResponse = await _tokenService.GenerateTokenResponseAsync(user);
            
            // Create user session
            var sessionId = _tokenService.GenerateSecureSessionId();
            await _sessionService.CreateUserSessionAsync(user, sessionId);
            
            await _securityAuditService.LogSecurityEventAsync(await _userManager.GetUserIdAsync(user), request.Email, "LoginEnhanced", true, null);
            
            _logger.LogInformation("Enhanced login successful for {Email}", request.Email);
            return TypedResults.Ok((object)new { success = true, tokenResponse = accessTokenResponse });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during enhanced login for {Email}", request.Email);
            return TypedResults.Problem("An unexpected error occurred during login", 
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <inheritdoc />
    public async Task<Results<Ok<AccessTokenResponse>, ProblemHttpResult>> RecoveryCodeLoginAsync(RecoveryCodeLoginRequest request)
    {
        try
        {
            _logger.LogInformation("Recovery code login attempt for {Email}", request.Email);
            
            var registrationPrefix = string.IsNullOrWhiteSpace(request.ClientApp) ? _options.DefaultTenantId.ToString() : request.ClientApp;

            var registrationUniqueEmail = $"{registrationPrefix}+{request.Email}";

            var user = await _userManager.FindByEmailAsync(registrationUniqueEmail);
            if (user == null)
            {
                _logger.LogWarning("Recovery code login failed - user not found: {Email}", request.Email);
                await _securityAuditService.LogSecurityEventAsync(null, request.Email, "RecoveryCodeLogin", false, "User not found");
                return TypedResults.Problem("Invalid login attempt", statusCode: StatusCodes.Status401Unauthorized);
            }

            var result = await _userManager.RedeemTwoFactorRecoveryCodeAsync(user, request.RecoveryCode);
            if (!result.Succeeded)
            {
                _logger.LogWarning("Invalid recovery code for {Email}", request.Email);
                await _securityAuditService.LogSecurityEventAsync(await _userManager.GetUserIdAsync(user), request.Email, "RecoveryCodeLogin", false, "Invalid recovery code");
                return TypedResults.Problem("Invalid recovery code", statusCode: StatusCodes.Status401Unauthorized);
            }

            var accessTokenResponse = await _tokenService.GenerateTokenResponseAsync(user);
            
            // Create user session
            var sessionId = _tokenService.GenerateSecureSessionId();
            await _sessionService.CreateUserSessionAsync(user, sessionId);
            
            await _securityAuditService.LogSecurityEventAsync(await _userManager.GetUserIdAsync(user), request.Email, "RecoveryCodeLogin", true, null);
            
            _logger.LogInformation("Recovery code login successful for {Email}", request.Email);
            return TypedResults.Ok(accessTokenResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during recovery code login for {Email}", request.Email);
            return TypedResults.Problem("An unexpected error occurred during login", 
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <inheritdoc />
    public async Task<Results<Ok<AccessTokenResponse>, UnauthorizedHttpResult>> RefreshTokenAsync(RefreshRequest request)
    {
        try
        {
            _logger.LogInformation("Token refresh attempt");

            // Try to get refresh token from request body, fallback to cookie if empty
            string? refreshTokenValue = request.RefreshToken;
            if (string.IsNullOrEmpty(refreshTokenValue))
            {
                var httpContext = _sessionService.GetCurrentHttpContext();
                refreshTokenValue = httpContext?.Request.Cookies["refresh_token"];
                
                if (string.IsNullOrEmpty(refreshTokenValue))
                {
                    _logger.LogWarning("No refresh token provided in request or cookie");
                    await _securityAuditService.LogSecurityEventAsync(null, "Unknown", "TokenRefresh", false, "No refresh token provided");
                    return TypedResults.Unauthorized();
                }
            }

            var user = await _tokenService.ValidateRefreshTokenAsync(refreshTokenValue);
            if (user == null)
            {
                _logger.LogWarning("Invalid or expired refresh token");
                await _securityAuditService.LogSecurityEventAsync(null, "Unknown", "TokenRefresh", false, "Invalid refresh token");
                return TypedResults.Unauthorized();
            }

            _logger.LogDebug("Refresh token validation successful, generating new access token");
            var accessTokenResponse = await _tokenService.GenerateTokenResponseAsync(user);
            
            var userEmail = await _userManager.GetEmailAsync(user);
            await _securityAuditService.LogSecurityEventAsync(await _userManager.GetUserIdAsync(user), userEmail ?? "Unknown", "TokenRefresh", true, null);
            
            _logger.LogInformation("Token refresh successful for user {UserId}", await _userManager.GetUserIdAsync(user));
            return TypedResults.Ok(accessTokenResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during token refresh");
            return TypedResults.Unauthorized();
        }
    }

    /// <inheritdoc />
    public async Task<Results<Ok, UnauthorizedHttpResult>> LogoutAsync()
    {
        try
        {
            var httpContext = _sessionService.GetCurrentHttpContext();
            var claimsPrincipal = httpContext?.User;
            
            if (claimsPrincipal != null)
            {
                var user = await _userManager.GetUserAsync(claimsPrincipal);
                
                if (user != null)
                {
                    var userEmail = await _userManager.GetEmailAsync(user);
                    var userId = await _userManager.GetUserIdAsync(user);
                    
                    // Get session ID from claims if available
                    var sessionId = claimsPrincipal.FindFirst("session_id")?.Value;
                    
                    // Terminate the current session if we have a session ID
                    if (!string.IsNullOrEmpty(sessionId))
                    {
                        await _sessionService.TerminateUserSessionAsync(userId, sessionId, "Manual logout");
                    }
                    
                    await _securityAuditService.LogSecurityEventAsync(userId, userEmail ?? "Unknown", "Logout", true, null);
                    
                    _logger.LogInformation("User {Email} signed out successfully", userEmail);
                }
            }
            
            await _signInManager.SignOutAsync();
            return TypedResults.Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout");
            // Still attempt to sign out even if session cleanup fails
            await _signInManager.SignOutAsync();
            return TypedResults.Ok();
        }
    }
}