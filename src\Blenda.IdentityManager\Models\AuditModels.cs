namespace Blenda.IdentityManager.Models;

/// <summary>
/// Server-side audit entry for security and compliance logging
/// </summary>
public class ApiAuditEntry
{
    public int Id { get; set; }
    public string Method { get; set; } = string.Empty;
    public string Path { get; set; } = string.Empty;
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string? UserId { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public int StatusCode { get; set; }
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public long ResponseTimeMs { get; set; }
    public string? RequestBody { get; set; }
    public int? RequestSize { get; set; }
    public string? ResponseContentType { get; set; }
    public Dictionary<string, string>? Headers { get; set; }
}

/// <summary>
/// Security-specific audit entry for authentication and authorization events
/// </summary>
public class SecurityAuditEntry
{
    public int Id { get; set; }
    public string EventType { get; set; } = string.Empty; // Authentication, Authorization, PasswordReset, etc.
    public string UserId { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public Dictionary<string, object>? Details { get; set; }
    public string? Reason { get; set; }
    public string? SessionId { get; set; }
    public string? Resource { get; set; }
    public string? Action { get; set; }
}
