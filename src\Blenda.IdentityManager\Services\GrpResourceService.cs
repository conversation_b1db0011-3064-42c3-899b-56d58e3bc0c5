using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Options;
using IdentityManager.Models;
using IdentityManager.Services.Core;
using IdentityManager.Services.Interfaces;
using Blenda.IdentityManager.Services;
using Blenda.IdentityManager.Models;

namespace IdentityManager.Services;

/// <summary>
/// Service for synchronizing user resources with GRP (General Resource Provider) system
/// Implements enterprise-grade integration with comprehensive error handling and audit logging
/// </summary>
public class GrpResourceService : IGrpResourceService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<GrpResourceService> _logger;
    private readonly GrpApiOptions _options;
    private readonly IAuditService _auditService;
    private readonly ITenantService _tenantService;
    private readonly JsonSerializerOptions _jsonOptions;

    public GrpResourceService(
        HttpClient httpClient,
        ILogger<GrpResourceService> logger,
        IOptions<GrpApiOptions> options,
        IAuditService auditService,
        ITenantService tenantService)
    {
        _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _auditService = auditService ?? throw new ArgumentNullException(nameof(auditService));
        _tenantService = tenantService ?? throw new ArgumentNullException(nameof(tenantService));

        // Configure JSON serialization options for GRP API compatibility
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false,
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        };

        // Configure HTTP client with GRP API settings
        ConfigureHttpClient();
    }

    /// <inheritdoc />
    public async Task<GrpSyncResult> SynchronizeUserResourceAsync(ApplicationUser user)
    {
        var operationId = Guid.NewGuid().ToString();
        var startTime = DateTime.UtcNow;

        try
        {
            _logger.LogInformation("Starting GRP resource synchronization for user {UserId} (Operation: {OperationId})", 
                user.Id, operationId);

            // Audit: Log GRP sync initiation
            await _auditService.LogSecurityEventAsync(new SecurityAuditEntry
            {
                UserId = user.Id,
                EventType = "GRP_SYNC_INITIATED",
                Success = true,
                UserAgent = "GrpResourceService",
                Details = new Dictionary<string, object> { { "OperationId", operationId }, { "UserEmail", user.Email } }
            });

            // Create resource from user data
            var resource = await CreateResourceFromUserAsync(user);

            // Submit resource to GRP API
            var submissionResult = await SubmitResourceReplicationAsync(resource);

            if (!submissionResult.Success)
            {
                _logger.LogWarning("GRP resource submission failed for user {UserId}: {Error}", 
                    user.Id, submissionResult.ErrorMessage);

                // Audit: Log GRP sync failure
                await _auditService.LogSecurityEventAsync(new SecurityAuditEntry
                {
                    UserId = user.Id,
                    EventType = "GRP_SYNC_FAILED",
                    Success = false,
                    UserAgent = "GrpResourceService",
                    Details = new Dictionary<string, object> { 
                        { "OperationId", operationId }, 
                        { "Error", submissionResult.ErrorMessage ?? "Unknown error" },
                        { "ElapsedMs", (DateTime.UtcNow - startTime).TotalMilliseconds }
                    }
                });

                return new GrpSyncResult
                {
                    Success = false,
                    ErrorMessage = submissionResult.ErrorMessage,
                    RequestId = submissionResult.RequestId,
                    OperationId = operationId,
                    SyncTimestamp = startTime,
                    ProcessingTimeMs = (int)(DateTime.UtcNow - startTime).TotalMilliseconds
                };
            }

            // Generate follow-up TODO activities for user
            var todoActivities = await GenerateTodoActivitiesAsync(user, submissionResult.RequestId!);

            var syncResult = new GrpSyncResult
            {
                Success = true,
                RequestId = submissionResult.RequestId,
                OperationId = operationId,
                SyncTimestamp = startTime,
                ProcessingTimeMs = (int)(DateTime.UtcNow - startTime).TotalMilliseconds,
                TodoActivities = todoActivities,
                ResourceId = resource.Id.ToString(),
                TenantId = resource.TenantId
            };

            // Audit: Log successful GRP sync
            await _auditService.LogSecurityEventAsync(new SecurityAuditEntry
            {
                UserId = user.Id,
                EventType = "GRP_RESOURCE_SYNC",
                Success = true,
                UserAgent = "GrpResourceService",
                Details = new Dictionary<string, object> { 
                    { "OperationId", operationId },
                    { "RequestId", submissionResult.RequestId ?? "Unknown" },
                    { "ResourceId", resource.Id },
                    { "TenantId", resource.TenantId },
                    { "ElapsedMs", 0 }, // Will be updated with actual processing time
                    { "TodoActivitiesCount", todoActivities.Count }
                }
            });

            _logger.LogInformation("GRP resource synchronization completed successfully for user {UserId} " +
                "(Operation: {OperationId}, Request: {RequestId}, Duration: {Duration}ms)", 
                user.Id, operationId, submissionResult.RequestId, syncResult.ProcessingTimeMs);

            return syncResult;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP request failed during GRP resource synchronization for user {UserId} (Operation: {OperationId})", 
                user.Id, operationId);

            await LogSyncException(user.Id, operationId, "HTTP_REQUEST_FAILED", ex, startTime);
            return CreateErrorResult(operationId, startTime, "GRP API communication failed", ex.Message);
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            _logger.LogError(ex, "Timeout during GRP resource synchronization for user {UserId} (Operation: {OperationId})", 
                user.Id, operationId);

            await LogSyncException(user.Id, operationId, "TIMEOUT", ex, startTime);
            return CreateErrorResult(operationId, startTime, "GRP API request timeout", "The request to GRP API timed out");
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "JSON serialization error during GRP resource synchronization for user {UserId} (Operation: {OperationId})", 
                user.Id, operationId);

            await LogSyncException(user.Id, operationId, "SERIALIZATION_ERROR", ex, startTime);
            return CreateErrorResult(operationId, startTime, "Data serialization failed", ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during GRP resource synchronization for user {UserId} (Operation: {OperationId})", 
                user.Id, operationId);

            await LogSyncException(user.Id, operationId, "UNEXPECTED_ERROR", ex, startTime);
            return CreateErrorResult(operationId, startTime, "Unexpected error occurred", "An internal error occurred during synchronization");
        }
    }

    /// <inheritdoc />
    public async Task<AnonymousRequestResponse> SubmitResourceReplicationAsync(ResourceDto resource)
    {
        try
        {
            _logger.LogDebug("Submitting resource replication request for resource {ResourceId} to tenant {TenantId}", 
                resource.Id, resource.TenantId);

            // Create anonymous request payload
            var requestPayload = new SubmitAnonymousRequestDto
            {
                TenantId = resource.TenantId,
                Resource = resource,
                RequestedAt = DateTime.UtcNow,
                Priority = 3, // Normal priority
                Source = "IdentityManager",
                Metadata = new Dictionary<string, object>
                {
                    ["userRegistration"] = true,
                    ["version"] = "1.0",
                    ["environment"] = _options.Environment
                }
            };

            // Serialize request payload
            var jsonPayload = JsonSerializer.Serialize(requestPayload, _jsonOptions);
            var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

            // Build request URL
            var requestUrl = $"{_options.BaseUrl.TrimEnd('/')}/{resource.TenantId}/resources";

            _logger.LogDebug("Sending POST request to GRP API: {Url}", requestUrl);

            // Send request to GRP API
            var response = await _httpClient.PostAsync(requestUrl, content);

            // Handle response
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var submissionResponse = JsonSerializer.Deserialize<AnonymousRequestSubmissionResponseDto>(responseContent, _jsonOptions);
                
                _logger.LogInformation("GRP resource replication submitted successfully: {RequestId}", 
                    submissionResponse?.RequestId);

                return new AnonymousRequestResponse
                {
                    Success = true,
                    RequestId = submissionResponse?.RequestId,
                    Status = submissionResponse?.Status ?? "Submitted",
                    Message = "Resource replication request submitted successfully"
                };
            }
            else
            {
                _logger.LogWarning("GRP API returned error status {StatusCode}: {Response}", 
                    response.StatusCode, responseContent);

                return new AnonymousRequestResponse
                {
                    Success = false,
                    ErrorMessage = $"GRP API error ({response.StatusCode}): {responseContent}",
                    Status = "Failed"
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting resource replication request for resource {ResourceId}", resource.Id);

            return new AnonymousRequestResponse
            {
                Success = false,
                ErrorMessage = $"Failed to submit replication request: {ex.Message}",
                Status = "Error"
            };
        }
    }

    /// <inheritdoc />
    public async Task<List<TodoActivity>> GenerateTodoActivitiesAsync(ApplicationUser user, string requestId)
    {
        try
        {
            _logger.LogDebug("Generating TODO activities for user {UserId} with GRP request {RequestId}", 
                user.Id, requestId);

            var activities = new List<TodoActivity>();

            // Activity 1: Welcome and onboarding
            activities.Add(new TodoActivity
            {
                Id = Guid.NewGuid(),
                Title = "Complete Welcome Process",
                Description = $"Welcome to the platform! Your account has been created and synchronized with our resource management system (Request: {requestId}). Please review your profile and complete any missing information.",
                Priority = ActivityPriority.High,
                Status = ActivityStatus.Pending,
                AssignedUserId = user.Id,
                CreatedAt = DateTime.UtcNow,
                DueDate = DateTime.UtcNow.AddDays(7),
                Category = "Onboarding",
                Metadata = new Dictionary<string, object>
                {
                    ["grpRequestId"] = requestId,
                    ["userEmail"] = user.Email ?? string.Empty,
                    ["activityType"] = "welcome"
                }
            });

            // Activity 2: Profile verification
            activities.Add(new TodoActivity
            {
                Id = Guid.NewGuid(),
                Title = "Verify Profile Information",
                Description = "Please verify that all your profile information is accurate and up-to-date. This ensures proper resource allocation and access permissions.",
                Priority = ActivityPriority.Medium,
                Status = ActivityStatus.Pending,
                AssignedUserId = user.Id,
                CreatedAt = DateTime.UtcNow,
                DueDate = DateTime.UtcNow.AddDays(14),
                Category = "Verification",
                Metadata = new Dictionary<string, object>
                {
                    ["grpRequestId"] = requestId,
                    ["activityType"] = "verification"
                }
            });

            // Activity 3: Security setup
            activities.Add(new TodoActivity
            {
                Id = Guid.NewGuid(),
                Title = "Enable Two-Factor Authentication",
                Description = "Enhance your account security by enabling two-factor authentication. This is recommended for all users to protect sensitive resources.",
                Priority = ActivityPriority.Medium,
                Status = ActivityStatus.Pending,
                AssignedUserId = user.Id,
                CreatedAt = DateTime.UtcNow,
                DueDate = DateTime.UtcNow.AddDays(30),
                Category = "Security",
                Metadata = new Dictionary<string, object>
                {
                    ["grpRequestId"] = requestId,
                    ["activityType"] = "security"
                }
            });

            _logger.LogInformation("Generated {Count} TODO activities for user {UserId}", activities.Count, user.Id);

            return activities;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating TODO activities for user {UserId}", user.Id);
            return new List<TodoActivity>(); // Return empty list on error
        }
    }

    /// <summary>
    /// Creates a ResourceDto from an ApplicationUser
    /// </summary>
    private async Task<ResourceDto> CreateResourceFromUserAsync(ApplicationUser user)
    {
        var tenantId = await _tenantService.GetDefaultTenantIdAsync();
        
        return new ResourceDto
        {
            Id = Guid.NewGuid(),
            TenantId = Guid.Parse(tenantId),
            Type = "User",
            Name = user.UserName ?? user.Email ?? "Unknown",
            Description = $"User resource for {user.Email}",
            Properties = new Dictionary<string, object>
            {
                ["userId"] = user.Id,
                ["email"] = user.Email ?? string.Empty,
                ["userName"] = user.UserName ?? string.Empty,
                ["emailConfirmed"] = user.EmailConfirmed,
                ["createdAt"] = DateTime.UtcNow.ToString("O"),
                ["source"] = "IdentityManager"
            },
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            Version = "1.0.0",
            IsActive = true
        };
    }

    /// <summary>
    /// Configures the HTTP client with GRP API settings
    /// </summary>
    private void ConfigureHttpClient()
    {
        _httpClient.BaseAddress = new Uri(_options.BaseUrl);
        _httpClient.Timeout = TimeSpan.FromSeconds(_options.TimeoutSeconds);

        // Add required headers
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "BlendaIdentityManager/1.0");
        _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");

        if (!string.IsNullOrEmpty(_options.ApiKey))
        {
            _httpClient.DefaultRequestHeaders.Add("X-API-Key", _options.ApiKey);
        }

        _logger.LogDebug("HTTP client configured for GRP API: {BaseUrl} (Timeout: {Timeout}s)", 
            _options.BaseUrl, _options.TimeoutSeconds);
    }

    /// <summary>
    /// Logs sync exceptions with comprehensive audit information
    /// </summary>
    private async Task LogSyncException(string userId, string operationId, string errorType, Exception ex, DateTime startTime)
    {
        try
        {
            await _auditService.LogSecurityEventAsync(new SecurityAuditEntry
            {
                UserId = userId,
                EventType = "GRP_SYNC_EXCEPTION",
                Success = false,
                UserAgent = "GrpResourceService",
                Details = new Dictionary<string, object> { 
                    { "OperationId", operationId },
                    { "ErrorType", errorType },
                    { "ExceptionType", ex.GetType().Name },
                    { "ExceptionMessage", ex.Message },
                    { "ElapsedMs", (DateTime.UtcNow - startTime).TotalMilliseconds }
                }
            });
        }
        catch (Exception auditEx)
        {
            _logger.LogError(auditEx, "Failed to log sync exception for user {UserId} (Operation: {OperationId})", 
                userId, operationId);
        }
    }

    /// <summary>
    /// Creates a standardized error result
    /// </summary>
    private static GrpSyncResult CreateErrorResult(string operationId, DateTime startTime, string errorMessage, string? details = null)
    {
        return new GrpSyncResult
        {
            Success = false,
            ErrorMessage = errorMessage,
            Details = details,
            OperationId = operationId,
            SyncTimestamp = startTime,
            ProcessingTimeMs = (int)(DateTime.UtcNow - startTime).TotalMilliseconds
        };
    }
}