using Microsoft.AspNetCore.Identity;
using IdentityManager.Models;
using IdentityManager.Services.Core;
using Microsoft.AspNetCore.Http.HttpResults;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.WebUtilities;
using System.Text;
using Microsoft.AspNetCore.Identity.Data;
using IdentityManager.Services.Validation;

using RegisterRequest = Microsoft.AspNetCore.Identity.Data.RegisterRequest;
using ValidationProblem = Microsoft.AspNetCore.Http.HttpResults.ValidationProblem;

namespace IdentityManager.Services.Handlers;

/// <summary>
/// Interface for user registration operations
/// </summary>
public interface IUserRegistrationHandler<TUser> where TUser : class
{
    /// <summary>
    /// Registers a new user with the specified role
    /// </summary>
    Task<Results<Ok, ValidationProblem>> RegisterUserAsync(RegisterRequest registration, string role, Func<string, bool>? emailValidator = null);
}

/// <summary>
/// Enhanced user registration service with comprehensive validation and features
/// </summary>
public class UserRegistrationService<TUser> : IUserRegistrationService<TUser> where TUser : class, new()
{
    private readonly UserManager<TUser> _userManager;
    private readonly IEmailSender<TUser> _emailSender;
    private readonly IConfiguration _configuration;
    private readonly ISecurityAuditService _securityAuditService;
    private readonly ILogger<UserRegistrationService<TUser>> _logger;
    private static readonly EmailAddressAttribute _emailAddressAttribute = new();

    public UserRegistrationService(
        UserManager<TUser> userManager,
        IEmailSender<TUser> emailSender,
        IConfiguration configuration,
        ISecurityAuditService securityAuditService,
        ILogger<UserRegistrationService<TUser>> logger)
    {
        _userManager = userManager;
        _emailSender = emailSender;
        _configuration = configuration;
        _securityAuditService = securityAuditService;
        _logger = logger;
    }

    /// <inheritdoc />
    public async Task<RegistrationResult> RegisterUserAsync(
        RegisterRequest registration, 
        string role, 
        Func<string, bool>? emailValidator = null,
        bool requireEmailConfirmation = true)
    {
        try
        {
            _logger.LogInformation("Starting user registration for email: {Email} with role: {Role}", 
                registration.Email, role);

            // Validate registration data
            var validationResult = await ValidateRegistrationAsync(registration);
            if (!validationResult.IsValid)
            {
                await _securityAuditService.LogSecurityEventAsync(null, registration.Email, "Registration", false, "Validation failed");
                return new RegistrationResult
                {
                    Success = false,
                    Errors = validationResult.Errors.Select(e => new IdentityError { Code = e.Key, Description = e.Value }).ToList()
                };
            }

            // Apply custom email validation if provided
            if (emailValidator != null && !emailValidator(registration.Email))
            {
                _logger.LogWarning("Email failed custom validation: {Email}", registration.Email);
                await _securityAuditService.LogSecurityEventAsync(null, registration.Email, "Registration", false, "Custom email validation failed");
                return new RegistrationResult
                {
                    Success = false,
                    Errors = new List<IdentityError> { new() { Code = "InvalidEmail", Description = "Email does not meet the required criteria." } }
                };
            }

            // Check if user already exists
            var existingUser = await _userManager.FindByEmailAsync(registration.Email);
            if (existingUser != null)
            {
                _logger.LogWarning("Registration attempt for existing email: {Email}", registration.Email);
                await _securityAuditService.LogSecurityEventAsync(null, registration.Email, "Registration", false, "User already exists");
                return new RegistrationResult
                {
                    Success = false,
                    Errors = new List<IdentityError> { new() { Code = "DuplicateEmail", Description = "User with this email already exists." } }
                };
            }

            // Create new user
            var user = new TUser();
            await _userManager.SetUserNameAsync(user, registration.Email);
            await _userManager.SetEmailAsync(user, registration.Email);
            
            var result = await _userManager.CreateAsync(user, registration.Password);
            if (!result.Succeeded)
            {
                _logger.LogWarning("User creation failed for {Email}: {Errors}", 
                    registration.Email, string.Join(", ", result.Errors.Select(e => e.Description)));
                await _securityAuditService.LogSecurityEventAsync(null, registration.Email, "Registration", false, "User creation failed");
                return new RegistrationResult
                {
                    Success = false,
                    Errors = result.Errors.ToList()
                };
            }

            var userId = await _userManager.GetUserIdAsync(user);

            // Assign role
            var roleResult = await _userManager.AddToRoleAsync(user, role);
            if (!roleResult.Succeeded)
            {
                _logger.LogError("Role assignment failed for user {Email}, role {Role}: {Errors}", 
                    registration.Email, role, string.Join(", ", roleResult.Errors.Select(e => e.Description)));
                
                // Log error but don't fail registration completely
                await _securityAuditService.LogSecurityEventAsync(userId, registration.Email, "Registration", false, "Role assignment failed");
                return new RegistrationResult
                {
                    Success = false,
                    Errors = roleResult.Errors.ToList()
                };
            }

            // Send confirmation email if required
            bool emailConfirmationSent = false;
            if (requireEmailConfirmation)
            {
                try
                {
                    await SendConfirmationEmailAsync(user);
                    emailConfirmationSent = true;
                    _logger.LogInformation("Confirmation email sent for user {Email}", registration.Email);
                }
                catch (Exception emailEx)
                {
                    _logger.LogError(emailEx, "Failed to send confirmation email for user {Email}", registration.Email);
                    // Don't fail registration if email sending fails
                }
            }

            await _securityAuditService.LogSecurityEventAsync(userId, registration.Email, "Registration", true, null);
            
            _logger.LogInformation("User registration completed successfully for {Email} with role {Role}", 
                registration.Email, role);
            
            return new RegistrationResult
            {
                Success = true,
                UserId = userId,
                EmailConfirmationSent = emailConfirmationSent
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during user registration for {Email}", registration.Email);
            await _securityAuditService.LogSecurityEventAsync(null, registration.Email, "Registration", false, "Unexpected error");
            return new RegistrationResult
            {
                Success = false,
                Errors = new List<IdentityError> { new() { Code = "RegistrationError", Description = "An unexpected error occurred during registration." } }
            };
        }
    }

    /// <inheritdoc />
    public async Task SendConfirmationEmailAsync(TUser user)
    {
        try
        {
            var code = await _userManager.GenerateEmailConfirmationTokenAsync(user);
            code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
            var userId = await _userManager.GetUserIdAsync(user);

            var frontendUrl = _configuration["ApiSettings:PublicFacingService"] ?? "https://blenda.lat";
            var confirmEmailUrl = $"{frontendUrl}/account/confirm-email?userId={userId}&code={code}";

            var userEmail = await _userManager.GetEmailAsync(user);
            await _emailSender.SendConfirmationLinkAsync(user, userEmail!, confirmEmailUrl);

            _logger.LogInformation("Confirmation email sent to {Email}", userEmail);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send confirmation email for user {UserId}", await _userManager.GetUserIdAsync(user));
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<UserValidationResult> ValidateRegistrationAsync(RegisterRequest registration)
    {
        var result = new UserValidationResult { IsValid = true };

        // Validate email format
        if (string.IsNullOrEmpty(registration.Email) || !_emailAddressAttribute.IsValid(registration.Email))
        {
            result.IsValid = false;
            result.Errors["Email"] = "Invalid email format.";
        }

        // Validate password strength (basic validation - ASP.NET Identity will do more comprehensive validation)
        if (string.IsNullOrEmpty(registration.Password))
        {
            result.IsValid = false;
            result.Errors["Password"] = "Password is required.";
        }
        else if (registration.Password.Length < 8)
        {
            result.IsValid = false;
            result.Errors["Password"] = "Password must be at least 8 characters long.";
        }

        // Additional business rule validations can be added here
        // For example: email domain restrictions, password complexity rules, etc.

        return result;
    }
}

/// <summary>
/// Enhanced user registration handler with comprehensive validation, email confirmation, and GDPR consent recording
/// </summary>
public class UserRegistrationHandler<TUser> : IUserRegistrationHandler<TUser> where TUser : class, new()
{
    private readonly IUserRegistrationService<TUser> _registrationService;
    private readonly ILogger<UserRegistrationHandler<TUser>> _logger;

    public UserRegistrationHandler(
        IUserRegistrationService<TUser> registrationService,
        ILogger<UserRegistrationHandler<TUser>> logger)
    {
        _registrationService = registrationService;
        _logger = logger;
    }

    /// <inheritdoc />
    public async Task<Results<Ok, ValidationProblem>> RegisterUserAsync(
        RegisterRequest registration, 
        string role, 
        Func<string, bool>? emailValidator = null)
    {
        try
        {
            var result = await _registrationService.RegisterUserAsync(registration, role, emailValidator);
            
            if (result.Success)
            {
                return TypedResults.Ok();
            }
            else
            {
                return CreateValidationProblem(result.Errors);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error in registration handler for {Email}", registration.Email);
            return CreateValidationProblem("RegistrationError", "An unexpected error occurred during registration.");
        }
    }

    private static ValidationProblem CreateValidationProblem(IList<IdentityError> errors)
    {
        var identityResult = IdentityResult.Failed(errors.ToArray());
        return IdentityValidation.CreateValidationProblem(identityResult);
    }

    private static ValidationProblem CreateValidationProblem(string field, string error)
    {
        return IdentityValidation.CreateValidationProblem(field, error);
    }
}