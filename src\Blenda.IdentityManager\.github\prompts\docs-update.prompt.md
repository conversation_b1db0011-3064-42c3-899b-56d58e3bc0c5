---
mode: agent
tools: ['codebase', 'editFiles', 'read_file', 'replace_string_in_file', 'semantic_search']
description: "Update documentation following 7-section Documentation Standards"
---

# Documentation Update Prompt

Update documentation files to comply with the Blenda Core Identity Documentation Standards requiring all documentation to follow a standardized 7-section structure.

## Documentation Standards Structure

Each documentation file must contain these 7 sections in order:

### 1. Overview
- Purpose and scope of the document
- Key features and benefits
- Target audience
- Prerequisites

### 2. Implementation Details
- Technical specifications
- Architecture components
- Code structure and patterns
- Dependencies and integrations

### 3. Configuration
- Environment setup requirements
- Configuration files and settings
- Environment variables
- Database connections

### 4. Examples
- Code samples and snippets
- API usage examples
- Practical demonstrations
- Step-by-step tutorials

### 5. Troubleshooting
- Common issues and solutions
- Error messages and resolutions
- Debugging techniques
- FAQ section

### 6. Security Considerations
- Security controls and best practices
- Access control requirements
- Data protection measures
- Compliance requirements

### 7. Support
- Additional resources
- Contact information
- Related documentation
- Community links

## Documentation Types

Apply appropriate naming conventions:
- README files: `{TOPIC}_README.md`
- Guide files: `{PURPOSE}_GUIDE.md`
- Report files: `{TYPE}_REPORT.md`
- Technical docs: `{action}-{system}.md`

## Quality Standards

Ensure documentation includes:
- Clear, concise language
- Proper markdown formatting
- Code examples with syntax highlighting
- Appropriate headings and structure
- Cross-references to related documents

Focus on creating comprehensive, enterprise-grade documentation that serves both technical and business audiences while maintaining consistency across all documentation files.
