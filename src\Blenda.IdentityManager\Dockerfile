# Etapa de Build con .NET 9 SDK
FROM mcr.microsoft.com/dotnet/sdk:9.0.302 AS build
ARG PACKAGE_SOURCE_PATH
ARG PACKAGE_SOURCE_NAME
ARG PACKAGE_SOURCE_USERNAME
ARG PACKAGE_SOURCE_PASSWORD
WORKDIR /App
RUN ls -la 
COPY . ./
RUN dotnet nuget add source ${PACKAGE_SOURCE_PATH} \
    -n ${PACKAGE_SOURCE_NAME} \
    -u ${PACKAGE_SOURCE_USERNAME} \
    -p ${PACKAGE_SOURCE_PASSWORD} \
    --store-password-in-clear-text
RUN dotnet restore
RUN ls -la src/ && \
    dotnet publish -c Release -o out --no-restore \
    && ls -la /App/out  # Depuración: listar archivos generados

# Etapa de Runtime con .NET 9 ASP.NET Core
FROM mcr.microsoft.com/dotnet/aspnet:9.0.7 AS runtime
WORKDIR /App
COPY --from=build /App/out .
RUN ls -la /App  # Depuración: verificar archivos copiados
EXPOSE 8080

ENV DOTNET_ENVIRONMENT=Production
ENV DOTNET_RUNNING_IN_CONTAINER=true
ENV ASPNETCORE_HTTP_PORTS=8080
ENV ASPNETCORE_URLS=http://+:8080

RUN useradd -m -s /bin/bash appuser && \
    chown -R appuser:appuser /App
USER appuser
ENTRYPOINT ["dotnet", "SecurityApi.dll"]

















# # Build stage
# FROM mcr.microsoft.com/dotnet/sdk:9.0.302 AS build
# ARG PACKAGE_SOURCE_PATH
# ARG PACKAGE_SOURCE_NAME
# ARG PACKAGE_SOURCE_USERNAME
# ARG PACKAGE_SOURCE_PASSWORD
# WORKDIR /App
# COPY . ./
# RUN dotnet nuget add source https://f.feedz.io/packhub-private-server/blenda-grp/nuget/index.json \
#     -n packhub-private-server \
#     -u <EMAIL> \
#     -p T-aOo8l9k0XzbIHu5MN6fWzlSWzAxLKK8NEGE \
#     --store-password-in-clear-text
# RUN dotnet restore
# RUN dotnet publish -c Release -o out

# # Runtime stage
# FROM mcr.microsoft.com/dotnet/aspnet:9.0.7 AS runtime
# WORKDIR /App
# COPY --from=build /App/out .
# ENTRYPOINT ["dotnet", "SecurityApi.dll"]
