---
mode: agent
tools: ['codebase', 'editFiles', 'runCommands', 'test_search', 'runTests']
description: "Generate comprehensive test cases for identity and authentication features"
---

# Test Coverage Prompt

Generate comprehensive test coverage for the Blenda Core Identity & Access Management System focusing on security-critical functionality and enterprise requirements.

## Test Categories to Cover

### 1. Authentication Tests
- User registration with email validation
- Login with multiple authentication factors
- JWT token generation and validation
- Refresh token security and rotation
- Password reset workflows
- Email confirmation processes

### 2. Multi-Factor Authentication (2FA) Tests
- TOTP setup and verification
- QR code generation and validation
- Recovery code generation and usage
- 2FA disable/enable workflows
- Authenticator app integration

### 3. Authorization Tests
- Role-based access control (RBAC)
- Permission enforcement across all endpoints
- JWT token claim validation
- Cross-tenant isolation
- Administrative privilege escalation prevention

### 4. Security Tests
- SQL injection prevention
- XSS protection validation
- CSRF token validation
- Rate limiting enforcement
- Input validation and sanitization
- Error handling without information disclosure

### 5. Database Integration Tests
- Oracle connection string security
- Entity Framework Core operations
- Migration testing
- Connection failure handling
- Data encryption validation

### 6. Email Service Tests
- SMTP configuration testing
- Template validation and security
- Email delivery confirmation
- Failed delivery handling
- HTML injection prevention

## Test Implementation Standards

### Unit Tests
- Isolated component testing
- Mock external dependencies
- Security-focused assertions
- Edge case coverage

### Integration Tests
- End-to-end authentication flows
- Database integration validation
- Email service integration
- API endpoint security testing

### Security Tests
- Penetration testing scenarios
- Vulnerability assessment
- Compliance validation
- Performance under attack conditions

## Test Framework Requirements

Use appropriate testing frameworks:
- **xUnit**: Primary testing framework
- **Moq**: Mocking and dependency injection
- **Microsoft.AspNetCore.Mvc.Testing**: Integration testing
- **FluentAssertions**: Enhanced assertions

## Coverage Goals

Achieve minimum coverage targets:
- **Security-critical code**: 100% coverage
- **Authentication flows**: 95% coverage
- **Business logic**: 90% coverage
- **Overall codebase**: 85% coverage

Focus on testing security vulnerabilities, edge cases, and enterprise compliance requirements while maintaining fast test execution and reliable results.
